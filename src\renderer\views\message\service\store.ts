import { computed, ref, unref, toRaw, reactive } from 'vue';
import { defineStore } from 'pinia';
import { getImCardIds, getOpenid, cfgCommon, getShouldRinging } from '@renderer/utils/auth';
import { safeParseJson } from '@renderer/utils/assist';
import { GetAdvancedHistoryMsgParams } from '@rk/im-sdk/dist/types/params';
import { IRelationGroup, GroupTabKey, GroupTabItem } from '@renderer/api/im/model/relation';
import {
  ReceiptAttachedInfo,
} from '@rk/im-sdk/dist/types/entity';
import _ from 'lodash';
import { MessagePlugin } from 'tdesign-vue-next';
import { logHandler } from '@renderer/log';
import { msgEmit } from '@renderer/views/message/service/msgEmit';
import { getAssistantInfoApi } from '@renderer/api/im/api';
import router from '@/router';

import { updateConversationRelation } from './relationUpdate';

import { useNewEditorStore } from './editor';
import { useNetStore } from './imStatus';
import { getPairGroupConfig } from '@renderer/api/account';

import {
  getAllSessions,
  loadPrivateSession,
  getPrivateSession,

  getGroupSession,
  getGroupCardSession,
  parseGroupRelation,
  getGroupMember,

  getSessionMember,
  reqGroupLabels,

} from './request';

import {
  getCardMainPeer,
  getMappedMembers,
  getSessionLocalIdByCards,
  getMsgToStore,
  msgToUseChat,
  SentStatusMap,
  getAssistantsSessions,
} from './utils';

import { shouldCountMention, getUnreadInfo, isClientMsg, latestMsgData, assistantMap } from './msgUtils';
import MsgWrapper from './message';
import {
  queryAllConversations,
  queryRemoveSessions,
  queryConversationByIds,
  insertConversations,
  updateDbConversationFromServer,
  onDbConversationFromServer,
  onDbConversationMessageUpdate,
  onDbConversationSetting,
  onDbConversationSceneRedDot,

  onDbConversationDelete,
  insertOrUpdateGroup,
  onDbGroupCardDelete,
  onDbConversationUpdate,
  getGroupLabels,

} from './dbUtils';

import { cardIdType, cardGroupType } from '@/views/identitycard/data';
import { useGroupInviteStore } from './groupInvite';
import { loadAcountCards } from './accountUtils';
import { useImToolStore } from '../tool/service/tool';
import { onApplaunchIsServerFinish } from './handler';

import { loadOpenIMHistory, firstLoadOpenIMHistory } from './msgHistory';
// import { loadDbFirstPage, loadDbMorePage } from './msgLoader';
import RingTone from '@/assets/ring.mp3';
import OrderRingTone from '@/assets/orderRing.mp3';
import { getConversationIDBySessionType, getOpenIMConversationList, getOneConversationDetail, resetUnreadMsgCount, findMsgList, updateMessage, handleSyncedAllSeqsConversation, getCurrentServerTime, deleteConversationAllMessages, sendReadReceiptMessage } from './ipcIMBridge';
import LynkerSDK from '@renderer/_jssdk';

import { mockMessageData } from './mockData';

const { ipcRenderer } = LynkerSDK;


export const useMessageStore = defineStore('message', () => {

  // 所有会话的成员，为map形式，方式查询
  const allMembers = ref<Map<string, Map<string, ConversationMemberToSave>>>(new Map());
  // 所有群组
  const allGroups = ref<GroupToSave[]>([]);
  // 群成员身份标签
  const groupLabels = ref<Map<string, Map<string, string>>>(new Map());

  // 获取当前用户所有组织的专属名称
  const exclusiveNames = ref<Map<string, string>>(new Map());

  // 当前会话列表
  const sessionList = ref<ConversationToSave[]>([]);
  // 是否需要滚动到底部
  const shouldScroll = ref(false);
  /**
   * 会话滚动的条数 默认20条
   */
  const scrollLength = ref(20);
  const scrollPx = ref(0);
  const scrollPosition = ref(0);
  // 当前正在聊天的会话
  const chatingSession = ref<ConversationToSave>(null);
  // 是否是我发送的数字名片
  const isMySendVcard = ref(false);

  // 暂存会话选中未读数，供界面展示
  const unredInfo = reactive({ time: 0, count: 0, mention: 0 });

  // 咨询信息弹窗
  const consultInfoPop = reactive({ value: {} });
  // 正在创建的会话，避免多条消息来会话还没创建完成再次创建更新
  const creatingConversation = new Map();
  // 当前正在聊天的消息
  const chatingMessages = ref<MsgWrapper[]>([]);
    // 当前被引用的消息
  const referMsg = ref<MessageToSave>(null);

  // openIM历史消息是否更多
  const imHasHistoryMore = ref(true);
  // 第一页历史报错，用在收取中的时候切换会话拉取历史报错禁止发送消息
  const getfirstOpenImHistoryFail = ref(false);
  // 获取历史消息参数
  const getHistoryOption = ref<GetAdvancedHistoryMsgParams>();
  // 展示拉取历史数据异常
  const showHistoryError = ref(0);
  // 搜索到的消息
  const highlightedSearchedId = ref('-1');
  // 搜索到的消息
  const isFirstPageForChatingMessages = ref(false);
  // 需要更新的咨询消息
  const needUpdateConsultMessageUId = ref('');
  // 需要更新的数字平台联系人消息
  const needUpdateRefreshMessageUId = ref('');
  // 需要更新的订单消息
  const needUpdateOrderMessageUId = ref('');
  // 需要更新的反诈消息
  const needUpdatePushMessageUId = ref('');

  // 系统休眠状态
  const powerShutdown = ref(false);
  const setPowerShutdown = (type) => {
    powerShutdown.value = ['suspend', 'lock-screen'].includes(type);
  };
  // 窗口是否聚焦主窗口
  const mainWindowIsFocused = ref(true);
  const setMainWindowIsFocused = (type) => {
    // 聚焦有在聊天的窗口发送已读
    mainWindowIsFocused.value = type;
    console.log('===>mainWindowIsFocused.value',mainWindowIsFocused.value, chatingMessages.value.length)
    if (type && chatingMessages.value.length) {
      const oldConversationID = chatingSession.value.conversationID
      receiveMsgReadReceipt();
      setTimeout(() => {
        setUnreadMsgCount(oldConversationID);
      }, 1000);
    }

  };
  // 窗口未聚焦主窗口，currentRoute不在聊天窗口不需要清零未读消息
  const notReadMsgCount = () => {
    return mainWindowIsFocused.value && router.currentRoute.value.name === 'message' && localStorage.getItem("curMenuValue") == 'message'
  }
  /**
   * 切换会话分组
  */
  const activeGroupType = ref<GroupTabKey>('all')
  const groupTab = ref<GroupTabItem[]>([])
  // 获取分组tab数据
  const getPairGroup = async() => {
    const res = await getPairGroupConfig({
      openid: getOpenid()
    })
    activeGroupType.value = 'all';
    const data = res?.data?.data;
    if (data?.enabled === 1) {
      const list = []
      data?.enableList.forEach((item) => {
        list.push({...item, count:0 })
      })
      groupTab.value = list
      updateLeftbarBadge('getPairGroupConfig', false)
    } else {
      groupTab.value = []
    }
  }
  const onSwitchGroup = async (type:GroupTabKey) => {
    activeGroupType.value = type;
    commonChatClick(false)
    // 强制触发虚拟 DOM 复用
    sessionList.value = [...sessionList.value];
    setTimeout(() => {
      // 计算常用会话列表的未读消息
      getCommonListCounts();
    }, 100);
  }
  // 选中当前会话列表
  const sessionListActive = computed(() => {
    if(activeGroupType.value === 'all') return sessionList.value.filter((item) => !item.group_type );
    // 预解析过滤条件
    const filterMap = {
      community: (item) =>
        item.conversationType !== 6 &&
        (!item.group_type) &&
        ['22', '20', '23'].includes(item.relation as string),
      default: (item) =>
        item.conversationType !== 6 &&
        (!item.group_type) && item.relation !== 'IDLE_TEMPORARY' &&
        cardGroupType(item.myCardId) === activeGroupType.value,
      idle: (item) =>
        item.conversationType !== 6 &&
        (!item.group_type || item.group_type === 'GT_DEFAULT') &&
        item.relation === 'IDLE_TEMPORARY'
    };

    let list = sessionList.value.filter(activeGroupType.value === 'idle' ? filterMap.idle : filterMap.default);
    return list;
  });
  // 常用会话分类
  const commonChatActive = ref(false);
  const cardGroupTypeCache = new Map<string, string>();
  const getCardGroupType = (cardId: string) => {
    let type = cardGroupTypeCache.get(cardId)
    if(type) return type
    type = cardGroupType(cardId);
    cardGroupTypeCache.set(cardId, type);
    return type
  };
  const commonSessionList = computed(() => {
    const isAllGroup = activeGroupType.value === 'all';
    return sessionList.value.filter((item) =>
      item.group_type === 1 && (isAllGroup || getCardGroupType(item.myCardId) === activeGroupType.value) )
  })
  // 常用会话分类点击
  const commonChatClick = (data:boolean) => {
    if (data) {
      useNewEditorStore().newSaveConversationDraft(chatingSession.value);
      chatingSession.value = null;
      isMySendVcard.value = false;
    }
    commonChatActive.value = data;
    console.log('====>commonSessionList', commonSessionList.value)
  }

  // 正在聊天的会话成员
  const chatingSessionMembers = computed(() => {
    if (chatingSession.value?.conversationType === 1) {
      return allMembers.value.get(chatingSession.value?.localSessionId);
    }
    return allMembers.value.get(chatingSession.value?.targetId);

  });

  // 正在聊天的会话群组
  const chattingGroup = computed(() => {
    if (chatingSession.value?.conversationType === 3) {
      return allGroups.value.find((g) => g.group === chatingSession.value?.targetId);
    }
    return null;
  });

  // 是否助手会话
  const isAssistantSession = computed(() => chatingSession.value && chatingSession.value.conversationType === 6);

  // 群聊的时候，我在本群的所有身份卡
  const myChatingGroupCards = computed(() => {
    if (chatingSession.value?.conversationType !== 3) {
      return [];
    }
    const myCards = getImCardIds();
    const groupMemberMap = allMembers.value.get(chatingSession.value?.targetId);
    return myCards.filter((card) => groupMemberMap.has(card));
  });

  const showNotInGroup = computed(() => {
    if (chatingSession.value?.conversationType === 1) {
        return chatingSession.value.unregistered;
    }
    return !isAssistantSession.value && !chatingSession.value?.inSession;
  })

  const showNotFriends = computed(() => {
    return chatingSession.value?.conversationType === 1 && !chatingSession.value?.inSession;
  })
  // 聊天我的身份对应的组织
  const getMyCardTeam = (session: ConversationToSave) => {
    if (session.conversationType === 1) {
      const sessionId = session.localSessionId;
      const isPlat = session.localSessionId.indexOf(':PT');
      const map = allMembers.value.get(sessionId);
      const myCard = map?.get(session.myCardId);
      const peerCard = map?.get(session.targetCardId);
      const isCompany = cardIdType(myCard?.cardId) !== 'personal';
      if (isCompany || myCard?.teamId) {
        const platText = isPlat !== -1 ? '[平台]' : '';
        let teamName = '',teamId ='';
        // 社群会话自己没有teamId 取对方的
        if(!myCard?.teamId){
          teamName = peerCard?.teamName
          teamId = peerCard?.teamId
        }else{
          teamName = myCard?.teamName
          teamId = myCard?.teamId
        }
        // 专属名称替换逻辑
        if (platText && exclusiveNames.value.get(teamId)) {
          const exclusiveName = exclusiveNames.value.get(teamId);
          return platText + exclusiveName;
        }

        return teamName ? platText + teamName : '';
      }
      return '个人';
    }
    let teamName = getGroupInfo(session.targetId)?.attachment?.teamName || '';

    // 专属名称替换逻辑, 内部不需要换

    const map = allMembers.value.get(session.targetId);
    const myCard = map?.get(session.myCardId);
    if (!teamName) {
      const isCompany = cardIdType(myCard?.cardId) !== 'personal';
      if (isCompany || myCard?.teamId) {
        teamName = myCard?.teamName || '';
      }
    }
    // 23：数字平台分组群
    if (['22', '20', '23'].includes(session.relation as string)) {
      const exclusiveName = exclusiveNames.value.get(myCard?.teamId);
      if (exclusiveName) {
        teamName = exclusiveName;
      }
      teamName = `[平台]${teamName}`;
    }
    return teamName;

  };

  /**
   * 重新排序
   * newMsg, conversationChange。非自己发的需要闪烁
   * 置顶，删除，不显示聊天，退出群聊。不需要闪烁
   * @param badge 默认true 更新总的红点。
   * @param flashFrame 默认false 不闪烁。
   */
  const sortSessions = (origin, badge = true, flashFrame = false) => {
    sessionList.value = sessionList.value.sort((a, b) => {
      if (Boolean(a.isTop) !== Boolean(b.isTop)) {
        return a.isTop ? -1 : 1;
      }
      // 置顶的会话使用updateTime排序
      if (a.isTop) {
        a.startChatTime = null;
        return (b.updateTime || 0) - (a.updateTime || 0);
      }
      // IM1.4.2迭代现在以updateTime时间排序，会话列表时间也展示updateTime。
      // 以前消息没更新updateTime，需要在初始化的时候需要把有摘要的sentTime没有则是createTime赋值给updateTime。
      // 增加消息更新updateTime
      const updateA = a.startChatTime || a.updateTime || 0;

      const updateB = b.startChatTime || b.updateTime || 0;

      return updateB - updateA;
    });
    badge && updateLeftbarBadge(`====>${origin}>sortSessions`, flashFrame);
  };

  /**
   * 更新助手会话置顶，免打扰
   * @param data
   */
  const updateAssistantConversation = (data) => {
    const { assistantId, conversationId, noDisturb, stayOn } = data;
    const session = sessionList.value.find((it) => it.conversationID === conversationId)
    session.isMute = noDisturb === 1
    session.isTop = stayOn === 1
    sortSessions('updateAssistantConversation', false)
  }

  /**
   * 添加会话列表，默认重新排序
   * @param conversation 会话
   * @param reSort 是否重新排序
   * @param origin log更新来源
   */
  const onAddToSessionList = (conversation: ConversationToSave, reSort = true, origin?: string) => {
    const index = sessionList.value.findIndex((item) => item.localSessionId === conversation.localSessionId);
    if (index === -1) {
      console.log('=====>onAddToSessionList_push', conversation, origin);
      conversation.updateTime = Math.max(conversation.updateTime || 0, conversation.latestMessage?.sentTime || 0, conversation.createTime);
      sessionList.value.push(conversation);
    } else {
      // !! 防止最新消息被覆盖置空
      const oldItem = sessionList.value[index];
      const oldMsgTime = oldItem.latestMessage?.sentTime || 0;
      const newMsgTime = conversation.latestMessage?.sentTime || 0;
      conversation.unreadCount === 0 && (conversation.unreadCount = oldItem.unreadCount);
      if (oldMsgTime > newMsgTime || !newMsgTime) {
        conversation.latestMessage = oldItem.latestMessage;
      } else if (oldMsgTime === newMsgTime) {
        // 摘要已读
        conversation.latestMessage.isRead = oldItem.latestMessage.isRead || conversation.latestMessage.isRead;
      }
      console.log('=====>onAddToSessionList', conversation.conversationID, conversation.unreadCount, conversation.latestMessage, origin, oldItem.latestMessage);
      conversation.updateTime = Math.max(conversation.updateTime || 0, conversation.latestMessage?.sentTime || 0, conversation.createTime);
      sessionList.value.splice(index, 1, conversation);
    }
    // 重新排序
    if(reSort) {
      setTimeout(() => {
        sortSessions('onAddToSessionList');
      }, 200);
    }
  };
  /**
   * 从数据库加载会话信息，用于首次获取的时候
   */
  const loadSessionList = async () => {
    await loadDbSessions();
    const IM_HAS_HISTORY = sessionList.value.length ? 'HAS' : 'NONE';
    localStorage.setItem('IM_HAS_HISTORY', IM_HAS_HISTORY);
  };

  /**
   * 加载本地数据库会话列表，成员，群列表, 首次获取
   */
  const loadDbSessions = async () => {
    const [dbMembers, dbGroups] = (await Promise.all([
      ipcRenderer.invoke('im.session.member.query'),
      ipcRenderer.invoke('im.group.query'),
    ])) as [ConversationMemberToSave[], GroupToSave[]];

    // 先更新界面
    Array.isArray(dbMembers) && (allMembers.value = getMappedMembers(dbMembers));
    allGroups.value = Array.isArray(dbGroups) ? dbGroups : [];
    loadAllDbConversation(true);
  };

  /**
   * 获取所有db会话列表
   */
  const loadAllDbConversation = async (isFirst = false) => {
    const dbSessionList = await queryAllConversations();
    const conversations = Array.isArray(dbSessionList) ? dbSessionList : [];
    const conversationList = conversations.filter((item) => {
      if (isFirst) {
        item.unreadCount = 0;
      }
      if (item.conversationType === 6) {
        item.avatar = assistantMap[item.localSessionId]?.avatar;
        item.name = assistantMap[item.localSessionId]?.name;
      }
      // 场景红点结构转换
      item.sceneRedDotObj = safeParseJson(item.sceneRedDot, {});
      item.latestMessage = safeParseJson(item.latestMessage as string, null);
      (item.latestMessage as MessageToSave)?.sentStatus === 10 && ((item.latestMessage as MessageToSave).sentStatus = 30);
      // if ([1, 6].includes(item.conversationType)) {
      //   // 没updateTime的赋值createTime，并且过滤掉组织内部，单聊产生关系，无消息的对话
      //   return Boolean(item.updateTime);
      // }
      const updateTime = item.updateTime || 0;
      const sentTime = (item.latestMessage as MessageToSave)?.sentTime || 0;
      item.updateTime = Math.max(updateTime, sentTime, item.createTime);

      // 有些异常数据没有session_id
      return !!item.targetId;
    });
    sessionList.value = conversationList;
    console.log('====>本地数据会话加载完成', conversationList, Date.now());
    !isFirst && sortSessions('loadAllDbConversation');
  };

  /**
   * 加载远端数据
   */
  const loadRemoteSessions = async (cards?: string[]) => {
    const myCards = cards || getImCardIds();
    getSeverTime();
    try {
      // 请求服务器会话
      const { sessions, members, groups } = await getAllSessions(myCards);

      if (groups.length) {
        // 保存群聊
        await insertOrUpdateGroup(groups);
        // 再从db拿出群聊
        allGroups.value = await ipcRenderer.invoke('im.group.query') ?? [];
      }
      // 新账号不需要等同步数据
      // if (sessions.length < 2) {
      //   useNetStore().setServerFinish(true);
      // }

      // 更新单聊成员
      if (members.length ) {
        await ipcRenderer.invoke('im.session.member.update', { list: members });
        const dbMembers = await ipcRenderer.invoke('im.session.member.query');
        Array.isArray(dbMembers) && (allMembers.value = getMappedMembers(dbMembers));
      }
      onSyncServerFinish(sessions, cards);

      const groupMember = groups.length ? await getGroupMember(groups) : []

      // 直接更新成员表
      if (groupMember.length) {
        await ipcRenderer.invoke('im.session.member.update', { list: groupMember });
        const dbMembers = await ipcRenderer.invoke('im.session.member.query');
        Array.isArray(dbMembers) && (allMembers.value = getMappedMembers(dbMembers));
      }
    } catch (error) {
      console.error(error);
    }
  };

/**
 * 同步成功后更新会话
 * @param session 传入会话
 * @param cards 是否是新增身份卡会话, 初始化没传入cards
 */
  const onSyncServerFinish = async (session?, cards?, origin?: string) => {
    let dbSessionList = await queryAllConversations();
    if (session?.length > 0) {
      // 会话全部以服务端返回的为准，本地数据库会话有以前遗留会话，这些会话没有openIMid需要清除掉。
      dbSessionList = dbSessionList.filter((item) => {
        if (session.find((it) => it.localSessionId === item.localSessionId)) {
          return true;
        }
        if (!cards) {
          try {
            console.log('=====>onDbConversationDelete', item.localSessionId, origin);
            onDbConversationDelete({ localSessionId: item.localSessionId });
            const index = sessionList.value.findIndex((it) => it.localSessionId === item.localSessionId);
            if (index !== -1) {
              sessionList.value.splice(index, 1);
            }
          } catch (error) {
            console.error('onDbConversationDelete', error);
          }
        }
        return false;

      });
    } else {
      session = sessionList.value.length > 0 ? toRaw(sessionList.value) : dbSessionList;
    }
    const toInsert = [];
    const toUpdate = [];
    const openImSessionList = origin === 'onSyncServerFinish' ? (await getOpenIMConversationList()).data : [];
    console.log('=====>openImConversations', dbSessionList, session, openImSessionList, origin);
    // 同步完成发送过来的，im退出后，需要检查是否新增会话，更新会话列表。
    if (origin === 'onSyncServerFinish' && useNetStore().lastDisconnetTime > 0) {
      let moreLength = 0;
      openImSessionList.every((item) => {
        if (item.latestMsgSendTime > useNetStore().lastDisconnetTime) {
          if (![4, 5].includes(item.conversationType)) {
            moreLength += 1;
          }
          return true;
        }
        return false;
      });
      // 同步的数据大于服务端返回的数据需要检查补全session；
      if (moreLength > 0) {
        // 请求服务器会话
        const { sessions, members, groups } = await getAllSessions([]);
        console.log('====>moreLength', moreLength, members, sessions);
        sessions.forEach((item) => {
          const index = session.findIndex((it) => it.localSessionId === item.localSessionId);
          if (index === -1) {
            session.push(item);
          }
        });
        reLoadRemoteSessions(groups, members);
      }
    }
    const removeSessions = await queryRemoveSessions();
    console.log('====>removeSessions', removeSessions);
    await Promise.allSettled(
      session.map(async (s) => {
        if (!s.targetOpenImId) {
          return false;
        }
        // 初始进入获取openIM会话id,最新一条摘要消息，未读数量
        if (!s.conversationID) {
          const conversationInfo = { conversationType: s.conversationType, targetId: s.targetOpenImId, myOpenImId: s.myOpenImId || '' };
          console.error('====>没有openIMid会话conversationInfo', conversationInfo);
          const { data } = await getConversationIDBySessionType(conversationInfo);
          s.conversationID = data;
          if(!data) return false;
        }
        const isRemove = removeSessions.find((it) => it.conversationID === s.conversationID);
        if (isRemove) {
          return false;
        }
        const dbItem = dbSessionList.find((it) => it.localSessionId === s.localSessionId);
        if (dbItem?.latestMessage) dbItem.latestMessage = safeParseJson(dbItem.latestMessage as string);
        const uiItem = toRaw(sessionList.value.find((it) => it.localSessionId === s.localSessionId));
        // 清除历史会话红点数，只用openImConversations返回的。
        s.unreadCount = 0;
        openImSessionList.forEach((item) => {
          if (item.conversationID === s.conversationID) {
            if (item.latestMsg) {
              const latestMsg = safeParseJson(item.latestMsg);
              // db最后一条消息的比openIM返回的新用db的
              if (!dbItem?.latestMessage?.sendTime || latestMsg.sendTime >= dbItem.latestMessage.sendTime) {
                latestMsg.status === 1 && (latestMsg.status = 2);
                const msgItem = msgToUseChat(latestMsg);

                if (msgItem) {
                  // 转换消息格式
                  delete msgItem?.textElem;
                  delete msgItem?.attachedInfoElem;
                  delete msgItem?.receipts;
                  delete msgItem?.attachedInfo;
                  delete msgItem?.quoteElem;
                  const lastMsg = getMsgToStore(msgItem);
                  if (lastMsg) {
                    // 音视频信令
                    if (lastMsg.contentExtra?.contentType === 'meeting') {
                      if (['ringing', 'accept'].includes(lastMsg.contentExtra.data.action)) {
                        return false;
                      }
                      // 群聊的信令过滤掉单聊通知
                      if (lastMsg.contentExtra?.data.meetingType === 'group' && lastMsg.conversationType === 1) {
                        return false;
                      }
                    }
                    s.latestMessage = lastMsg;
                  }
                }
              }
            }
            s.unreadCount = item.unreadCount;
            return item;
          }
        });
        // 没有摘要的保留本地或数据库的那条摘要
        if (!s.latestMessage) {
          if (uiItem?.latestMessage) {
            // 转换消息格式
            delete uiItem?.latestMessage?.textElem;
            delete uiItem?.latestMessage?.attachedInfoElem;
            delete uiItem?.latestMessage?.receipts;
            delete uiItem?.latestMessage?.attachedInfo;
            delete uiItem?.latestMessage?.quoteElem;
            s.latestMessage = uiItem?.latestMessage;
          } else if (dbItem?.latestMessage) {
            // 转换消息格式
            delete dbItem?.latestMessage?.textElem;
            delete dbItem?.latestMessage?.attachedInfoElem;
            delete dbItem?.latestMessage?.receipts;
            delete uiItem?.latestMessage?.attachedInfo;
            delete uiItem?.latestMessage?.quoteElem;
            s.latestMessage = dbItem?.latestMessage;
          }
        }
        // 本地不存在，或者本地存在，但是是未删除的会话，都需要添加到会话列表
        if (!uiItem || !dbItem || !dbItem.removeSession) {
          console.log('====>onAddToSessionLists', s, origin);
          onAddToSessionList(s, false, 'onSyncServerFinish');
        }
        // 保存到数据库
        dbItem ? toUpdate.push(s) : toInsert.push(s);
      }),
    ).catch((err) => {
      console.error('====>im-同步成功后更新会异常', origin, err);
      logHandler({ name: 'im-同步成功后更新会异常', level: 'middle', info: `${JSON.stringify(err)}`, desc: '' });
    });
    console.log('====>用时onSyncServerFinish加载完成', Date.now());
    sortSessions('onSyncServerFinish');
    if (toInsert.length) {
      console.log('====>toInsert', toInsert);
      await insertConversations(toInsert);
    }

    if (toUpdate.length) {
      console.log('====>toUpdate', toUpdate);
      await updateDbConversationFromServer(toUpdate, 'onSyncServerFinish');
    }
    // 收到sdk同步完成后检测没有摘要的会话做补偿
    if (origin === 'onSyncServerFinish') {
      getSdkLatestMsg().then(() => { sortSessions('getSdkLatestMsg', false, false); });
    }
    // await loadAllDbConversation();
    onApplaunchIsServerFinish('onSyncServerFinish');
    return 'done';
  };

  /**
 * 补偿加载远端数据更新群聊和成员
 */
  const reLoadRemoteSessions = async (groups, members) => {
    try {

      if (groups.length) {
        // 保存群聊
        await insertOrUpdateGroup(groups);
        // 再从db拿出群聊
        allGroups.value = await ipcRenderer.invoke('im.group.query') ?? [];
      }

      // 直接更新成员表
      if (members.length) {
        await ipcRenderer.invoke('im.session.member.update', { list: members });
        const dbMembers = await ipcRenderer.invoke('im.session.member.query');
        Array.isArray(dbMembers) && (allMembers.value = getMappedMembers(dbMembers));
      }
    } catch (error) {
      console.error(error);
    }
  };
  /**
 * 摘要补偿
*/
  const getSdkLatestMsg = async () => {
    for (const uiItem of sessionList.value) {
      if (!uiItem.latestMessage) {
        const info = { conversationType: uiItem.conversationType, targetId: uiItem.targetOpenImId, myOpenImId: uiItem.myOpenImId };
        // const result = await getOneConversationDetail(info);
        // console.log('====>result', result);
        const conversationInfo = { conversationType: uiItem.conversationType, conversationID: uiItem.conversationID, targetId: uiItem.targetId };
        const { hasMore, data, option } = await loadOpenIMHistory(conversationInfo, { count: 10, lastMinSeq: 0, startClientMsgID: '', conversationID: '' }, 'getSdkLatestMsg');
        const latestMsg = data?.[0];
        // console.log('====>latestMsg', uiItem, latestMsg);
        if (latestMsg) {
          delete latestMsg?.textElem;
          delete latestMsg?.attachedInfoElem;
          delete latestMsg?.receipts;
          uiItem.msgId = latestMsg.messageUId;
          uiItem.latestMessage = latestMsg;
        }
        uiItem.updateTime = Math.max(uiItem.updateTime || 0, latestMsg?.sentTime || 0, uiItem.createTime);
      }
    }
  };

  const conversationChangedNewSession = new Map();
  /**
 * 会话变更, 未读数，摘要
 * @param param0
 */
  const onConversationChanged = async ({ updateTime, latestMsg, conversationID, conversationType, unreadCount, isPinned }) => {
    let hasSession = false;
    let needSort = false
    for (const uiItem of sessionList.value) {
      if (uiItem.conversationID === conversationID && uiItem.inSession) {
        console.log('1====>uiItem', updateTime, latestMsg, uiItem.conversationID);
        hasSession = true;
        const uiLastMessage = uiItem.latestMessage;
        if (uiItem.isTop !== isPinned) {
          needSort = true
          uiItem.isTop = isPinned;
        }

        // 不存在更新时间或者ui的更新时间小于等于传入的时间才设置lastmsg
        if (latestMsg && (!uiLastMessage || !uiItem.updateTime || uiItem.updateTime <= updateTime)) {
          const latestMessage = safeParseJson(latestMsg);
          if (!latestMessage || latestMessage.status >= 4) return;
          const msgItem = msgToUseChat(latestMessage);
          if (msgItem) {
            // 转换消息格式
            delete msgItem?.textElem;
            delete msgItem?.attachedInfoElem;
            delete msgItem?.receipts;
            const lastMsg = getMsgToStore(msgItem);
            if (!lastMsg) return;
            // 音视频信令
            if (lastMsg.contentExtra?.contentType === 'meeting') {
              // 响铃自己发起拨打也会收到对方返回响铃。摘要过滤掉
              if (lastMsg.contentExtra?.data.action === 'ringing') {
                return;
              }
              // 群聊的信令过滤掉单聊通知
              if (lastMsg.contentExtra?.data.meetingType === 'group' && lastMsg.conversationType === 1) {
                return false;
              }
            }
            console.log('2====>lastMsg', lastMsg);
            // 避免摘要发送成功重置为发送中
            // id变化，发送状态不同并且不为已成功状态，已读状态不同且不为已读状态。
            if (
              !uiLastMessage
              || uiLastMessage.messageUId !== lastMsg.messageUId
              || (uiLastMessage.sentStatus !== lastMsg.sentStatus && uiLastMessage.sentStatus !== 30)
              || (uiLastMessage.isRead !== lastMsg.isRead && uiLastMessage.isRead !== true)
            ) {
              uiItem.latestMessage = lastMsg;
              console.log('3====>onConversationChangedlastMsg', uiItem.latestMessage);
              updateTime && (uiItem.updateTime = updateTime);
              needSort = true
            }
            // 超时发送失败可能 通信已经超时，需要同步更新消息状态
            if(lastMsg.sentStatus === 20 && conversationID === chatingSession.value?.conversationID){
              updateSendMsg(lastMsg, 'onConversationChanged')
            }
          }
        }
        // 在当前会话列表则不更新小红点
        if (chatingSession.value && chatingSession.value.conversationID === conversationID && notReadMsgCount()) {
          return;
        }
        if (uiItem.unreadCount !== unreadCount) {
          console.log('====>onConversationChangedunreadCount', conversationID, unreadCount, uiItem);
          uiItem.unreadCount = unreadCount;
          unreadCount === 0 && (uiItem.unreadMentionedCount = 0);
          updateLeftbarBadge('onConversationChangedunreadCount', !uiItem.isMute && unreadCount > 0);
        }
        // 只有群聊才会有多个，非群聊匹配到一个就退出。
        if (conversationType !== 3) {
          break;
        }

      }
    }
    needSort && sortSessions('onConversationChanged', false, false);
    if (!hasSession) {
      // 当前会话列表没有找到更新的会话，存起来等消息来创建会话后再去更新会话红点。
      conversationChangedNewSession.set(conversationID, { unreadCount, latestMsg });
    }
  };
  /**
   * 加载群聊标签
   * @param conversation 会话
   */
  const loadGroupMemberLabels = async (conversation?: ConversationToSave) => {
    conversation = conversation || chatingSession.value;
    if (conversation.relation !== '20' || conversation.conversationType !== 3) {
      return;
    }

    const groupId = conversation.targetId;
    const labelMap = groupLabels.value.get(groupId) || new Map();

    const dbLabels = await getGroupLabels(groupId);
    if (dbLabels.length) {
      dbLabels.forEach((item) => {
        labelMap.set(item.openid, item.label);
      });
      groupLabels.value.set(conversation.targetId, labelMap);
    }

    // 请求服务端接口
    const serverLabels = await reqGroupLabels(groupId);
    // 删除标签要清除
    labelMap.clear();
    if (serverLabels?.length) {
      serverLabels.forEach((item) => {
        labelMap.set(item.openid, item.label);
      });
      groupLabels.value.set(conversation.targetId, labelMap);
    }
  };

  // 首次获取历史记录异常处理，自动拉取3次
  const firstOpenImHistory = async (conversation, retryTimes?) => {
    const { hasMore, data, option, errCode } = await firstLoadOpenIMHistory(conversation);
    console.log('====>result', errCode, new Date());
    if (conversation.localSessionId === chatingSession.value?.localSessionId) {
      !retryTimes && (retryTimes = 0);
      getfirstOpenImHistoryFail.value = errCode !== 0;
      if (errCode !== 0 && retryTimes < 3) {
        setTimeout(async () => {
          firstOpenImHistory(conversation, retryTimes + 1);
        }, 2 * 1000);
      } else {
        console.log('====>errCode', errCode);
        if (errCode !== 0) {
          // 提示拉取历史异常
          showHistoryError.value = errCode;
          MessagePlugin.error({
            content: '获取聊天记录异常',
            duration: 3000,
          });
          return;
        }
        if (conversation.localSessionId === chatingSession.value?.localSessionId) {
          // 取倒数第二条，因为最新的一条会推送过来
          const len = chatingMessages.value.length;
          const index = len > 1 ? len - 2 : 0;
          if (len > 0 && chatingMessages.value?.[index]?.msg.seq === data?.[data.length - 2]?.seq) {
            console.log('====>chatingMessages.value', chatingMessages.value);
            return;
          }
          chatingMessages.value = [];
          // 发送中的消息和拉取历史的消息刚好msgUid和tempId不同时存在，去不了重
          MsgWrapper.cleanMessages()
          imHasHistoryMore.value = hasMore;
          getHistoryOption.value = option;
          console.log('===>fupdateMsgWrapper', data, JSON.stringify(chatingMessages.value));
          // const msgList = data.concat(mockMessageData)
          // const msgList = conversation.localSessionId === "assistant8app8store8assistant" ? data.concat(mockMessageData) : data
          const msgList = data
          updateMsgWrapper(msgList, true);
          sendReadReceipt();
          resetUnreadMsgCount(conversation.conversationID, 0, conversation.myOpenImId);
        }
      }
    }

  };
  /**
   * 加载OpenIM历史消息 TODO@合并消息，滚动加载下一页
   * @param conversation
   * @param isFirstPage
   * @returns
   */
  const loadOpenIMMsg = async (conversation: ConversationToSave, isFirstPage = false) => {
    console.log('=====>loadOpenIMMsg', isFirstPage, conversation);
    changeShouldScroll(isFirstPage);
    isFirstPageForChatingMessages.value = isFirstPage;
    if (conversation.localSessionId !== chatingSession.value?.localSessionId) {
      // 由于获取数据库数据是异步的，返回的时候已经不是当前聊天就不做处理。如果不匹配，则不处理
      return false;
    }
    // openIM数据拉取完去拉本地数据库
    if (!imHasHistoryMore.value) {
      return false;
    }
    let result;
    if (isFirstPage) {
      // 首次进入会话加载成员标签
      if (conversation.conversationType === 3) {
        loadGroupMemberLabels(conversation);
      }
      // 拉取第一页历史
      result = await firstOpenImHistory(conversation);
      return result;
    }
    result = await loadOpenIMHistory(conversation, getHistoryOption.value, 'loadOpenIMMsg');
    const { hasMore, data, option, errCode } = result;
    // openim历史拉取出错，不加载后续本地和服务端融云历史了。
    if (errCode !== 0) {
      return result;
    }

    if (conversation.localSessionId === chatingSession.value?.localSessionId) {
      imHasHistoryMore.value = hasMore;
      getHistoryOption.value = option;
      updateMsgWrapper(data, isFirstPage);
      sendReadReceipt();
    }
    return result;
  };

  // 更新会话数据
  const updateMsgWrapper = (dbRes, isFirstPage = false) => {
    if (dbRes?.length) {
      const conversation = chatingSession.value;
      MsgWrapper.onLoadDbMessages(dbRes, { joined: conversation.joined, viewHistory: conversation.viewHistory });
      chatingMessages.value = MsgWrapper.getComputeMessages();
      // 设置最新消息
      if (isFirstPage && chatingMessages.value) {
        const latest = chatingMessages.value[chatingMessages.value.length - 1];
        let msg = latest.msg;
        delete msg?.textElem;
        delete msg?.attachedInfoElem;
        delete msg?.ex;
        conversation.latestMessage = msg;
        const uiSession = sessionList.value.find((val) => val.conversationID === conversation.conversationID);
        uiSession.updateTime = Math.max(uiSession.updateTime || 0, msg?.sentTime || 0, uiSession.createTime);
        sortSessions('updateMsgWrapper', false);
        uiSession.latestMessage = msg;
        onDbConversationMessageUpdate(conversation, 'updateMsgWrapper');
      }
      isFirstPageForChatingMessages.value = isFirstPage;
    }
  };

  const onDeleteMessageFromList = async (messageUId: string) => {
    changeShouldScroll(false);
    MsgWrapper.onDeleteMsg(messageUId);
    chatingMessages.value = MsgWrapper.getComputeMessages();

    const length = chatingMessages.value.length;

    const preMerged = length ? chatingMessages.value[length - 1] : null;
    const mergedLength = preMerged?.merged?.length ?? 0;
    const preMsg = mergedLength ? preMerged.merged[mergedLength - 1] : preMerged;
    const lastMsg = preMsg?.msg;
    delete lastMsg?.textElem;
    if ([1, 6].includes(chatingSession.value?.conversationType)) {
      // 单聊直接删除
      chatingSession.value.latestMessage = lastMsg || null;
      chatingSession.value.updateTime = lastMsg?.sentTime || chatingSession.value.createTime;
      for (const session of sessionList.value) {
        if (session.conversationID === chatingSession.value.conversationID) {
          session.latestMessage = lastMsg;
          session.updateTime = chatingSession.value.updateTime;
          break;
        }
      }
      onDbConversationMessageUpdate(chatingSession.value, 'onDeleteMessageFromList1');

    } else if (chatingSession.value?.conversationType === 3) {
      // 群聊需要额外处理
      sessionList.value.forEach((item) => {
        if (item.targetId === chatingSession.value.targetId) {
          item.latestMessage = lastMsg;
          item.updateTime = lastMsg?.sentTime;
        }
      });

      const dbConversations = await queryConversationByIds({ targets: [chatingSession.value.targetId] });
      dbConversations.forEach((item) => {
        item.latestMessage = lastMsg;
      });
      onDbConversationMessageUpdate(dbConversations, 'onDeleteMessageFromList3');
    }
    sortSessions('onDeleteMessageFromList', false);
  };
  /**
   * 更新聊天是否需要置底
   * @param v Boolean
   */
  const changeShouldScroll = (v) => {
    shouldScroll.value = v;
  };
  const setScrollHeight = (scroll, scrollP) => {
    scrollPx.value = scroll;
    scrollPosition.value = scrollP;
  };
  const loadMessageMoreToMentionMe = async (type: string) => {
    // unread未读消息 mention未读的 @ 消息
    if (unredInfo.count > 1000) {
      MessagePlugin.error({
        content: '数据太多，加载异常',
        duration: 3000,
      });
      unredInfo.count = 0;
      return;
    }
    let length = chatingMessages.value.length;
    // 未读数量大于当前会话消息数量 循环去拉取历史
    while (unredInfo.count + 19 > length) {
      const { errCode, hasMore, data } = await loadOpenIMMsg(chatingSession.value, false);
      if (errCode !== 0 || !hasMore) {
        break;
      }
      length += data.length;
    }
    let count = 0; const mentions = []; const len = chatingMessages.value.length;
    for (let i = len - 1; i >= 0; i--) {
      const msgItme = chatingMessages.value[i];
      if (msgItme.merged) {
        const mergedCount = msgItme.merged.filter((it) => ((it.msg?.openImId && it.msg?.openImId === chatingSession.value.myOpenImId) || it.msg?.senderUserId !== chatingSession.value.myOpenImId));
        count += mergedCount.length;
        if (count >= unredInfo.count) {
          if (type === 'unread') {
            unredInfo.count = 0;
            return gotoMessageById(msgItme.msg.messageUId);
          }
          break;
        }
      }
      const msg = msgItme.msg;
      if ((msg.openImId && msg.openImId === chatingSession.value.myOpenImId) || msg.senderUserId !== chatingSession.value.myOpenImId) {
        if (!unredInfo.time || msg.sentTime <= unredInfo.time) { // 标记之前的时间才计算
          count++;
          if (type === 'mention' && shouldCountMention(msg)) {
            mentions.push(msgItme);
            if (unredInfo.mention === mentions.length) {
              unredInfo.mention = 0;
              return gotoMessageById(msg.messageUId);
            }
          }
          if (type === 'unread') {
            if (count === unredInfo.count) {
              unredInfo.count = 0;
              return gotoMessageById(msg.messageUId);
            }
          }
        }
      }
    }
    if (type === 'unread') {
      unredInfo.count = 0;
      chatingMessages.value[0]?.msg?.messageUId && gotoMessageById(chatingMessages.value[0].msg.messageUId);
    } else {
      // 获取最后一条消息的messageUId, 如果没有向上查找
      const item = mentions?.reverse?.()?.find((item) => item.messageUId);
      gotoMessageById(item.messageUId);
      unredInfo.mention = 0;
    }
  };

  // 当前聊天消息跳转，引用回复、工具区历史消息跳转
  const gotoChatingSessionMessage = async (messageUId: string, localSessionId?: string) => {
    const msg = chatingMessages.value.find((item) => item.messageUId === messageUId || item.merged?.some((it) => it.messageUId === messageUId));
    // 在当前消息列表中
    console.log('消息跳转4', msg);
    if (msg) {
      gotoMessageById(msg.messageUId);
    } else {
      // 从数据库查找消息
      console.error('=====>未找到消息', messageUId);
      loadMessageToID(messageUId, chatingSession.value);
      //  if (localSessionId === chatingSession.value?.localSessionId) {
      //    gotoMessageById(messageUId);
      //   }
    }
  };

  // 聚合搜索跳转到消息
  const gotoMessage = async (params: { messageUId: string, sentTime: number, localSessionId: string, conversationType: number, targetId: string }) => {
    console.log('消息跳转', params);
    const { messageUId, targetId, localSessionId, conversationType, sentTime } = params;

    let session = sessionList.value.find((s) => (conversationType === 3 ? s.targetId === targetId : s.localSessionId === localSessionId));
    // 如果会话不在 UI列表中
    if (!session) {
      const option = conversationType === 1 ? { ids: localSessionId } : { targets: targetId };
      const dbSessions = await queryConversationByIds(option);
      if (dbSessions.length) {
        session = dbSessions[0];
        onAddToSessionList(session);
      }
    }
    console.log('消息跳转2', session);

    if (session) {
      if (session.localSessionId !== chatingSession.value?.targetId) {
        setCurrentSession(session);
      }

      console.log('消息跳转3', session, params);

      // setTimeout(async () => {
      //   gotoChatingSessionMessage(messageUId, session.localSessionId);
      // }, 100);
    }
    /**
     * fix https://www.tapd.cn/69781318/bugtrace/bugs/view/1169781318001045972
     */
    cancelHighlightedMsg();
    setTimeout(async () => {
      gotoChatingSessionMessage(messageUId, session?.localSessionId);
    }, 300);
  };

  // 递归加载历史记录滚动到对应的msg
  const loadMessageToID = async (messageUId, chating) => {
    let goOn = true;
    const result = await loadOpenIMMsg(chating, false);
    if (result?.data?.length && chating.localSessionId === chatingSession.value?.localSessionId) {
      const { hasMore, data } = result;
      const msg = data.find((item) => item.messageUId === messageUId);
      console.log('====>跳转msg', msg);
      if (msg) {
        gotoMessageById(messageUId);
      } else if (hasMore && goOn) {
        loadMessageToID(messageUId, chating);
      }
    } else {
      goOn = false;
    }
  };

  // 根据id更新是否每个会话需要详情展示，类型为空则不展示
  // extend: 详情参数 link云盘链接, msg.msg?: MessageToSave 服务详情消息,服务页面传入 兼容服务详情
  const updateMsgDetailById = (msg: MessageToSave | ConversationToSave, type: MsgDetailDrawerType | null, link?: string | null, extend?: { msgDetailOptionZIndex?: number | null, sceneOptionZIndex: number | null }) => {
    // extend?.msg: 服务详情信息是否存在
    const msgs = msg?.msg ? msg.msg : msg;
    const idType = msgs?.conversationType === 1 ? 'localSessionId' : 'targetId';
    let index = -1;
    try {
      // 文件助手没有消息的时候点击发送名片这里报错
      index = sessionList.value.findIndex((v: ConversationToSave) => v[idType] === msgs[idType]);
    } catch (e) {
      index = -1;
      console.log('showMsgDetail', e);
    }
    if (index !== -1) {
      const detailObj = sessionList.value[index]?.detailObj || {};
      let additionalData = {
        detailType: type,
        id: msgs?.id || null,
        link: link || null,
        serverDetailId: msg?.msg ? msg.messageUId : null,
      };
      // 更新会话的 detailObj
      if (extend) {
        additionalData = {
          msgDetailOptionZIndex: extend?.msgDetailOptionZIndex || null,
          sceneOptionZIndex: extend?.sceneOptionZIndex || null,
        };
      }
      sessionList.value[index].detailObj = Object.assign(detailObj, additionalData);
    }
  };

  const gotoMessageById = (id: string) => {
    highlightedSearchedId.value = id;
    console.log('当前选中高亮消息id', id, highlightedSearchedId.value);
  };

  // 取消高亮消息
  const cancelHighlightedMsg = (setcount = true) => {
    highlightedSearchedId.value = '-1';
    console.log('取消选中');
  };
  const ringingMsg = (type?: string) => {
    // 是否锁定或熄屏
    const isShutdown = powerShutdown.value;
    if(!isShutdown && getShouldRinging()){
      type === 'orderRing' ? onOrderRingNewMessage() : onRingNewMessage();
    }
  };
  const onRingNewMessage = _.throttle(() => {
      new Audio(RingTone).play();
  }, 2000, { leading: true });

  const onOrderRingNewMessage = _.throttle(() => {
      new Audio(OrderRingTone).play();
  }, 2000, { leading: true });

  // 计算会话红点
  const totalUnread = ref(0);
  const updateLeftbarBadge = (origin, flash = true) => {
    const groupCounts = {
      all: 0,
      friend: 0,
      external: 0,
      internal: 0,
      community: 0
    };
    sessionList.value.forEach(item => {
      const { unreadCount = 0, isMute } = item;
      const sessionUnread = isMute ? 0 : unreadCount;
      // 总未读数
      groupCounts.all += sessionUnread;

      // 分组统计（仅统计普通会话）
      if (groupTab.value.length && item.conversationType != 6) {
        const cardType = cardGroupType(item.myCardId);
        groupCounts[cardType] += sessionUnread;
      }
    });
    // 更新分组计数
    groupTab.value.map((item)=>{
      item.count = groupCounts[item.key];
    })
    getCommonListCounts();
    const flashFrame = flash || groupCounts.all > totalUnread.value;
    totalUnread.value = groupCounts.all;
    ipcRenderer.invoke('sidebar-count-change', { title: '消息', count:groupCounts.all, flashFrame });
    console.log('====>updateLeftbarBadge', origin, totalUnread, flashFrame);
  };

  const commonListCounts = ref(0)
  const latestConversation = ref<ConversationToSave>(null);
  const getCommonListCounts = () => {
    // 常用会话计算红点. 切换tab也要计算commonCount
    if(!commonSessionList.value || !commonSessionList.value.length) {
      commonListCounts.value = 0;
      latestConversation.value = null;
      return;
    };
    const commonCount = commonSessionList.value.reduce((total, item) =>
    total + (item.isMute ? 0 : (item.unreadCount ?? 0)), 0);
    commonListCounts.value = commonCount;
    latestConversation.value = commonSessionList.value.reduce((a, b) =>
      (a.updateTime || 0) > (b.updateTime || 0) ? a : b
    );
    console.log('===>latestConversation', latestConversation);
  }

  /**
   * 通过 conversationID 设置会话未读数
   * @param conversationID 会话ID
   * @param count 未读数，默认为0
   */
  const setUnreadMsgCount = (conversationID?: string, count?: number, flash?: boolean) => {
    if(!chatingSession.value) return;
    const defaultCount = count ?? 0; // 处理默认值
    const defaultFlash = flash ?? false; // 处理默认值
    const ID = conversationID || chatingSession.value.conversationID;
    for (const session of sessionList.value) {
      if (session.conversationID === ID) {
        if (session.unreadCount === defaultCount) break;
        session.unreadCount = defaultCount;
        defaultCount === 0 && (session.unreadMentionedCount = 0);
        onDbConversationMessageUpdate(session, 'resetUnreadMsgCount');
        updateLeftbarBadge('setUnreadMsgCount', defaultFlash);
        break;
      }
    }
  };

  // 修改已读状态
  const changeMsgRead = (msg: {
    targetId?: string;
    messageUId: string | [];
    conversationType?: number;
    readTime?: number;
    receiptTime?: number;
    receipts?: [ReceiptAttachedInfo];
    conversationID: string;
  }) => {

    const msgIds = typeof msg.messageUId === 'string' ? [msg.messageUId] : msg.messageUId;
    console.log('====>changeMsgReadMsgIds', msgIds);
    if (msg.conversationType === 1) {
      // 单聊才更新摘要已读状态和时间
      for (const it of sessionList.value) {
        // 更新对应会话
        // if(it.localSessionId === msg.localSessionId){
        const { latestMessage } = it;
        // 会话摘要更新已读状态
        if (latestMessage) {
          if (msgIds.includes(latestMessage.messageUId)) {
            latestMessage.receiptTime = msg.receiptTime;
            latestMessage.readTime = msg.readTime;
            if (msg.conversationType === 1) latestMessage.isRead = true;
            // 单聊只有一个跳出循环
            break;
          }
        }
        // }
      }
    }
    const groupHasReadInfo = {};
    if (msg.conversationType === 3) {
      msg.receipts.forEach((item) => {
        groupHasReadInfo[item.msgID] = item.attachedInfo.groupHasReadInfo;
      });
    }
    // 当前会话消息已读状态更新
    const updateReadMessage = (it: MsgWrapper) => {
      it.msg.receiptTime = msg.receiptTime;
      if (msg.conversationType === 3) it.msg.receipts = groupHasReadInfo[it.messageUId];
      it.msg.readTime = msg.readTime;
      if (msg.conversationType === 1) it.msg.isRead = true;
    };

    // 接收的已读对应消息是否当前会话
    if (msg.conversationID === chatingSession.value?.conversationID) {
      chatingMessages.value.forEach((it) => {
        if (msgIds.includes(it.messageUId)) {
          updateReadMessage(it);
          // it.merged?.forEach((m) => updateReadMessage(m));
        }
      });
    }
    // // 会话和消息已读状态不统一，埋点log
    // if (msgChangeStatus !== sessionChangeStatus) {
    //   const info = `chatingMessages:${JSON.stringify(chatingMessages.value)};msg:${JSON.stringify(msg)};lastMsgList:${JSON.stringify(lastMsgList)}`
    //   const desc = `store.ts>changeMsgRead();msgChangeStatus:${msgChangeStatus};msgChangeStatus:${sessionChangeStatus};`
    //   logHandler({ name: 'im-列表会话已读状态不统一', level: 'middle', info, desc });
    // }
  };

  // 设置当前会话
  const setCurrentSession = async (current: ConversationToSave, forceUpdate = false) => {
    if(!current.group_type) commonChatActive.value = false;
    if (forceUpdate || current.localSessionId !== chatingSession.value?.localSessionId) {
      useImToolStore().resetConversation();
      const oldConversationID = chatingSession.value?.conversationID;
      const oldConversationBindUserId = chatingSession.value?.myOpenImId;
      oldConversationID && useNewEditorStore().newSaveConversationDraft(chatingSession.value);
      imHasHistoryMore.value = true;
      getHistoryOption.value = null;
      showHistoryError.value = 0;

      const inviteStore = useGroupInviteStore();
      inviteStore.$reset();
      if (current.conversationType === 3 && ['0', '1', '2', '3', '10', '15', '20', '22', '23'].includes(current.relation)) {
        inviteStore.group = current.targetId;
      }

      // 兼容处理设置红点，避免sceneRedDotObj不存在
      current.sceneRedDotObj = current?.sceneRedDotObj || safeParseJson(current.sceneRedDot, {});
      // 设置为当前会话
      chatingSession.value = current;
      isMySendVcard.value = false;
      // 重置消息列表
      chatingMessages.value = [];
      // 清空消息
      MsgWrapper.cleanMessages();
      // 重置定位消息id
      cancelHighlightedMsg(false);

      resetUnreadMsgCount(current.conversationID, 0, current.myOpenImId);

      const unreadCount = current.unreadCount;
      console.log('====>unreadCount', unreadCount);
      if (unreadCount > 0) {
        unredInfo.count = unreadCount;
        unredInfo.mention = current.unreadMentionedCount;
        current.unreadCount = 0;
        current.unreadMentionedCount = 0;
        updateLeftbarBadge('setCurrentSession', false);
      } else {
        unredInfo.count = 0;
        unredInfo.mention = 0;
      }
      // 离开会话和新会话都设置下sdk的清除红点。
      // 稍后处理只需要清除红点 红点有问题先加上标记消息已读
      // oldConversationID && resetUnreadMsgCount(oldConversationID, 0);
      // if (current.conversationType === 6 && current.targetId === "assistant8app8pending") {
      //   return;
      // }
      // 【【IM】没聊过天的会话，会报获取聊天记录异常】https://www.tapd.cn/69781318/bugtrace/bugs/view/1169781318001046655
      const conversationInfo = { conversationType: current.conversationType, targetId: current.targetOpenImId, myOpenImId: current.myOpenImId, from:'setCurrentSession' };
      const result = await getOneConversationDetail(conversationInfo);
      current.conversationID = result.data?.conversationID;
      await loadOpenIMMsg(current, true);
      const latest = chatingMessages.value[chatingMessages.value.length - 1];
      // 设置进入会话页面时间，未读消息跳转使用
      unredInfo.time = latest?.msg.sentTime || 0;
      useImToolStore().reset();
      handleSyncedAllSeqsConversation(current.conversationID, oldConversationID);
      // 离开会话和新会话都设置下sdk的清除红点。
      oldConversationID && resetUnreadMsgCount(oldConversationID, 0, oldConversationBindUserId);
    }
  };

  // 开始聊天，
  const onStartChat = async (params: {
    main: string,
    peer?: string,
    group?: string,
    // 咨询内容
    consultInfo?: any
  }) => {
    console.log('=====>onStartChat', params);
    // 打开会话默认到全部分组
    activeGroupType.value = 'all';
    let localSessionId = '';
    if (!params.main) {
      localSessionId = params.peer;
    } else {
      localSessionId = getSessionLocalIdByCards(params.main, params.peer || params.group);
    }

    // 服务内容咨询数据
    if (params.consultInfo) {
      console.log('onStartChat=====', params, JSON.parse(params.consultInfo));
      setConsultInfo({ id: localSessionId, info: JSON.parse(params.consultInfo) });
    }

    // 首先判断数据库是否存在
    const dbSessions = await queryConversationByIds({ ids: localSessionId });
    // 查询结果数组不为空，说明存在
    if (dbSessions.length) {
      const index = sessionList.value.findIndex((item) => item.localSessionId === localSessionId);
      const session = index === -1 ? dbSessions[0] : sessionList.value[index];
      if (session.conversationType === 6) {
        session.avatar = assistantMap[session.localSessionId].avatar;
        session.name = assistantMap[session.localSessionId].name;
      }
      // 其他入口打开会话时间，需要排序但是不更新会话时间,收到消息或updateTime更新的时候需要置null，重新启用updateTime排序
      session.startChatTime = Date.now();
      session.removeSession = false;
      if (session.group_type) {
        commonChatActive.value = true;
      }
      onAddToSessionList(session, true, 'onStartChat');
      setCurrentSession(session);

      if (isAssistantByConversation(session)){
        onDbConversationFromServer([session]);
      } else {
        updateConversationRelation(session, 'onStartChat');
      }
      return;
    }

    /**
     * 本地不存在的情况，从服务器拉取关系
     */
    if (params.group) {
      const info = await getGroupSession(params.group);
      console.log('=====>onStartChatGroup', info, params);
      await onGroupChattingFromContact(info, params.main);
    } else {
      const info = await loadPrivateSession(params.main, params.peer, { ...params, localSessionId, type: 'startChat' });
      console.log('=====>onStartChatPrivate', info, params);
      if (info) {
        await onPrivateChattingFromContact(info);
      }
    }
  };

  const onPrivateChattingFromContact = async (info: Awaited<ReturnType<typeof getPrivateSession>>, open = true) => {
    const { session, members } = info;
    if (!session.conversationID) {
      const conversationInfo = { conversationType: session.conversationType, targetId: session.targetOpenImId, myOpenImId: session.myOpenImId || '' };
      const { data } = await getConversationIDBySessionType(conversationInfo);
      session.conversationID = data;
    }
    onAddToSessionList(session, true, 'onPrivateChattingFromContact');
    if (open) {
      session.updateTime = Date.now() + currentTimeDiff.value;
      if (session.group_type) {
        commonChatActive.value = true;
      }
      setCurrentSession(session);
    }
    // 更新界面数据
    allMembers.value.set(session.localSessionId, new Map(members.map((m) => [m.cardId, m])));

    // 更新数据库
    await insertConversations([session]);
    await ipcRenderer.invoke('im.session.member.update', { list: members });
  };

  const onGroupChattingFromContact = async (info: Awaited<ReturnType<typeof getGroupSession>>, card: string, open = true) => {
    const { session, myGroupCards, members, group } = info || {};
    // 群成员信息更新、保存
    await ipcRenderer.invoke('im.session.member.update', { list: members });
    const map = allMembers.value.get(group?.group) ?? new Map();
    members?.forEach((m) => map.set(m.cardId, m));
    allMembers.value.set(group?.group, map);

    // 群信息保存
    group && updateGroupInfo(group);

    myGroupCards?.forEach((item) => {
      const cardSession = getGroupCardSession(session, item);
      cardSession.startChatTime = Date.now();
      cardSession.removeSession = false;
      insertConversations([cardSession]);
      onAddToSessionList(cardSession, true, 'onGroupChattingFromContact');

      if (card === item.openid && open) {
        if (session.group_type) {
          commonChatActive.value = true;
        }
        setCurrentSession(cardSession);
      }
    });
  };
  // 被移除的群聊的 id 记录
  const alertDeletedGroups = reactive(new Set());
  // 是否展示被移除弹窗
  const showGroupRemoveAlert = computed(() => {
    if (!chatingSession.value || chatingSession.value.conversationType === 1) {
      return false;
    }
    return alertDeletedGroups.has(chatingSession.value.localSessionId) || alertDeletedGroups.has(chatingSession.value.targetId);
  });
  // 移除类型：被踢出kickOut、解散dissolution
  const isDissolutionOrKickOut = ref('');
  const setDissolutionOrKickOut = (type = '') => {
    isDissolutionOrKickOut.value = type;
  };

  // 被踢出群聊
  const onGroupKickedAlert = (group: string, card?: string, kickOutType?: string) => {
    setDissolutionOrKickOut(kickOutType);
    if (!card) {
      alertDeletedGroups.add(group);
    } else {
      alertDeletedGroups.add(getSessionLocalIdByCards(group, card));
    }
  };
  // 点开身份卡后，将对方的信息更新
  const checkPeerInfo = (data: IChatCardUpdateNotificationItem) => {
    sessionList.value.forEach((item) => {
      if (item.conversationType === 1 && item.targetCardId === data.cardId) {
        const peer = allMembers.value.get(item.localSessionId)?.get(data.cardId);
        if (peer) {
          peer.avatar = data.avatar;
          peer.comment = data.comment;
          peer.staffName = data.staffName;
          peer.describe = data.describe;
          const myInfo = allMembers.value.get(item.localSessionId)?.get(item.myCardId)
          const members = [toRaw(myInfo),toRaw(peer)]
          console.log('members',myInfo, members)
          ipcRenderer.invoke('im.session.member.update', { list: members });
        }
        console.log('onPrivatePeerInfoUpdate', item, peer,data);
      }
    });
  };

  /** *
   * 退出群聊
   * rid: # 外部群多身份
   */
  const onQuitGroupConversation = (conversation: ConversationToSave, rid: string, saveHistory = true) => {
    if (!saveHistory) {
      rid !== '#'
        ? onDeleteConversation({ targetId: conversation.targetId, deleHistory: !saveHistory })
        : onDbGroupCardDelete(conversation.targetId, conversation.myCardId);
    } else {
      conversation.inSession = false;
      if (chatingSession.value?.localSessionId === conversation.localSessionId) {
        chatingSession.value.inSession = false;
      }
      onDbConversationSetting(conversation);
    }
  };

  const onDeleteConversation = (params: { targetId?: string, localSessionId?: string, deleHistory?: boolean }) => {
    const chattingSessionIndex = sessionList.value.findIndex((item) => item.localSessionId === chatingSession.value?.localSessionId);
    console.log('====>storeonDeleteConversation', params);
    if (params.localSessionId) {
      if (params.deleHistory) {
        const session = sessionList.value.filter((item) => item.localSessionId === params.localSessionId)[0];
        console.log('====>storeonDeleteConversation2', session);
        if (session) {
          const conversationID = session.conversationID;
          const bindUserID = session.myOpenImId;
          // 清空历史记录
          console.log('===>清空历史记录', conversationID);

          conversationID && deleteConversationAllMessages({ conversationID, bindUserID });
        }else{
          console.log('onDeleteConversationNOSesion1', params)
        }

      }
      sessionList.value = sessionList.value.filter((item) => item.localSessionId !== params.localSessionId);
      onDbConversationDelete({ localSessionId: params.localSessionId });

    } else if (params.targetId) {
      if (params.deleHistory) {
        const session = sessionList.value.filter((item) => item.targetId === params.targetId)[0];
        if (session) {
          const conversationID = session.conversationID;
          const bindUserID = session.myOpenImId;
          // 清空历史记录
          conversationID && deleteConversationAllMessages({ conversationID, bindUserID });
        }else{
          console.log('onDeleteConversationNOSesion', params)
        }

      }
      sessionList.value = sessionList.value.filter((item) => item.targetId !== params.targetId);
      onDbConversationDelete({ targetId: params.targetId });

    }

    const index = Math.min(chattingSessionIndex, sessionList.value.length - 1);
    if (chattingSessionIndex >= 0 && index >= 0 && (params.targetId || params.localSessionId)) {
      setCurrentSession(sessionList.value[index]);
    }
  };

  // 身份卡移除
  const onDeleteCardConversations = (cards: string[]) => {
    console.log('====>onDeleteCardConversations', cards);
    const chattingSessionIndex = sessionList.value.findIndex((item) => item.localSessionId === chatingSession.value?.localSessionId);
    sessionList.value = sessionList.value.filter((item) => !cards.includes(item?.myCardId));
    if (chattingSessionIndex !== -1) {
      const index = Math.min(chattingSessionIndex, sessionList.value.length - 1);
      index >= 0 && setCurrentSession(sessionList.value[index]);
    }
  };

  const onGroupSettingUpdate = (changed: IRelationGroup) => {
    // 我的身份卡
    const myCard = chatingSession.value.myCardId;

    const { session, members, group, myGroupCards } = parseGroupRelation(changed);
    myGroupCards.forEach((item) => {
      if (myCard === item.openid) {
        const cardSession = getGroupCardSession(session, item);
        chatingSession.value.isTop = cardSession.isTop;
        chatingSession.value.isMute = cardSession.isMute;
        onDbConversationSetting(cardSession);
      }
    });

    const memberMap = allMembers.value.get(group.group);
    members.forEach((item) => {
      memberMap?.set(item.cardId, item);
    });
    ipcRenderer.invoke('im.session.member.update', { list: members });

    const groupInfo = allGroups.value.find((item) => item.group === group.group);
    groupInfo.name = group.name;
    groupInfo.attachment = group.attachment;
    groupInfo.owner = group.owner;
    groupInfo.sync_disk = group.sync_disk;
    groupInfo.disk_folder = group.disk_folder;
    groupInfo.total = group.total;
    groupInfo.updated = group.updated;
    insertOrUpdateGroup(group);

    sortSessions('onGroupSettingUpdate', false);
  };

  const onGroupMembersUpdate = async (params: { groupId: string, members: ConversationMemberToSave[] }) => {
    const { groupId, members } = params;
    // 删除群成员缓存
    ipcRenderer.invoke('im.session.member.delete', { sessionId: groupId });
    // 更新成员表
    ipcRenderer.invoke('im.session.member.update', { list: members });

    // 更新界面数据
    const membersMap = new Map<string, ConversationMemberToSave>();
    members.forEach((item) => membersMap.set(item.cardId, item));
    allMembers.value.set(params.groupId, membersMap);
    sessionList.value.forEach((item) => {
      if (item.targetId === params.groupId) {
        const myCard = membersMap.get(item.myCardId);
        if (!myCard) {
          return;
        }
        item.isTop = Boolean(myCard.stayOn);
        item.isMute = Boolean(myCard.noDisturb);
        item.group_type = myCard.group_type;
        console.log('===>sessionListitem', item, members, myCard);

        onDbConversationUpdate(item);
      }
    });

    if (params.groupId === chatingSession.value?.targetId) {
      loadGroupMemberLabels(chatingSession.value);
    }
  };

  const onDeleteGroupMembers = async (groupId: string, cards: string[]) => {
    if (groupId && cards?.length) {
      // 删除数据库界面
      ipcRenderer.invoke('im.session.member.delete', { sessionId: groupId, cards });
      // 删除界面缓存数据
      const groupMembers = allMembers.value.get(groupId);
      cards.forEach((card) => groupMembers?.delete(card));
    }
  };

  /** 会话右键移除 */
  const onRemoveSession = (session: ConversationToSave) => {
    session.removeSession = true;
    onDbConversationSetting(session);

    const index = sessionList.value.findIndex((item) => item.localSessionId === session.localSessionId);
    if (index === -1) {
      return;
    }
    // 只有一个对话的时候处理
    if (sessionList.value.length === 1) {
      sessionList.value = [];
      chatingSession.value = null;
      chatingMessages.value = [];
      isMySendVcard.value = false;
      return;

    }
    if (session.localSessionId === chatingSession.value?.localSessionId) {
      const nextCurIndex = index + 1 < sessionList.value.length ? index + 1 : index - 1;
      const currentSession = sessionList.value[nextCurIndex];
      setCurrentSession(currentSession);
    }
    sessionList.value.splice(index, 1);
  };

  const onGroupDeletedConfirm = (session: ConversationToSave) => {
    console.log('删除群聊', session);
    if (alertDeletedGroups.has(session.targetId)) {
      onDeleteConversation({ targetId: session.targetId });

    } else if (alertDeletedGroups.has(session.localSessionId)) {
      onDeleteConversation({ localSessionId: session.localSessionId });
    }

    alertDeletedGroups.delete(session.targetId);
    alertDeletedGroups.delete(session.localSessionId);

    setDissolutionOrKickOut('');
  };

  /**
   * 发送消息更新UI界面系列操作
   * @param sendItem
   */
  const onSendMessageUpdate = async (sendItem: MessageToSave) => {
    sendReadReceipt();
    const item = {
      conversationType: sendItem.conversationType,
      localSessionId: sendItem.localSessionId,
      targetId: sendItem.targetId,
      msgList: [sendItem],
      cleans: [],
    };
    onSendMsgUpdate([item]);
  };
  const onSendMsgUpdate = async (datas: ConversationMsgReceiveData[]) => {
    datas.forEach(async (item) => {
      // 更新正在聊天会话的消息
      if (chatingSession.value && item.msgList.length) {
        const cId = { 1: chatingSession.value.localSessionId, 3: chatingSession.value.targetId, 6: chatingSession.value.localSessionId }[chatingSession.value.conversationType as (1 | 3 | 6)];
        const mId = { 1: item.localSessionId, 3: item.targetId, 6: item.localSessionId }[item.conversationType as (1 | 3 | 6)];
        if (cId && cId === mId) {
          changeShouldScroll(true);
          MsgWrapper.onMessages(item.msgList, false);
          // TODO 性能优化，发送消息不用getComputeMessages，返回状态再执行
          chatingMessages.value = MsgWrapper.getComputeMessages();
          if (item.msgList[0]?.contentExtra?.data?.source === 'birthday_wish') {
            onCakeRain();
          }
          // 自己发送置底
          setTimeout(() => {
            msgEmit.emit('chatScrollDown');
          }, 0);
        }
      }
      if ([1, 6].includes(item.conversationType)) {
        const { localSessionId, msgList } = item;
        changeUIConversession(msgList, item);
      } else if (item.conversationType === 3) {
        const { targetId: groupId, msgList } = item;
        // 群聊，可能存在多个
        const dbConversations = await queryConversationByIds({ targets: groupId });
        if (dbConversations.length) {
          dbConversations.forEach((cItem) => {
            if(!cItem.inSession) return;
            changeUIConversession(msgList, cItem);
          });
        }
      }
      sortSessions('onSendMsgUpdate', false, false);
    });
  };
  const changeUIConversession = (msgList, cItem) => {
    // 设置最新消息
    const latestMsg = latestMsgData(msgList);
    const uiConversation = sessionList.value.find((data) => data.localSessionId === cItem.localSessionId);
    const conversation = uiConversation || cItem;
    conversation.latestMessage = latestMsg;
    console.log('===>latestMsg222', latestMsg);
    conversation.updateTime = Math.max(conversation.updateTime || 0, latestMsg?.sentTime || 0, conversation.createTime);
    if (conversation.startChatTime) conversation.startChatTime = null;
    if (!uiConversation) {
      onAddToSessionList(conversation, false, 'onSendMsgUpdate');
    }
  };
  /**
   * 只更新消息内容，如上传文件状态
   */
  const onSendMessageUpdateInfo = (sendItem: MessageToSave) => {
    MsgWrapper.onMessages([sendItem], false);
    chatingMessages.value = MsgWrapper.getComputeMessages();
  };
  /**
   * 消息排序
   */
  const sortMsg = () => {
    MsgWrapper.sortMessages();
    chatingMessages.value = MsgWrapper.getComputeMessages();
    setTimeout(() => {
      msgEmit.emit('chatScrollDown');
    });
  };
  /**
   * 发送消息回调更新消息发送状态
   */
  const updateSendMsg = (msg, from?) => {
    if (chatingSession.value && (msg.sessionType === chatingSession.value?.conversationType || msg.conversationType === chatingSession.value?.conversationType)) {
      chatingMessages.value.map((item) => {
        if(!item.msg?.messageUId && from === 'onConversationChanged') {
          if( Date.now() - item.msg?.sentTime > 4000) {
            item.msg.sentStatus = SentStatusMap[msg.status];
          }
          // return msg.contentExtra.data.text === item.msg?.contentExtra?.data?.text || msg.contentExtra.data.imgUrl === item.msg?.contentExtra?.data?.imgUrl
        } else if(item.msg.messageUId === msg.clientMsgID) {

          item.msg.sentStatus = SentStatusMap[msg.status]
        }
        // return item.msg.messageUId === msg.clientMsgID
      });
      if (from === 'sendMsg') {
        MsgWrapper.changeMsg(msg.tempId, msg.messageUId, SentStatusMap[msg.status]);
      } else if(msg.status === 3){
        MsgWrapper.changeMsgSendStatus(msg.messageUId || msg.tempId, SentStatusMap[msg.status]);
      }
      // 发送失败需要合并的消息拆出来
      if(msg.status === 3){
        chatingMessages.value = MsgWrapper.getComputeMessages();
      }
    }
    if (from !== 'onConversationChanged') {
      const idType = msg.clientMsgID ? 'clientMsgID' : 'tempId';
      const msgID = msg[idType]
      sessionList.value.forEach((item) => {
        if (item.latestMessage?.[idType] === msgID) {
           item.latestMessage = {
            ...item.latestMessage,
            sentStatus: SentStatusMap[msg.status]
          };
          return item;
        }
      });
    }
  };

  /**
   * 收到消息更新会话UI消息
   * @param datas
   * @param isReceive 是否是收到消息，在当前会话更新红点
   */
  const onReceiveRemoteMessages = async (datas: ConversationMsgReceiveData[], isReceive = true) => {
    const msgHandler = { 1: _onPrivateMessageReceived, 3: _onGroupMessageReceived, 6: _onPrivateMessageReceived };
    datas.forEach((item) => {
      // 更新消息未读数，及最新
      msgHandler[item.conversationType as (1 | 3 | 6)]?.(item)
        .then((res) => {
          console.log('====>msgHandlerres', res, isReceive);
          sortSessions('onReceiveRemoteMessages', true, res?.unread > 0);
          // 更新正在聊天会话的消息
          if (chatingSession.value && item.msgList.length) {
            const cId = { 1: chatingSession.value.localSessionId, 3: chatingSession.value.targetId, 6: chatingSession.value.localSessionId }[chatingSession.value.conversationType as (1 | 3 | 6)];
            const mId = { 1: item.localSessionId, 3: item.targetId, 6: item.localSessionId }[item.conversationType as (1 | 3 | 6)];
            if (cId && cId === mId) {
              if (isReceive) {
                changeShouldScroll(false);
              }
              MsgWrapper.onReceiveRemoteMessages(item.msgList);
              chatingMessages.value = MsgWrapper.getComputeMessages();
              if (item.msgList[0]?.contentExtra?.data?.source === 'birthday_wish') {
                onCakeRain();
              }
              if (isReceive) {
                receiveMsgReadReceipt();
              }
              if (res?.unread > 0) {
                if (scrollPx.value > 220) {
                  unredInfo.time = res.latestMsg.sendTime;
                  unredInfo.count += res.unread || 0;
                } else {
                  setTimeout(() => {
                    msgEmit.emit('chatScrollDown');
                  }, 0);
                }
              } else { // 自己发送置底
                setTimeout(() => {
                  msgEmit.emit('chatScrollDown');
                }, 0);
              }
            }
          }
        });
    });

  };
  const receiveMsgReadReceipt = (from?) => {
    if (!chatingSession.value) return;
    // 在会话页面才发送已读
    if (notReadMsgCount()) {
      sendReadReceipt();
    }
  };
  /**
   * 发送已读回执
   */
  const sendReadReceipt = async () => {
    if (chatingMessages.value.length === 0) return;
    // 发送已读判断内部关系才发送  @陈琪：解决红点问题，现在所有会话消息都发送mark
    // if (!conversationShouldRead(msgStore.chatingSession)) return;

    // const wrappers = msgStore.chatingMessages.flatMap((item) => (item.merged?.length ? [item, ...item.merged] : item));
    // 合并消息不发送已读未读
    const ids = [];
    const messages = chatingMessages.value.filter(({ msg }) => {
      // 只对接收到的消息发送已读回执，撤回的消息,已读的不发送
      if (chatingSession.value.myCardId === msg.contentExtra?.senderId || msg.isRead || msg.messageType === 2101 || msg.messageType === 111) {
        return false;
      }
      const hasRead = !msg.readTime || !msg.isRead;
      hasRead && ids.push(msg.messageUId);
      return hasRead;
    }).map((c) => c.msg);
    // 对未读消息发送已读回执
    console.log('====>sendReadReceipt', messages);
    if (messages.length) {
      const { code } = await sendReadReceiptMessage(chatingSession.value.conversationID, ids);
      code === 0 && messages.forEach((msg) => {
        msg.isRead = true;
      });
    }
  };

  const _onPrivateMessageReceived = async (received: ConversationMsgReceiveData) => {
    const { localSessionId, msgList } = received;
    // 单聊，只有一个会话
    const dbConversations = await queryConversationByIds({ ids: localSessionId });
    console.log('=====>_onPrivateMessageReceived', received, dbConversations);
    if (dbConversations.length) {

      const uiConversation = sessionList.value.find((item) => item.localSessionId === localSessionId);
      const conversation = uiConversation || dbConversations[0];
      if (received.conversationType === 6) {
        conversation.avatar = assistantMap[received.localSessionId].avatar;
        conversation.name = assistantMap[received.localSessionId].name;
      }
      const unreadInfo = getUnreadInfo(received, conversation.myCardId);
      console.log('=====>unreadInfo', unreadInfo);
      if (unreadInfo?.unread > 0) {
        // 是否设置消息免打扰
        const isMute = conversation?.isMute;
        console.error('dbConversations', dbConversations, unreadInfo);
        if (!isMute) {
          console.log('===>msgList', msgList, unreadInfo);

          useNetStore().isServerFinish && ringingMsg(unreadInfo.ringType);
        } else {
          unreadInfo.unread = 0;
        }
      }
      _updateConversationMessages(conversation, unreadInfo, '_onPrivateMessageReceived1');
      if (!uiConversation) {
        conversation.unreadCount = unreadInfo?.unread || 0
        conversation.unreadMentionedCount = unreadInfo?.mention || 0
        onAddToSessionList(conversation, false, '_onPrivateMessageReceived1');
      }
      return unreadInfo;
    }
    if (!msgList.length) {
      return false;
    }
    // 创建会话同时来两条消息，第一条创建会话还没完成第二条消息会再次创建。
    if (creatingConversation.get(localSessionId)) {
      creatingConversation.delete(localSessionId);
      return;
    }
    creatingConversation.set(localSessionId, localSessionId);
    // ! 会话不存在，保险起见，重新加载一下身份卡
    await loadAcountCards();

    // ! 这里取的消息 index 不重要，仅为获取 main peer 身份卡
    const msgItem = msgList[0];
    if (msgItem.conversationType === 6) {
      const openId = getOpenid();
      const assistantId = msgItem.sendID === openId ? msgItem.recvID : msgItem.sendID;
      const data = (await getAssistantInfoApi(openId, assistantId))?.data?.data;
      console.log('====>data', data);
      const msg = getAssistantsSessions(data);
      onAddToSessionList(msg, false, '_onPrivateMessageReceived assistantId');
      const unreadInfo = getUnreadInfo(received, openId);
      if (unreadInfo.unread > 0) {
        ringingMsg(unreadInfo.ringType);
      }
      _updateConversationMessages(msg, unreadInfo, '_onPrivateMessageReceived assistantId', false);
      creatingConversation.delete(localSessionId);
      return;
    }
    // 获取关系
    const { main, peer } = getCardMainPeer(msgItem.contentExtra.senderId, msgItem.contentExtra.receiverId);
    const res = await loadPrivateSession(main, peer, { ...received, dbConversations, type: '_onPrivateMessageReceived' });
    if (res) {
      console.log('【新单聊会话-新消息】', msgList);
      const unreadInfo = getUnreadInfo(received, res.session.myCardId);
      if (unreadInfo.unread > 0) {
        ringingMsg();
      }
      _updateConversationMessages(res.session, unreadInfo, '_onPrivateMessageReceived2', false);
      await onPrivateChattingFromContact(res, false);

      creatingConversation.delete(localSessionId);
      return unreadInfo;
    }
    // 关系不存在
    console.error('【未知单聊消息】', localSessionId, msgList);
    creatingConversation.delete(localSessionId);
    return false;
  };

  const _onGroupMessageReceived = async (received: ConversationMsgReceiveData) => {
    const { targetId: groupId, msgList } = received;
    // 群聊，可能存在多个
    const dbConversations = await queryConversationByIds({ targets: groupId });

    let shouldRing = false;
    let unreadObj = { unread: 0 };
    if (dbConversations.length) {
      dbConversations.forEach((item) => {
        if(!item.inSession) return;
        const unreadInfo = getUnreadInfo(received, item.myCardId);
        console.log('====>unreadInfo', JSON.stringify(unreadInfo), received);
        if (unreadInfo.unread > 0) {
          // if (chatingSession.value?.targetId === groupId && chatingSession.value?.myCardId === item.myCardId) {
          unreadObj = unreadInfo;
          // }
          if (!shouldRing) {
            // 是否设置消息免打扰 todo针对多身份群聊会话
            const isMute = item.isMute;
            // 是否锁定或熄屏
            shouldRing = useNetStore().isServerFinish && !isMute;
          }
        }
        const uiConversation = sessionList.value.find((uItem) => uItem.localSessionId === item.localSessionId);
        _updateConversationMessages(uiConversation || item, unreadInfo, '_onGroupMessageReceived');
        // 在当前会话加入群聊置为在会话中

        if (!uiConversation) {
          onAddToSessionList(item, false, '_onGroupMessageReceived');
        } else if (chatingSession.value?.conversationID === uiConversation.conversationID) {
          uiConversation.inSession = true;
        }

      });
      shouldRing && ringingMsg();
      !shouldRing && (unreadObj.unread = 0);
      return unreadObj;
    }

    if (!msgList.length) {
      return false;
    }
    // 创建会话同时来两条消息，第一条创建会话还没完成第二条消息会再次创建。
    if (creatingConversation.get(groupId)) {
      creatingConversation.delete(groupId);
      return;
    }
    creatingConversation.set(groupId, groupId);
    // 会话不存在添加会话,群聊信息更新,群聊成员更新
    const info = await getGroupSession(groupId);

    // 群存在多身份， 需要每个处理
    if (info?.session && info?.myGroupCards) {
      console.log('【新群会话-新消息】', msgList, info.session);
      if (!info.session.conversationID) {
        const conversationInfo = { conversationType: info.session.conversationType, targetId: info.session.targetOpenImId, myOpenImId: info.session.myOpenImId || '' };
        const { data } = await getConversationIDBySessionType(conversationInfo);
        info.session.conversationID = data;
      }
      const conversations = info.myGroupCards?.map((item) => getGroupCardSession(info.session, item));
      console.log('=====>_onGroupMessageReceived2', conversations);

      await insertConversations(conversations);

      conversations.forEach((item) => {
        // // 创建会话同时来两条消息，第一条创建会话还没完成第二条消息会再次创建。
        // const index = sessionList.value.findIndex((it) => it.localSessionId === item.localSessionId);
        // if (index > -1) return;
        const unreadInfo = getUnreadInfo(received, item.myCardId);

        _updateConversationMessages(item, unreadInfo, '_onGroupMessageReceived2', false);
        if (item.unreadCount > 0) {
          unreadObj = unreadInfo;
          ringingMsg();
        }
        console.log('=====>_onGroupMessageReceived2', unreadInfo, item);
        onAddToSessionList(item, false, '_onGroupMessageReceived2');
      });
      creatingConversation.delete(groupId);
      return unreadObj;
    }
    // 关系不存在
    console.error('【未知群消息】', groupId, msgList);
    creatingConversation.delete(groupId);
    return false;
  };

  /**
   * 更新会话未读数、@ 数量，最后一条消息，最后一条未读消息时间，工具方法
   *
   * @param conversation 会话
   * @param info 待更新信息，其值由 getUnreadInfo 返回
   * @returns
   */
  const _updateConversationMessages = (conversation: ConversationToSave, info: ConversationChangeData, origin?: string, hasSession = true) => {
    console.log('=====>_updateConversationMessages', conversation, info, hasSession, origin);
    // 添加好友消息和conversationchange比app_pairs先到，找不到会话上屏丢失消息
    // if (!conversation.inSession && info.unread > 0) {
    //   conversation.inSession = true
    // }



    // 如果没有会话需要添加的时候需要计算更新未读数。有会话是通过onconversationChange更新的。
    if (!hasSession) {
      conversation.unreadCount += info.unread;
    }
    const newSession = conversationChangedNewSession.get(conversation.conversationID);
    console.log('====>newSession', newSession);
    if (newSession) {
      conversationChangedNewSession.delete(conversation.conversationID);
      conversation.unreadCount = newSession.unreadCount;
      const latestMessage = safeParseJson(newSession.latestMsg);
      const msgItem = msgToUseChat(latestMessage);
      if (msgItem) {
        // 转换消息格式
        delete msgItem?.textElem;
        delete msgItem?.attachedInfoElem;
        delete msgItem?.receipts;
        const lastMsg = getMsgToStore(msgItem);
        if (lastMsg) { 
          conversation.latestMessage = lastMsg;
          conversation.updateTime = lastMsg.sentTime;
        }
      }
    } else if (info.latestMsg) {
      // 设置最新消息
      conversation.latestMessage = info.latestMsg;
      conversation.updateTime = info.latestMsg.sentTime;
      if (conversation.startChatTime) conversation.startChatTime = null;
    }
    // 存在未读消息，当会话被移除界面时，需要重新加入
    if (conversation.unreadCount) {
      conversation.removeSession = false;
      conversation.inSession = true
    }
    // 更新 @ 数量
    conversation.unreadMentionedCount ? conversation.unreadMentionedCount += info.mention : conversation.unreadMentionedCount = info.mention;

    // 如果在当前会话，则清零计数
    if (conversation.localSessionId === chatingSession.value?.localSessionId && notReadMsgCount()) {
      conversation.unreadCount = 0;
      conversation.unreadMentionedCount = 0;
    }

    console.log('=====>conversation', conversation, hasSession, origin, notReadMsgCount(), mainWindowIsFocused.value, router.currentRoute,localStorage.getItem("curMenuValue"));
    // 更新数据库
    onDbConversationMessageUpdate(conversation, `${origin}>_updateConversationMessages`);
    return conversation;
  };

  // 消息撤回
  const onMessageRecalled = (message) => {
    console.log('====>onMessageRecalled', message);
    // 会话更新
    sessionList.value.forEach((it) => {
      if (it.inSession && it.latestMessage?.messageUId === message.messageUId) {
        it.latestMessage.messageType = 2101;
        // 更新数据库
        delete it.latestMessage?.content;
        delete it.latestMessage?.attachedInfo;
        delete it.latestMessage?.receipts;
        delete it.latestMessage?.notificationElem;
        onDbConversationMessageUpdate(it, `${origin}>onMessageRecalled`);
      }
    });
    if (chatingSession.value?.conversationID === message.conversationID) {
      MsgWrapper.recallMessage(message);
      chatingMessages.value = MsgWrapper.getComputeMessages();
    }
    // 检查是否有引用撤回的消息
    checkRcallMsg(message);

    // 撤回消息暂时没有msg.contentExtra 没法处理
    // if (message.contentExtra.contentType === "image"){
    //   // 通知图片预览组件撤回消息
    //    console.log(message.contentExtra, "message.contentExtra.data?.imgUrl");
    //    ipcRenderer.invoke("preview-recall", JSON.stringify({ url: message.contentExtra.data?.imgUrl }));
    // }
  };

  /**
   * 场景红点消息，更新会话列表
   * @param msg:{localSessionId,targetId,conversationType}
   * @param scene 场景
   * @param flag：true false 场景是否红点
   */
  const onConversationSceneChange = (msg, scene, flag = true) => {
    const idKey = [1, 6].includes(msg.conversationType) ? 'localSessionId' : 'targetId';
    const myCardId = msg.contentExtra?.data?.card_id || msg.myCardId;
    try {
      sessionList.value.forEach((item) => {
        if (item[idKey] === msg[idKey]) {
          if ((flag && item.myCardId !== myCardId) || (!flag && item.myCardId === myCardId)) { // 自己身份卡触发的红点不处理，只有更新自己身份触发的清除红点
            item.sceneRedDotObj[scene] = flag;
            msg.sceneRedDot = JSON.stringify(item.sceneRedDotObj);
            item.sceneRedDot = JSON.stringify(item.sceneRedDotObj);
            onDbConversationSceneRedDot(msg);
          }
        }
      });
    } catch (error) {
      console.error(error);
    }
  };

  /**
   * 蛋糕雨
   */
  const showCakeRain = ref(false);
  /**
  * 蛋糕雨触发
  */
  const onCakeRain = () => {
    if (showCakeRain.value) return;
    showCakeRain.value = true;
    setTimeout(() => {
      showCakeRain.value = false;
    }, 9000);
  };

  /**
   *  小秘书点击打开组织认证弹窗
   */
  const orgAuthDialogVisible = ref(false);
  const orgAuthDetail = ref({ region: '', teamId: '', orgType: '' });
  const changeOrgAuthDialog = (flag, detail) => {
    orgAuthDialogVisible.value = flag;
    orgAuthDetail.value = { ...detail };
  };

  /**
   * 消息的附件等信息更新，刷新消息列表。
   * 消息本身变化，不通过此列表刷新
   */
  const refreshMessages = (msg?: MessageToSave) => {
    if (msg) {
      const index = chatingMessages.value.findIndex((it) => it.messageUId === msg.messageUId);
      if (index !== -1) {
        chatingMessages.value[index].msg = msg;
      }
    }

    chatingMessages.value = MsgWrapper.getComputeMessages();
  };
  /**
   * 更新某一条消息
   *
   */
  const refreshMessageByNeedUpdateMessageUId = ({ messageUId, scene }) => {
    console.log('更新某一条消息', messageUId, scene);
    if (scene === 8017 && messageUId) {
      needUpdateConsultMessageUId.value = messageUId;
      return;
    }
    if ([19036, 51036,5066, 16036, 14036].includes(scene) && messageUId) {
      needUpdateRefreshMessageUId.value = messageUId;
      return;
    }
    if ([5076, 5077, 5078, 5079].includes(scene)) {
      needUpdateRefreshMessageUId.value = messageUId;
      return;
    }

    if (scene === 8016 && messageUId) {
      needUpdateOrderMessageUId.value = messageUId;
      return;
    }
    if (scene === 31 && messageUId) {
      needUpdatePushMessageUId.value = messageUId;
      return;
    }
  };

const changeNeedUpdateOrderMessageUId = (messageUId) => {
  needUpdateOrderMessageUId.value = messageUId;
}
// 店铺助手消息按钮状态更新
const onShopMsgUpdate = (msg: MessageToSave) => {
  const { scene} = msg.contentExtra;
  if (scene === 100012) {
    const TeamId =  msg.contentExtra.data?.teamId
    // 倒序查找优化：从最后一条消息往前遍历
    for (let i = chatingMessages.value.length - 1; i >= 0; i--) {
      const v = chatingMessages.value[i].msg;
      if (v.contentExtra?.scene === 100001 &&
          v.contentExtra?.data?.store.teamId === TeamId) {
          changeNeedUpdateOrderMessageUId(v.messageUId);
          break;
      }
    }
    return;
  }
  if (scene === 100017) {
    const Id = msg.contentExtra.data?.id
    // 倒序查找优化：从最后一条消息往前遍历
    for (let i = chatingMessages.value.length - 1; i >= 0; i--) {
      const v = chatingMessages.value[i].msg;
      if (v.contentExtra?.scene === 100004 &&
          v.contentExtra?.data?.category?.id === Id) {
          changeNeedUpdateOrderMessageUId(v.messageUId);
          break;
      }
    }
  }
}
  /**
   * 获取消息发送者信息
   * @param msg
   * @returns
   */
  const getMsgSender = (msg: MessageToSave) => {
    const sender = chatingSessionMembers?.value?.get(msg?.contentExtra?.senderId);
    return sender;
  };

  // 获取消息发送者是否有群身份
  const getSenderLabel = (msg: MessageToSave) => {
    const conversationMembers = allMembers.value.get(msg.targetId);

    const senderId = msg.contentExtra?.senderId;
    const sender = conversationMembers?.get(senderId);
    return Boolean(sender?.label_id);
  };

  // 获取消息发送者群身份名称
  const getSenderLabelName = (msg: MessageToSave) => {
    const conversationMembers = allMembers.value.get(msg.targetId);

    const senderId = msg.contentExtra?.senderId;
    const sender = conversationMembers?.get(senderId);
    const labels = groupLabels.value.get(msg.targetId);
    return labels?.get(sender?.label_id) || '';
  };

  /**
  * 获取群成员列表群身份名称
  * @param label_id
  * @returns
  */
  const getMemberLabelName = (label_id) => {
    const labels = groupLabels.value.get(chatingSession.value?.targetId);
    return labels?.get(label_id) || '';
  };
  // 获取当前群聊群身份列表
  const getLabelList = () => groupLabels.value.get(chatingSession.value?.targetId);

  /**
   * 获取消息发送者头像
   * @param msg
   * @returns
   */
  const getMsgSenderAvatar = (msg: MessageToSave) => getMsgSender(msg)?.avatar ?? '';

  /**
   * 获取当前会话成员信息
   * @param card 身份卡
   * @returns
   */
  const getChatingSessionMember = (card: string) => chatingSessionMembers.value?.get(card);

  const getMyGroupRole = () => {
    if (!chatingSession.value || chatingSession.value?.conversationType !== 3) {
      return null;
    }

    const card = chatingSession.value?.myCardId;
    const mycardInfo = chatingSessionMembers.value?.get(card);
    return mycardInfo?.roles ?? null;
  };

  /**
   * 判断是否当前群的管理员或者群主
   * @returns
   */
  const isGroupAdminOrOwner = () => {
    const roles = getMyGroupRole();
    if (!roles) {
      return false;
    }

    return ['ADMIN', 'OWNER'].some((r) => roles.includes(r));
  };

  /**
   * 获取当前会话成员名称
   * @param card 身份卡
   * @returns
   */
  const getChatingSessionMemberName = (card: string) => {
    const member = chatingSessionMembers.value?.get(card);
    return member?.staffName || member?.nickname;
  };

  /**
   * 获取消息发送者名称
   * @param msg
   * @returns
   */
  const getMsgSenderName = (msg: MessageToSave) => {
    const sender = getMsgSender(msg);
    return sender?.staffName ?? sender?.nickname;
  };

  // 群聊信息更新
  const updateGroupInfo = (group) => {
    if (group) {
      const index = allGroups.value.findIndex((gp) => gp.group === group?.group);
      console.log('===>updateGroupInfoindex', index);

      index === -1 ? allGroups.value.push(group) : allGroups.value.splice(index, 1, group);
      insertOrUpdateGroup(group);
    }
  };
  // 查询群信息
  const getGroupInfo = (groupId: string) => allGroups.value.find((g) => g.group === groupId) as GroupToSave;

  /**
   *  客服弹窗数据采用本地存储， 只有关闭或者发送才会关闭弹窗
   *
   */
  const setConsultInfo = (data: {
    id: any,
    info: any
  }) => {
    const consultInfo = getConsultInfo() || {};
    console.log('setConsultInfo:', consultInfo);
    consultInfo[data.id] = data.info;
    consultInfoPop.value[data.id] = data.info;
    console.log('consultInfoPop', consultInfoPop);
    return window.localStorage.setItem('consultCustomerService', JSON.stringify(consultInfo));
  };

  // 添加咨询客服浮窗数据
  const getConsultInfo = (id?) => {
    let consultInfo = {};
    const localInfo = window.localStorage.getItem('consultCustomerService');
    try {
      consultInfo = localInfo ? JSON.parse(localInfo) : {};
      consultInfoPop.value = consultInfo;
    } catch (e) {
      console.log('consultInfo error ', e);
    }

    // 指定查询否则返回所有数据
    if (id) {
      return consultInfoPop.value[id] ? consultInfoPop.value[id] : '';
    }
    return consultInfoPop.value;
  };

  // 删除咨询客服浮窗数据
  const delConsultInfo = (id) => {
    let consultInfo = {};
    try {
      consultInfo = JSON.parse(window.localStorage.getItem('consultCustomerService'));
    } catch (e) {
      console.log('consultInfo error ', e);
    }
    if (id && consultInfo[id]) {
      delete consultInfo[id];
      delete consultInfoPop.value[id];
      window.localStorage.setItem('consultCustomerService', JSON.stringify(consultInfo));
    }

    return consultInfo;
  };
  /**
   * 更新openIM会话信息中的某一条消息数据
   */
  const updateLocalMsgToOpenIM = async ({ conversationID, messageUId, ex }) => {

    // console.error('msg', conversationID, messageUId, ex);
    const res = await findMsgList({ conversationID, messageUIds: [messageUId] });
    if (res.code === 0) {
      const updateMsgData = res.result?.[0]?.messageList[0];
      updateMsgData.ex = ex;
      const conversation = { targetId: updateMsgData.recvID, myOpenImId: updateMsgData.sendID, conversationType: updateMsgData.sessionType };
      // console.error('updateMsgData', conversation, updateMsgData);

      await updateMessage(conversation, updateMsgData, {});
    }
  };
  /**
   * 设置会话列表滚动距离
   */
  const setScrollLength = () => {
    scrollLength.value += 20;
  };
  /**
   * 通过会话判断是否是助手类型会话
   */
  const isAssistantByConversation = (conversation) => conversation.conversationType === 6;

  /**
   * 触发重新登录后检查当前会话是否有新消息
   */
  const chattingMsgRefresh = async () => {
    if (chatingSession.value?.localSessionId) {
      firstOpenImHistory(chatingSession.value);
    }
  };
  const currentTime = ref(Date.now());
  const currentTimeDiff = ref(0);
  const getSeverTime = async () => {
    // cfgCommon();
    const res = await getCurrentServerTime();
    currentTime.value = res.data ? res.data : Date.now();
    currentTimeDiff.value = res.diff ? res.diff : 0;
    console.log('===>es.data : Date.now()', res.data, Date.now());
  };
  /**
   * 记录当前时间,每分钟更新UI上的时间
   * clear:true 清除timer
   */
  let timer = null;
  const setCurrentTime = (clear) => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
    if (!clear) {
      timer = setInterval(() => {
        getSeverTime();
      }, 60 * 1000);
    }
  };

  /**
   * 获取选中的消息id
   */
  const selectedIDList = ref([]);
  const getselectedIDList = () => {
    selectedIDList.value = chatingMessages.value.filter((msg) => msg.selected).map((item) => item.msg.messageUId);
  };
  /**
   * 复原之前选中的
   */
  const setselectedIDList = () => {
    if (selectedIDList.value.length === 0) return;
    selectedIDList.value.forEach((val) => {
      for (const key in chatingMessages.value) {
        if (chatingMessages.value[key].msg.messageUId === val) {
          chatingMessages.value[key].selected = true;
          return;
        }
      }
    });
  };
  // 引用回复消息，编辑
  const onReplyEditing = (msg?: MessageToSave) => {
    referMsg.value = msg;
  }

  // 关闭引用消息模式（发送或者点击关闭按钮取消）
  const onCleanReferMsg = () => {
    referMsg.value && (referMsg.value = null);
  }

  const checkRcallMsg = (msg) => {
    if(!referMsg.value) return;
    if(msg.messageUId === referMsg.value.messageUId){
      referMsg.value.messageType = 2101
    }
  }
  return {
    isMySendVcard,
    currentTime,
    currentTimeDiff,
    setCurrentTime,
    exclusiveNames,
    cancelHighlightedMsg,
    highlightedSearchedId,
    showGroupRemoveAlert,
    alertDeletedGroups,
    isFirstPageForChatingMessages,
    unredInfo,
    groupLabels,
    sessionList,
    sessionListActive,
    commonSessionList,
    latestConversation,
    commonChatActive,
    commonListCounts,
    groupTab,
    activeGroupType,
    chatingSession,
    chatingMessages,
    imHasHistoryMore,
    allMembers,
    allGroups,
    chatingSessionMembers,
    chattingGroup,
    myChatingGroupCards,
    showNotInGroup,
    showNotFriends,
    isAssistantSession,
    isDissolutionOrKickOut,
    needUpdateRefreshMessageUId,
    needUpdateConsultMessageUId,
    needUpdateOrderMessageUId,
    needUpdatePushMessageUId,
    isAssistantByConversation,

    powerShutdown,
    setPowerShutdown,
    setMainWindowIsFocused,

    scrollLength,
    setScrollLength,

    setDissolutionOrKickOut,
    loadRemoteSessions,
    onSyncServerFinish,
    loadSessionList,
    sortSessions,
    setCurrentSession,
    updateLeftbarBadge,
    updateAssistantConversation,
    onAddToSessionList,
    getMyCardTeam,
    getPairGroup,
    onSwitchGroup,
    commonChatClick,

    loadGroupMemberLabels,
    getSenderLabel,
    getSenderLabelName,
    getMemberLabelName,
    getLabelList,
    getMsgSender,
    getMsgSenderName,
    getMsgSenderAvatar,
    getChatingSessionMember,
    getChatingSessionMemberName,
    isGroupAdminOrOwner,

    getGroupInfo,
    updateGroupInfo,
    loadOpenIMMsg,
    chattingMsgRefresh,
    sendReadReceipt,
    // loadMessageMoreToUnRead,
    loadMessageMoreToMentionMe,
    gotoMessage,
    gotoChatingSessionMessage,

    changeMsgRead,
    onSendMessageUpdate,
    onSendMessageUpdateInfo,
    onReceiveRemoteMessages,
    onMessageRecalled,
    refreshMessages,
    checkPeerInfo,

    onConversationChanged,
    onDeleteConversation,
    onDeleteCardConversations,
    onQuitGroupConversation,
    onDeleteMessageFromList,
    onRemoveSession,
    onGroupDeletedConfirm,
    onGroupSettingUpdate,
    onGroupMembersUpdate,
    onDeleteGroupMembers,
    onGroupKickedAlert,
    onConversationSceneChange,
    onCakeRain,
    refreshMessageByNeedUpdateMessageUId,
    showCakeRain,
    orgAuthDialogVisible,
    orgAuthDetail,
    changeOrgAuthDialog,
    // onGroupEnterMsgSync,
    onPrivateChattingFromContact,

    onStartChat,
    consultInfoPop,
    setConsultInfo,
    getConsultInfo,
    delConsultInfo,

    updateMsgDetailById,
    updateLocalMsgToOpenIM,
    updateSendMsg,
    sortMsg,
    getfirstOpenImHistoryFail,
    getselectedIDList,
    setselectedIDList,

    setScrollHeight,
    scrollPx,
    scrollPosition,
    shouldScroll,
    changeShouldScroll,

    referMsg,
    onReplyEditing,
    onCleanReferMsg,
    changeNeedUpdateOrderMessageUId,
    onShopMsgUpdate,
  };
});
