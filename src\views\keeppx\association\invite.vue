<template>
  <home title="申请加入">
    <template #content>
      <div class="background">

        <!-- <div style="color: red">  {{accountStore.userInfo}}dd</div> -->
        <div v-if="organizeData && !accountStore.userInfo" class="invite">
          <div class="top">
            <img v-if="organizeData.logo" v-lazy="organizeData.logo" class="top-img" />

            <span class="top-title">
              {{ organizeData.team }}
            </span>
            <span class="top-tip">你正在申请加入该组织</span>
          </div>
          <div class="con mt-24px">
            <span class="con-item">
              <span class="text">已有账号，我要登录</span>
              <t-button theme="primary" class="log-btn" @click="goLogin">登录</t-button>
            </span>
            <span class="con-item">
              <span class="text">没有账号，我要注册</span>
              <t-button theme="primary" class="reg-btn" variant="outline" @click="goRegister">注册</t-button>
            </span>
          </div>
        </div>
        <!-- 申请入会 -->
        <div v-if="organizeData && accountStore.userInfo && accountStore.userInfo.account_mobile && !organizeData.no_activate_list.length" class="apply">
          <!-- <div class="apply-header">
            <span style="margin-left: 16px; padding-top: 7px">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M14.4 16.8002L9.59998 12.0002L14.4 7.2002"
                  stroke="#516082"
                  stroke-width="1.68"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </span>

            <span class="text">申请入会</span>
          </div> -->
          <!-- v-show="organizeData.status !== 2"  -->
          <div class="apply-tip">
            <span class="text">你正在申请加入：</span>
            <span class="value"> {{ organizeData?.exclusive_name || organizeData?.team }}</span>
          </div>
          <!-- <div v-if="false" class="apply-step">
            <div class="banner">
              <div
                :class="{
                  circle: true,
                  circle_working: [0].includes(organizeData.status),
                  circle_finished: [1, 2, 3].includes(organizeData.status),
                }"
              ></div>
              <div
                :class="{
                  line: true,
                  line_working: [1, 3].includes(organizeData.status),
                  line_finished: [2].includes(organizeData.status),
                }"
              ></div>
              <div
                :class="{
                  circle: true,
                  circle_working: [1, 3].includes(organizeData.status),
                  circle_finished: [2].includes(organizeData.status),
                }"
              ></div>
              <div
                :class="{
                  line: true,
                  line_finished: [2].includes(organizeData.status),
                }"
              ></div>
              <div
                :class="{
                  circle: true,
                  circle_finished: [2].includes(organizeData.status),
                }"
              ></div>
            </div>
            <div class="steps">
              <div class="li">
                <div class="counter">01</div>
                <div class="text">提交申请</div>
              </div>
              <div class="li">
                <div class="counter">02</div>
                <div class="text">政府单位审核</div>
              </div>
              <div class="li">
                <div class="counter">03</div>
                <div class="text">加入成功</div>
              </div>
            </div>
          </div> -->

          <!-- <div class="fix-step" v-if="false">
            <div class="line-boxx">
              <div
                :class="{
                  circle: true,
                  circle_working: [0].includes(organizeData.status),
                  circle_finished: [1, 2, 3].includes(organizeData.status),
                }"
              ></div>
              <div
                :class="{
                  line: true,
                  line_working: [1, 3].includes(organizeData.status),
                  line_finished: [2].includes(organizeData.status),
                }"
              ></div>
              <div
                :class="{
                  circle: true,
                  circle_working: [1, 3].includes(organizeData.status),
                  circle_finished: [2].includes(organizeData.status),
                }"
              ></div>
              <div
                :class="{
                  line: true,
                  line_finished: [2].includes(organizeData.status),
                }"
              ></div>
              <div
                :class="{
                  circle: true,
                  circle_finished: [2].includes(organizeData.status),
                }"
              ></div>
            </div>
            <div class="num-box">
              <div class="nt">
                <div class="number">01</div>
                <div class="text">提交申请</div>
              </div>
              <div class="nt">
                <div class="number">02</div>
                <div class="text">政府单位审核</div>
              </div>
              <div class="nt">
                <div class="number">03</div>
                <div class="text">加入成功</div>
              </div>
            </div>
          </div> -->
          <!-- 暂时先设置为false -->
          <!-- start -->
          <div v-if="[0].includes(organizeData.status)" class="apply-first">
            <FormDesignComp
              v-if="step === 1"
              :data="applyData"
              :industry-data="industryListData"
              :member-level-data="memberLevelListData"
              :size-data="sizeListData"
              :organize-type="optionsOrganizeType"
              @reload="onGetLinkDetail"
              @onSubmit="onFormDesignSubmit"
            />

            <SetGroupComp v-else @onSubmit="onSetGroupSubmit" :data="subParams" :groupId="group_id"/>

          </div>
          <template v-if="[1, 2, 3].includes(organizeData.status)">
            <!-- 平台无分组的情况 -->
            <template v-if="organizeData?.association_group_count < 1">
              <!-- v-if="[1, 2, 3].includes(organizeData.status)" -->
              <div class="apply-second">
                <div v-if="organizeData.status === 1" class="info">
                  <img class="info-img" src="@/assets/img/icon_time.svg" />
                  <!-- <img src="@/assets/img/icon_success-linear.png"> -->
                  <span class="info-status">等待审核</span>
                  <span class="info-tips">已提交申请，请等待处理</span>
                </div>
                <div v-if="organizeData.status === 2" class="info">
                  <img class="info-img" src="@/assets/img/icon_success.svg" />
                  <span class="info-status">加入成功</span>
                  <span class="info-tips">你已成功加入 {{ organizeData?.exclusive_name || organizeData?.team }}</span>
                </div>
                <div v-if="organizeData.status === 3" class="info">
                  <img class="info-img" src="@/assets/img/icon_warning.svg" />
                  <span class="info-status">申请被驳回</span>
                  <span class="info-tips"> {{ $filters.isPeriodEmpty(organizeData.content) }}</span>
                  <span class="info-btn">
                    <div class="n-btn" @click="goEdit">前往修改</div>
                    <!-- <t-button theme="primary" style="background: #4d5eff; border-color: #4d5eff" @click="goEdit"
                      >前往修改</t-button
                    > -->
                  </span>
                </div>


                <div v-show="!isRinkol &&!isRingkolDesktop&&!isInMiniApp" class="footer">
                  <div class="toUpload">
                    <span class="toUpload-left" >
                      <img class="img" src="@/assets/img/linker_name.png" />
                    </span>
                    <span class="toUpload-right">
                      <span class="btn" v-if="deviceInfo.type === 'pc'">
                        <a href="ringkol://ringkol.com/memberIndex/member_number">打开另可</a>
                      </span>
                      <span class="btn" v-else>
                        <a href="https://ringkol.com/mobile/downloadCenter">打开另可</a>
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </template>
            <template v-else>
              <SetGroupComp @onSubmit="onSetGroupSubmit" :data="subParams" :groupId="group_id"/>
            </template>
          </template>
          <!-- end -->

          <!-- <div class="apply-two  mt-12px"></div>
                <div class="apply-three  mt-12px"></div> -->
        </div>

        <!-- 已经入会 未激活 -->
        <div v-if="organizeData && accountStore.userInfo && accountStore.userInfo.account_mobile && organizeData.status == 2 && organizeData.no_activate_list && organizeData.no_activate_list.length" class="apply">
          <div v-if="[1, 2, 3].includes(organizeData.status)" class="apply-second2">
            <div v-show="organizeData.status === 2" class="info">
              <img class="info-img" src="@/assets/img/member_monkey.png" />
              <!-- <span class="info-status">你的信息已存在：
                <span style="color: #4D5EFF">
                  <span v-for="(item,index) in organizeData.no_activate_list" :key="index">
                    {{item.team_name}}
                    <span v-if="index < organizeData.no_activate_list.length - 1">，</span>
                  </span>
                </span>
              </span> -->
              <!-- <div class="cro"> 点击”前往激活”按钮可激活【{{organizeData?.exclusive_name || organizeData.team}}】的平台身份</div> -->
              <div style="text-align: center;">
                <span class="info-status" style="color: #516082">你的信息已存在：
                </span>
                <span style="color: #4D5EFF">
                  <span v-for="(item,index) in organizeData.no_activate_list" :key="index">
                    {{item.team_name}}
                    <span v-if="index < organizeData.no_activate_list.length - 1">，</span>
                  </span>
                  <span style="color: #516082">。点击”前往激活”按钮可激活【{{organizeData?.exclusive_name || organizeData.team}}】的平台身份</span>
                </span>
                <div class="cro"> </div>
              </div>
              <div class="btn mt-15px">
                <t-button theme="primary" style="background: #4d5eff; border-color: #4d5eff" @click="goActivePage">
                  前往激活
                </t-button>
              </div>
            </div>
            <div v-show="!isRinkol &&  !isRingkolDesktop&&!isInMiniApp" class="footer">
              <div class="toUpload">
                <span class="toUpload-left" >
                  <img class="img" src="@/assets/img/linker_name.png" />
                </span>
                <span class="toUpload-right">
                  <span class="btn" v-if="deviceInfo.type === 'pc'">
                    <a href="ringkol://ringkol.com/memberIndex/member_number">打开另可</a>
                  </span>
                  <span class="btn" v-else>
                    <a href="https://ringkol.com/mobile/downloadCenter">打开另可</a>
                  </span>
                </span>
              </div>
            </div>
          </div>
          <!-- <div class="apply-two  mt-12px"></div>
                <div class="apply-three  mt-12px"></div> -->
        </div>


        <div v-if="isLose" class="lose">
          <img src="@/assets/img/icon_warning.svg" alt="" class="icon" />
          <div class="title">链接失效</div>
          <div class="tipss">当前链接已失效</div>
        </div>

        <!-- <div v-if="accountStore?.userInfo && !accountStore.userInfo.account_mobile" class="lose">
          <img src="@/assets/img/icon_warning.svg" alt="" class="icon" />
          <div class="title">未绑定手机号</div>
          <div class="tipss p-20px">该账号未绑定手机号，请前往另可客户端【偏好设置】中配置绑定手机号</div>
        </div> -->
        <set-phone v-if="accountStore?.userInfo && !accountStore.userInfo.account_mobile" ref="setPhoneDom"   @confirm="getPhone" />

      </div>
    </template>

  </home>
  <TipAnualFeeModal ref="tipAnualFeeModalRef" :tip-show-text="tipShowText"/>
  <join-contact-modal ref="joinContactModal" :originType="originType?.Association" @on-success="onApplySuccess"/>
  <successPage v-if="organizeData && accountStore.userInfo && accountStore.userInfo.account_mobile && !organizeData.no_activate_list.length&&organizeData?.status === 2&&isInMiniApp" :organizeData="organizeData"></successPage>

</template>

<script setup lang="ts">
import setPhone from '@/views/keeppx/account/components/setMemberPhone.vue'
import home from '@/views/keeppx/homeIndex.vue';
import { useRoute, useRouter } from 'vue-router';
import { computed, ref, type Ref, onMounted, onBeforeUnmount } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { errorHandlerFilter, getResponseResult, priceDivisorShow } from '@/utils/myUtils';
import {
  GetMemberApplyDetailAxios,
  applyMemberCreateAxios,
  // getIndustryListAxios,
  getLinkDetailAxios,
  getMemberJobsListAxios,
  getMemberSettingAxios,
  getTeamsSizeListAxios,
  checkDelegate,
} from '@/api/association/association';
import { classifyTree } from '@/api/business/index';
import SetGroupComp from '@/views/keeppx/association/components/set-group-comp.vue'
import TipAnualFeeModal from '@/views/keeppx/modal/tipAnualFee.vue';

import FormDesignComp from '@/views/keeppx/association/components/form-design-comp.vue';
import { getProfile, bindAccount } from '@/api/account/login';
import { useAssociationStore } from '@/stores/association';
import { formDiff } from '@/components/free-from/utils';
import memberConst from '@/components/free-from/runtime/constants/associationConst';
import lodash from 'lodash';
import { useAccountStore } from '@/stores/account';
import to from 'await-to-js';
import { JsBridgeReady } from '@/utils/preload';
import {deviceType} from "@/utils/device";
import { AxiosError } from 'axios';
import JoinContactModal from '@/views/keeppx/modal/join-contact-modal.vue'  // <-- 新增引入
import {
  originType
} from "@/views/keeppx/account/constant";
import successPage from '@/views/keeppx/org/success.vue';
import { handleWeixinBridgeReady } from '@/views/square/utils.ts';
const isInMiniApp = ref(false);
handleWeixinBridgeReady(() => {
  isInMiniApp.value = window.__wxjs_environment === 'miniprogram';
});

const accountStore = useAccountStore();
const isRinkol = accountStore.isRingkol;
const isRingkolDesktop = accountStore.isRingkolDesktop;


const associationStore = useAssociationStore();
const route = useRoute();
const router = useRouter();
const current = 1;

const link: Ref<any> = ref('');
const group_id: Ref<any> = ref(0);
const organizeData: Ref<any> = ref(null);
// const tabType: Ref<any> = ref([])

const goActivePage = () => {
  router.push({
    name: 'commonActive',
    query: {
      type: 'association',
      teamId: organizeData.value.teamId
    }
  })
}

const validUser = () => {
  return new Promise((resolve, reject) => {
    console.log('member validUser')
    getProfile()
    .then((res) => {
      console.log('77777777777777777777777777777',res);
      accountStore.setUserInfo(res);
      resolve(res);
    })
    .catch((err) => {
      console.log('err?', err);
      //    if(err && [401, 403].includes(err.response.status)) {
      //         localStorage.clear();
      //         goLogin();
      //    }
      if (err && err.response.status) {
        console.log(router)
        errorHandlerFilter(err.response.status, err.response, router);
        onGetLinkDetail();
        accountStore.setUserInfo(null);
      }
      reject(err);
    });
  })
};

const onApplySuccess = () => {
  organizeData.value.association_group_count = 0 // 社群和政企比较特殊
  organizeData.value.status = 1;
}

// 前往修改
const subData = ref(null)
const goEdit = () => {
  // onGetSettings()
  onGetMemberApplyDetailAxios(organizeData.value.id).then((res: any) => {
    const subData = res.submit_data;
    if (subData) {
      // 提交申请字段
      subData.value = subData;
      console.log(subData);
      onGetSettings(true).then(() => {
        if (res.type === 1) {
          // 单位
          applyData.value.team_form = formDiff(subData.free_form, applyData.value?.team_form);
          console.log(applyData.value.team_form);
        } else if (res.type === 2) {
          // 个人
          applyData.value.personal_form = formDiff(subData.free_form, applyData.value?.personal_form);
          console.log(applyData.value.personal_form);
        }
        //    return;
        applyData.value.type = res.type;
        organizeData.value.status = 0;
      })
    }
  });
};

const deviceInfo = ref(deviceType(navigator.userAgent))
const onInit = async () => {
  await JsBridgeReady();
  await validUser();
  link.value = route.query.link;
  group_id.value = route.query.group_id;
  if (!link.value) {
    MessagePlugin.error('缺失link');
  } else {
    //   if(accountStore.userInfo) {

    //   }
    onGetLinkDetail();
  }
};

const subParams: any = ref(null);

const onFormDesignSubmit = async (e: any) => {
  console.log(e)

  // 这里还要判断一下是否有平台分组
  subParams.value = { params: e, detail: organizeData.value, teamId: associationStore.teamId };

  // 判断联系人分组情况
  if(subParams.value.params?.submit_data?.type === 1) {
    // 判断是否申请联系人
    const [err, res]:any = await to(checkDelegate({team_id:  applyData.value?.teamId, relation_team_id: e?.submit_data?.teamId, telephone: e?.submit_data?.telephone }))
    if(err) {
      MessagePlugin.error(err?.message);
      // loading.value = false;
      return;
    }
    // const { data } = res;
    // console.log(data);
    if(res?.code === 0 && res?.data?.apply_contact) {
      joinContactModal.value?.onOpen({
        team_name: e?.submit_data?.team_name,
        name: res?.data?.name,
        avatar: res?.data?.avatar,
        phone : e?.submit_data?.telephone,
        main_body_id: res?.data?.main_body_id,
      });
      return ;
    }
  }


  if(organizeData.value?.association_group_count > 0) {
    step.value = 2;
  } else {
    // 无分组则直接提交
    onSetGroupSubmit()
  }


}

const onSetGroupSubmit = (e?: any) => {
  console.log(e)
  const params = {
    ...subParams.value?.params,
    submit_data: {
      ...subParams.value?.params?.submit_data,
      group_arr: e?.group_arr || [],
      association_id: e?.association_id
    }
  }
  if(!params.link) {
    params.link = link.value;
  }
  if(!params.teamId) {
    params.teamId = associationStore.teamId;
  }

  if(subParams.value.params?.submit_data?.type === 1) {
    onReleaseRunUnitAxios(params);
  } else {
    onReleaseRunPersonalAxios(params)
  }
}
const joinContactModal: Ref<any> = ref(null);
const tipAnualFeeModalRef: Ref<any> = ref(null);
const tipShowText = ref('');

const onReleaseRunUnitAxios = async (params: any) => {
  let result: any = null;


  // 判断是否申请联系人
  const [err, res]:any = await to(checkDelegate({team_id:  applyData.value?.teamId, relation_team_id: params.teamId, telephone: params.submit_data?.telephone }))
  if(err) {
    MessagePlugin.error(err?.message);
    // loading.value = false;
    return;
  }


  try {
    result = await applyMemberCreateAxios(params);
    console.log(result);
    if(result?.code === -1) {
      tipShowText.value = result?.message;
      tipAnualFeeModalRef.value?.onOpen();
      // loading.value = false;
      return;
    }

    // else if(result?.code === 0 && result?.data?.apply_contact) {
    //   joinContactModal.value?.onOpen({
    //     team_name: params?.submit_data?.team_name,
    //     name: result?.data?.name,
    //     avatar: result?.data?.avatar,
    //     phone : params?.submit_data?.telephone,
    //     main_body_id: result?.data?.main_body_id,
    //   });
    //   return ;
    // }
    // 为了显示等待审核页面start
    organizeData.value.association_group_count = 0;
    organizeData.value.status = 1;


    // end
    // onGetLinkDetail();
    // emits('reload');
  } catch (error:any) {
    // resetViewOptions(data.free_form) // 回显数据
    console.log(error);
    const errMsg: any = error instanceof AxiosError ? error?.response?.data?.message : error?.message;
    MessagePlugin.error(errMsg);
    if (errMsg && errMsg.response?.status) {
      errorHandlerFilter(errMsg.response?.status);
    }
  }
  // loading.value = false;
  console.log(organizeData.value.status)
}

const onReleaseRunPersonalAxios = async (params: any) => {
  let result = null;
  try {
    result = await applyMemberCreateAxios(params);
    console.log(result);
    // onGetLinkDetail();
    // 为了显示等待审核页面start
    organizeData.value.association_group_count = 0;
    organizeData.value.status = 1;
    // end
  } catch (error:any) {
    // resetViewOptions(data.free_form) // 回显数据
    // const errMsg: any = error instanceof Error ? error.message : error;
    const errMsg: any = error instanceof AxiosError ? error?.response?.data?.message : error?.message;
    MessagePlugin.error(errMsg);
    if (errMsg && errMsg.response?.status) {
      errorHandlerFilter(errMsg.response?.status);
    }
  }
  console.log(organizeData.value.status)
  console.log(organizeData.value.association_group_count)
}




const getPhone = async (v:any) => {
  // registerParams.value.email.region = v.region;
  // registerParams.value.email.mobile = v.mobile;
  // registerParams.value.email.mobile_code = v.code;
  // registerAcc();


  const [err, res] = await to(bindAccount({
    mobile: v
  }))
  console.log(v)
  if(!err) {
    MessagePlugin.success('绑定成功')
    localStorage.clear();
    setTimeout(() => {
      window.location.reload();

    }, 1500);
  } else {
    MessagePlugin.error(err)
  }
};



// 获取入会申请详情
const onGetMemberApplyDetailAxios = (id: any) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await GetMemberApplyDetailAxios(id);
      //   result = getResponseResult(result);
      if (!result) {
        // eslint-disable-next-line prefer-promise-reject-errors
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      // eslint-disable-next-line prefer-promise-reject-errors
      reject();
      const errMsg: any = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};

// 获取邀请链接数据
const isLose = ref(false);
const step = ref(1);

// status: 申请状态，1：待审核，2：已入会，3：已驳回，0：待提交数据
const onGetLinkDetail = async () => {
  let result = null;
  try {
    if (link.value && link.value.length) {
      result = await getLinkDetailAxios({ link: link.value, telephone: accountStore.userInfo?.account_mobile, group_id:route.query?.group_id,  activate_link: route.query?.activate_link || ''  });
      console.log(result);
      //   result = getResponseResult(result);
      organizeData.value = result.data;
      subParams.value = { params: null, detail: organizeData.value, teamId: result.data?.teamId};
      associationStore.setLinkInfo(result.data);
      // associationStore.setTeamId(result.data?.teamId)
      //   onGetMemberSetting()
      // 2024-07-22
      // if(organizeData.value.status !== 3) {
      //   onGetSettings();
      // }
      onGetSettings();
      isLose.value = false;
      return
    }
    isLose.value = true;
  } catch (error) {
    const errMsg: any = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
    isLose.value = true;
  }
};
// 获取入会设置

const applyData: Ref<any> = ref(null); // 入会表单设置数据
const industryListData: Ref<any> = ref([]); // 行业列表
const sizeListData: Ref<any> = ref([]); // 规模列表
const memberLevelListData: Ref<any> = ref([]); // 会员职务列表

const onGetMemberSetting = () => {
  // getMemberSettingAxios
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberSettingAxios({ teamId: organizeData.value.teamId });
      console.log(result);
      //   result = getResponseResult(result);
      //   organizeData.value = result.data;
      // 单位入会、个人入会
      // applyData.value = result.data;

      // 根源：
      const subData = result.data;
      let menuList = memberConst.filter((v) => v.fromType === 'person');
      if (!(subData.personal_form && subData.personal_form.length > 0)) {
        subData.personal_form = lodash.cloneDeep(menuList);
      }
      menuList = memberConst.filter((v) => v.fromType === 'unit');
      if (!(subData.team_form && subData.team_form.length > 0)) {
        subData.team_form = lodash.cloneDeep(menuList);
      }

      console.log(result.data);

      resolve(result.data);
    } catch (error) {
      const errMsg: any = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      // eslint-disable-next-line prefer-promise-reject-errors
      reject();
    }
  });
};

const optionsCodeList = [
  { label: '+86', value: 86 },
  { label: '+853', value: 853 },
  { label: '+852', value: 852 },
];

const optionsOrganizeType = ref([
  { label: '企业', value: 1 },
  { label: '商协会', value: 2 },
  { label: '政府单位', value: 4 },
  { label: '个体户', value: 3 },
  { label: '其他', value: 0 },
]);
// 获取会员职务列表

const onGetMemberLevelList = () => {
  // getMemberJobsListAxios
  let result = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberJobsListAxios({ status: 0, show_status: 0 });
      console.log(result);
      //   result = getResponseResult(result);
      //   organizeData.value = result.data;
      // 单位入会、个人入会
      // applyData.value = result.data;
      result.data = result.data
        .filter((v: any) => v.show_status !== 2 && v.status !== 1)
        .map((v: any) => {
          v.money = priceDivisorShow(v.money, 100);
          return v;
        });
      memberLevelListData.value = result.data;
      resolve(result.data);
    } catch (error) {
      const errMsg: any = error instanceof Error ? error.message : error;
      // MessagePlugin.error(errMsg);
      console.log(error);
      // eslint-disable-next-line prefer-promise-reject-errors
      reject();
    }
  });
};

// 获取行业列表
const onGetIndustryList = () => {
  // getMemberJobsListAxios
  let result = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {

      // classifyTree

      result = await classifyTree(associationStore.$state.linkInfo.region);
      console.log(result, '阿萨达是');
      //   result = getResponseResult(result);
      //   organizeData.value = result.data;
      // 单位入会、个人入会
      // applyData.value = result.data;
      industryListData.value = result.data;

      resolve(result.data);
    } catch (error) {
      const errMsg: any = error instanceof Error ? error.message : error;
      // MessagePlugin.error(errMsg);
      console.log(error);

      // eslint-disable-next-line prefer-promise-reject-errors
      reject();
    }
  });
};
// 获取组织规模列表
const onGetTeamsSizeList = () => {
  // getMemberJobsListAxios
  let result = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getTeamsSizeListAxios();
      console.log(result);
      //   result = getResponseResult(result);
      //   organizeData.value = result.data;
      // 单位入会、个人入会
      // applyData.value = result.data;
      sizeListData.value = result.data;
      resolve(result.data);
    } catch (error) {
      const errMsg: any = error instanceof Error ? error.message : error;
      // MessagePlugin.error(errMsg);
      console.log(error);

      // eslint-disable-next-line prefer-promise-reject-errors
      reject();
    }
  });
};

// 获取表单、options等配置信息
/**
 * 组织配置信息、组织会员级别、组织行业列表、组织规模
 */
const onGetSettings = (isDetail = false) => {
  return new Promise(async(resolve, reject) => {
    try {
      const result = await Promise.all([
        onGetMemberSetting(), // 组织配置信息
        // onGetMemberLevelList(), // 组织会员级别
        onGetIndustryList(), // 组织行业列表
        onGetTeamsSizeList(), // 组织规模
      ]);
      if (result) {
        const apply: any = isDetail ? applyData.value : result[0];
        // const apply = result[0]

        onSetPeronForm(result, apply, isDetail);
        onSetUnitForm(result, apply, isDetail);
        console.log(apply);
        if (!isDetail) {
          applyData.value = apply;
        }
      }
      resolve(true);
    } catch (error) {
      const errMsg: any = error instanceof Error? error.message : error;
      MessagePlugin.error(errMsg);
      reject(errMsg);
    }
  })
};

// 设置个人表单
const onSetPeronForm = (result: Array<any>, apply: any, isDetail: Boolean) => {
  // const memberLevelOptions = result[1];
  if (apply?.personal_form && apply?.personal_form.length > 0) {
    const baseList = apply.personal_form.filter((v: any) => v.type === 'BaseInfoPolitics');
    // eslint-disable-next-line complexity
    baseList.map((v: any) => {
      // 会员级别的设置
      let origin: any = null;
      // origin = v.origin.find((or: any) => or.vModel === 'memberLevel');
      // if (origin) origin.options = memberLevelOptions;
      // console.log(origin);

      // 手机
      origin = v.origin.find((or: any) => or.vModel === 'phone');
      if (origin) {
        // origin.code = 86;
        // origin.code_value = origin.code_value ? origin.code_value: 86;
        origin.code_value = isDetail
          ? origin.code_value || 86
          : Number(accountStore.userInfo?.account_mobile_region || 86);
        origin.options = optionsCodeList;
        origin.value = isDetail ? origin.value : accountStore.userInfo?.account_mobile || '';
      }

      origin = v.origin.find((or: any) => or.vModel === 'reference');
      if (origin) {
        // origin.value = route.query.referrer ? decodeURIComponent(route.query.referrer) : origin.value;
        origin.value = organizeData.value?.referrer ? organizeData.value?.referrer : origin.value;
      }

      origin = v.origin.find((or: any) => or.vModel === 'referenceUnit');
      if (origin) {
        // origin.value = route.query.referrer_unit ? decodeURIComponent(route.query.referrer_unit) : origin.value;
        origin.value = organizeData.value?.referrer_unit ? organizeData.value?.referrer_unit: origin.value;
      }

      console.log(origin);

      // 头像
      origin = v.origin.find((or: any) => or.vModel === 'logo');
      if (origin) {
        origin.value = origin.value ? origin.value : [];
        if(origin.value?.length < 1 && accountStore.userInfo?.avatar) {
          const logoRes: any = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(accountStore.userInfo?.avatar);
          origin.value = [
            {
              file_name: accountStore.userInfo?.avatar,
              file_name_short: accountStore.userInfo?.avatar,
              original_name: accountStore.userInfo?.avatar,
              size: 0,
              type: logoRes?.length > 1 ? logoRes[1] : '',
            },
          ];
        }
      }
      // 代表人姓名
      origin = v.origin.find((or: any) => or.vModel === 'name');
      if (origin) {
        // origin.value = isDetail? origin.value : accountStore.userInfo?.account_name || '';
        origin.value = isDetail? origin.value : accountStore.userInfo?.title || '';
      }

      // 名录照片
      origin = v.origin.find((or: any) => or.vModel === 'nameLogo');
      if (origin) {
        origin.value = origin.value ? origin.value : [];
      }

      console.log(origin);

      return v;
    });
  }
};
// 设置单位
const onSetUnitForm = (result: Array<any>, apply: any, isDetail: Boolean) => {
  // const memberLevelOptions = result[1];
  if (apply?.team_form && apply?.team_form.length > 0) {
    console.log(apply.team_form);
    const baseList = apply.team_form.filter((v: any) => v.type === 'BaseInfoPolitics');
    // eslint-disable-next-line complexity
    baseList.map((v: any) => {
      // 会员级别的设置
      let origin = null;
      // origin = v.origin.find((or: any) => or.vModel === 'memberLevel');
      // if (origin) origin.options = memberLevelOptions;
      // // console.log(origin)
      // if (origin && origin.value) {
      //   // origin.value = origin.value;
      //   // if(subData.value) {
      //   //   origin.value = subData.value?.teamId || ''
      //   // }

      // }

      // 组织名称
      // origin = v.origin.find((or:any)=>or.vModel === 'organizeName')
      // if(origin) {
      //     origin.value = {
      //         teamLogo: politicsStore.linkInfo?.logo,
      //         teamId: politicsStore.teamId,
      //         teamFullName: politicsStore.linkInfo?.team
      //     }
      // }

      // 手机
      origin = v.origin.find((or: any) => or.vModel === 'phone');
      if (origin) {
        // origin.code_value = origin.code_value ? origin.code_value: 86;
        origin.code_value = isDetail
          ? origin.code_value || 86
          : Number(accountStore.userInfo?.account_mobile_region || 86);
        origin.options = optionsCodeList;
        origin.value = isDetail ? origin.value : accountStore.userInfo?.account_mobile || '';
        console.log('phoneSet', origin)
      }

      // 代表人姓名
      origin = v.origin.find((or: any) => or.vModel === 'name');
      if (origin) {
        // origin.value = isDetail? origin.value : accountStore.userInfo?.account_name || '';
        origin.value = isDetail? origin.value : accountStore.userInfo?.title || '';
      }

      origin = v.origin.find((or: any) => or.vModel === 'reference');
      if (origin) {
        // origin.value = route.query.referrer ? decodeURIComponent(route.query.referrer) : origin.value;
        origin.value = organizeData.value?.referrer ? organizeData.value?.referrer : origin.value;
      }

      origin = v.origin.find((or: any) => or.vModel === 'referenceUnit');
      if (origin) {
        // origin.value = route.query.referrer_unit ? decodeURIComponent(route.query.referrer_unit) : origin.value;
        origin.value = organizeData.value?.referrer_unit ? organizeData.value?.referrer_unit: origin.value;
      }

      console.log(origin, 'fffeeeee');
      // 行业选择
      origin = v.origin.find((or: any) => or.vModel === 'industryType');
      if (origin) {
        // origin.code = 86;
        origin.options = result[1];
        // industryListData.value = result[2];
      }

      // 规模选择
      origin = v.origin.find((or: any) => or.vModel === 'organizeScale');
      if (origin) {
        origin.options = result[2];
        // sizeListData.value = result[3];
      }

      // 组织类型选择
      origin = v.origin.find((or: any) => or.vModel === 'organizeType');
      if (origin) {
        origin.options = optionsOrganizeType.value;
      }

      // 地址
      origin = v.origin.find((or: any) => or.vModel === 'organizeAddress');
      if (origin) {
        // origin.detail = '';
        origin.value = origin.value ? origin.value : undefined;
      }

      // 头像
      // origin = v.origin.find((or: any) => ['logo', 'organizeLogo', 'nameLogo'].includes(or.vModel));
      // console.log(v.origin.find((or: any) => ['logo', 'organizeLogo', 'nameLogo'].includes(or.vModel)))
      // if (origin) {
      //   origin.value = origin.value ? origin.value : [];
      // }


      origin = v.origin.find((or: any) => 'logo'=== or.vModel);
      if (origin) {
        origin.value = origin.value ? origin.value : [];
        if(origin.value?.length < 1 && accountStore.userInfo?.avatar) {
          const logoRes: any = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(accountStore.userInfo?.avatar);
          origin.value = [
            {
              file_name: accountStore.userInfo?.avatar,
              file_name_short: accountStore.userInfo?.avatar,
              original_name: accountStore.userInfo?.avatar,
              size: 0,
              type: logoRes?.length > 1 ? logoRes[1] : '',
            },
          ];
        }
      }


      origin = v.origin.find((or: any) => 'organizeLogo'=== or.vModel);
      if (origin) {
        origin.value = origin.value ? origin.value : [];
      }

      origin = v.origin.find((or: any) => 'nameLogo'=== or.vModel);
      if (origin) {
        origin.value = origin.value ? origin.value : [];
      }

      // , 'organizeLogo', 'nameLogo']


      console.log(origin);

      return v;
    });
  }
};

onMounted(()=>{
  onInit();
})

const goLogin = () => {
  router.push({
    name: 'accountLogin',
    query: {
      redirect: encodeURIComponent(router.currentRoute.value.fullPath),
    },
  });
};

const goRegister = () => {
  router.push({
    name: 'accountRegister',
    query: {
      redirect: encodeURIComponent(router.currentRoute.value.fullPath),
    },
  });
};

// 监听右滑手势，返回上一页
const handleSwipeRight = () => {
  router.go(-1);
};

// 在组件挂载时监听右滑手势
onMounted(() => {
  document.title = '加入申请';
  document.addEventListener('swiperight', handleSwipeRight);
});

// 在组件卸载前移除监听
onBeforeUnmount(() => {
  document.removeEventListener('swiperight', handleSwipeRight);
});

// 在组件挂载时监听右滑手势
onMounted(() => {
  document.addEventListener('swiperight', handleSwipeRight);
});

// 在组件卸载前移除监听
onBeforeUnmount(() => {
  document.removeEventListener('swiperight', handleSwipeRight);
});
</script>

<style scoped lang="less">
.cro {
  color: var(--text-kyy_color_text_2, #516082);
}
.background {
  height: inherit;
  // width: 100vw;
  margin: auto;
  overflow-y: auto;
  // background: var(--bg-kyy-color-bg-deep, #f5f8fe);
  display: flex;
  // align-items: center;
  justify-content: center;
  background-color:#fff;
  overflow-x:hidden;
  align-items: center;
}
@width: 420px;
// pc端 固定尺寸展示
@media screen and (min-width: 500px) {

  #app>.home-container {
    max-height: 734px ;
    min-height: 734px;
    height: auto;
  }
  #app>.container {
    max-width: @width;
    min-width: @width;

  }
}


.invite {
  border-radius: 8px;
  background: var(--bg-kyy-color-bg-light, #fff);
  box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.12);
  display: flex;
  width: 384px;
  height: 584px;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
  margin: 20px auto;
  padding: 48px 32px 0 32px;
  .top {
    display: flex;
    flex-direction: column;
    align-items: center;
    &-img {
      // w-64px h-64px mt-64px
      width: 64px;
      height: 64px;
      border-radius: 100%;
    }

    &-title {
      margin-top: 12px;
      color: var(--text-kyy-color-text-1, #1a2139);
      text-align: center;
      font-family: PingFang SC;
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: 26px; /* 144.444% */
    }
    &-tip {
      color: var(--text-kyy-color-text-3, #828da5);
      text-align: center;
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      margin-top: 4px;
      // height: 54px;
    }
  }
  .con {
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%;
    &-item {
      display: flex;
      flex-direction: column;
      gap: 8px;
      .text {
        color: var(--text-kyy-color-text-1, #1a2139);
        text-align: left;
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
    }
    .log-btn {
      height: 40px;
      border-radius: var(--radius-kyy-radius-button-s, 4px);
      background: var(--brand-kyy-color-brand-default, #4d5eff);
      color: var(--color-button-primary-kyy-color-button-primary-text, #fff);
      text-align: center;

      /* kyy_fontSize_3/bold */
      font-family: PingFang SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px; /* 150% */
    }
    .reg-btn {
      height: 40px;
      border-radius: var(--radius-kyy-radius-button-s, 4px);
      border: 1px solid var(--brand-kyy-color-brand-default, #4d5eff);
      background: var(--bg-kyy-color-bg-light, #fff);
      color: var(--brand-kyy-color-brand-default, #4d5eff);
      text-align: center;

      /* kyy_fontSize_3/bold */
      font-family: PingFang SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px; /* 150% */
    }
  }
}

.apply {
  // margin-top: 44px;
  // height: calc(100% - 44px);
  height: calc(100%);
  width: 100%;
  display: flex;
  flex-direction: column;
  // width: 480px;
  &-header {
    display: flex;
    // padding: 8px;
    background-color: #fff;
    height: 48px;
    align-items: center;
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    z-index: 100;

    margin: auto;

    .back {
      flex: inherit;
      width: 24px;
      height: 24px;
    }
    .text {
      flex: 1;
      color: var(--text-kyy-color-text-1, #1a2139);
      text-align: center;

      /* kyy_fontSize_2/bold */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
      padding-right: 24px;
    }
  }
  &-tip {
    // margin-top:43px;
    padding: 16px 24px;
    background: #f0f8ff;
    height: 54px;
    .text {
      font-size: 14px;
      font-family: Microsoft YaHei, Microsoft YaHei-Regular;
      font-weight: 400;
      text-align: left;
      color: #13161b;
    }
    .value {
      color: var(--warning-kyy-color-warning-default, #fc7c14);
      text-align: center;

      /* kyy_fontSize_2/bold */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
    }
  }
  &-step {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    padding-bottom: 16px;
    max-width: 480px;
    height: 104px;
    .banner {
      margin-top: 18px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 42px;

      .circle {
        flex: none;
        display: inline-block;
        width: 20px;
        height: 20px;
        opacity: 0.2;
        border-radius: 50%;
        border: 2px solid #4d5eff;
      }
      // 进行中
      .circle_working {
        opacity: 1;
      }

      .circle_finished {
        background: url('@/assets/img/radioButton_active.svg');
        background-repeat: round;
        opacity: 1;

        width: 24px;
        height: 24px;
        border: 0;
      }
      .line {
        width: 100%;
        height: 1px;
        opacity: 0.2;
        border: 1px solid #4d5eff;
        border-radius: 2px;
        margin: 0 10px;
      }
      .line_working {
        border: 1px dashed #4d5eff;
        opacity: 1;
      }

      .line_finished {
        border: 1px bold #4d5eff;
        opacity: 1;
      }
    }
    .steps {
      display: flex;
      justify-content: space-between;
      padding: 0 24px;
      .li {
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 48px;
        .counter {
          color: var(--text-kyy-color-text-2, #516082);
          text-align: center;

          /* kyy_fontSize_4/bold */
          font-family: PingFang SC;
          font-size: 18px;
          font-style: normal;
          font-weight: 600;
          line-height: 26px; /* 144.444% */
        }
        .text {
          color: var(--text-kyy-color-text-3, #828da5);
          text-align: center;

          /* kyy_fontSize_2/regular */
          font-family: PingFang SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
      }
    }
  }
  &-first {
    margin-top: 12px;
    max-width: 100vw;
  }
  &-second {
    flex: 1;
    display: flex;
    // justify-content: center;
    padding-top: 80px;
    align-items: center;
    flex-direction: column;
    margin-top: 12px;

    background: #ffffff;
    // border-radius: 0px 0px 8px 8px;

    .info {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0 20px;
      &-img {
        width: 48px;
        height: 48px;
      }
      &-status {
        color: var(--text-kyy-color-text-1, #1a2139);
        text-align: center;

        /* kyy_fontSize_2/bold */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
        margin-top: 16px;
      }
      &-tips {
        color: var(--text-kyy-color-text-2, #516082);
        text-align: center;

        /* kyy_fontSize_2/regular */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        word-break: break-all;
      }
      &-btn {
        margin-top: 24px;
        .n-btn {
          display: flex;
          height: 32px;
          min-width: 80px;
          min-height: 32px;
          max-height: 32px;
          padding: 0px 16px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          flex-shrink: 0;
          color: var(--color-button-primary-kyy-color-button-primary-text, #fff);
          text-align: center;
          cursor: pointer;
          /* kyy_fontSize_2/bold */
          font-family: PingFang SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px; /* 157.143% */
          border-radius: var(--radius-kyy-radius-button-s, 4px);
          background: var(--color-button-primary-kyy-color-button-primary-bg-default, #4d5eff);
        }
      }
    }
  }
}

.lose {
  display: flex;
  flex-direction: column;
  align-items: center;
  align-self: stretch;
  padding-top: 160px;
  background-color: #fff;
  width: 100%;
  .icon {
    width: 48px;
    height: 48px;
  }
  .title {
    color: var(--text-kyy-color-text-1, #1a2139);
    text-align: center;
    margin-top: 16px;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .tipss {
    color: var(--text-kyy-color-text-2, #516082);
    text-align: center;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}

.fix-step {
  padding: 16px 24px;
  gap: 4px;
  // width: 480px;
  height: 104px;
  max-height: 104px;
  min-height: 104px;
  background: #fff;
  .line-boxx {
    padding: 0 18px;
    display: flex;
    width: 100%;
    align-items: center;
    height: 20px;
    .circle {
      width: 20px;
      height: 20px;
      opacity: 0.2;
      border-radius: 100%;
      border: 2px solid #4d5eff;
      flex: none;
      display: inline-block;
    }
    // 进行中
    .circle_working {
      opacity: 1;
    }

    .circle_finished {
      background: url('@/assets/img/radioButton_active.svg');
      background-repeat: round;
      opacity: 1;

      width: 24px;
      height: 24px;
      border: 0;
    }
    .line {
      width: 100%;
      height: 1px;
      opacity: 0.2;
      border: 1px solid #4d5eff;
      border-radius: 2px;
      margin: 0 10px;
    }
    .line_working {
      border: 1px dashed #4d5eff;
      opacity: 1;
    }

    .line_finished {
      border: 1px bold #4d5eff;
      opacity: 1;
    }
  }
  .num-box {
    display: flex;
    justify-content: space-between;
    margin-top: 4px;
    .nt {
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 48px;
      .number {
        color: var(--text-kyy-color-text-2, #516082);
        text-align: center;

        /* kyy_fontSize_4/bold */
        font-family: PingFang SC;
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: 26px; /* 144.444% */
      }
      .text {
        color: var(--text-kyy-color-text-3, #828da5);
        text-align: center;

        /* kyy_fontSize_2/regular */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
    }
  }
}


.footer {
  height: 104px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 24px;
  max-width: 420px;
  display: flex;
  justify-content: center;

  margin: 0 auto;
  padding: 0 24px;
}
.toUpload {

  background: url('@/assets/img/bg_pic.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border-radius: 16px;
  width: 100%;
  // height: 144px;
  display: flex;
  justify-content: space-between;
  // position: fixed;



  align-items: center;
  padding: 0 20px;

  &-left {
    display: flex;
    align-items: center;

    .img  {
        height: 80px;
    }
  }
  &-right {
    .btn {
      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_active, #4D5EFF);
      background: var(--color-button_border-kyy_color_buttonBorder_bg_hover, #EAECFF);
      padding: 4px 16px;
      a {
        color: var(--color-button_border-kyy_color_buttonBorder_text_active, #3E4CD1);
        text-align: center;

        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
      }


    }

  }
}

.apply-second2 {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  position: relative;
  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 100px;


    .info-img {
      width: 200px;
      height: 200px;
      margin-bottom: 12px;
    }

    .info-status {
      text-align: center;
      color: var(--text-kyy_color_text_2, #516082);
    }
  }
}


</style>
