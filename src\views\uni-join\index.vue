<template>
    <home v-show="!showSearch && !mapVisible" ref="homeRef" :markerInfo="markerInfo" class="boxxx" 
    @search="handleShowSearch" @selectLat="handleSelectLat"></home>
    <search ref="searchRef" :markerInfo="markerInfo" v-show="showSearch && !mapVisible" class="boxxx" @close="handleCloseSearch"></search>
    <BaiduMapSelector @confirm="handleMapConfirm" @close="handleCloseMap" v-show="mapVisible" />
</template>
<script setup lang="ts" name="uniJoin">

import { onMounted, onUnmounted, ref } from 'vue';
import home from './home.vue';
import search from './search.vue';
import BaiduMapSelector from './map/BaiduMapSelector.vue';
defineOptions({
  name: 'uniJoin'
});
const showSearch = ref(false);
const searchRef = ref(null);
const homeRef = ref(null);
const mapVisible = ref(false);
const componentRef = ref(null);
const markerInfo = ref({
  name: '珠海市政府',
  address: '广东省珠海市香洲区',
  location: { lng: 113.543005, lat: 22.265842 },
});

const handleShowSearch = () => {
  showSearch.value = true;
  setTimeout(() => {
    searchRef.value.focusrun();
  }, 0);
};
const handleCloseSearch = () => {
  showSearch.value = false;
  setTimeout(() => {
    homeRef.value.scrollTopRun();
  }, 0);
};
const handleSelectLat = () => {
  mapVisible.value = true;
}

onMounted(() => {
  // 将html,body设为height: 100%;overflow: hidden;
  document.documentElement.style.height = '100%';
  document.body.style.height = '100%';
  document.body.style.overflow = 'hidden';
  
});

onUnmounted(() => {
  // 移除html,body的height: 100%;overflow: hidden;
  document.documentElement.style.height = '100%';
  document.body.style.height = '100%';
  document.body.style.overflow = 'none';

});

const handleMapConfirm = (data) => {
  console.log('handleMapConfirm data:', data);
  markerInfo.value = data;
  mapVisible.value = false;
  homeRef.value.getDataByMapChange();
}

const handleCloseMap = () => {
  mapVisible.value = false;
}

</script>
<style lang="less" scoped>
.boxxx {
  height: 100%;
  overflow: hidden;
  position: relative;
  top: 0px;
  left: 0;
  right: 0;
  -webkit-overflow-scrolling: touch;
}
</style>
