<template>
  <div class="page">
    <div class="change-box">
      <img class="logo" src="/assets/member/ebook.svg" alt="" />
    </div>
    <div class="rich">
      <div class="between">
        <!-- <div class="top-left" v-show="square">
            <SquareAvatar size="32px" :square="square" />
            <span>{{ squareName }}</span>
          </div> -->
        <div class="app-name">刊物</div>
        <t-input
          v-model="keyword"
          :placeholder="t('ebook.sskw')"
          :maxlength="50"
          clearable
          style="width: 304px"
          @change="onSearch"
        >
          <template #prefix-icon>
            <iconpark-icon name="iconsearch" style="font-size: 20px" class="iconsearch"></iconpark-icon>
          </template>
        </t-input>
      </div>
      <div
        v-infinite-scroll="handleInfiniteOnLoad"
        class="lists"
        :infinite-scroll-immediate-check="false"
        :infinite-scroll-distance="20"
      >
        <div v-for="(rich, richIndex) in richArr" :key="richIndex" class="lists-item cursor" @click="onOpenEbook(rich)">
          <div class="cover">
            <img :src="getSrcThumbnail(rich.cover)" alt="" />
          </div>
          <div class="name">
            {{ rich.name }}
          </div>
        </div>

        <div style="display: flex; justify-content: center; width: 100%">
          <template v-if="!isNetworkError">
            <div v-show="richArr.length < 1" class="noEmpty">
              <Empty :tip="keyword ? '未找到匹配刊物' : '暂无刊物'" :name="keyword ? 'no-result' : 'no-data-new'" />
            </div>
          </template>
          <template v-else>
            <div v-show="!isLoading && richArr.length < 1" class="noEmpty">
              <Empty name="offline">
                <template #tip>
                  <div class="tipEmpty">
                    <span class="text">{{ t("ebook.neterr") }}</span>
                    <t-button theme="primary" class="btn" @click="onSearch">{{ t("ebook.again") }}</t-button>
                  </div>
                </template>
              </Empty>
            </div>
          </template>
        </div>
      </div>
      <!-- <div v-show="richArr.length > 0" class="example-more mt-24px">
          <span v-if=" pagination.total > richArr.length" class="more cursor" @click="onMore"> {{ isLoading ? t('ebook.load'): t('ebook.loadmore') }} </span>
          <span v-else-if="richArr.length > pagination.pageSize" class="noempty">
            <span class="line"></span><span class="toText">{{ $t('member.second.l') }}</span>  <span class="line"></span>
          </span>
        </div> -->
    </div>
  </div>
  <Tricks :offset="{ x: '-32', y: '-40' }" uuid="数字城市-刊物" />

</template>

<script setup lang="ts">
import { computed, onActivated, ref, toRaw, watch } from "vue";
import { getPoliticsTeamID } from "@renderer/views/politics/utils/auth";
import to from "await-to-js";
import { useI18n } from "vue-i18n";
import Empty from "@renderer/components/common/Empty.vue";
import { usePoliticsStore } from "@renderer/views/politics/store/politics";
import { getEbookShareList } from "@renderer/api/politics/api/ebookApi";
import { getBaseUrl } from "@renderer/utils/apiRequest";
import { getSrcThumbnail } from "@renderer/views/message/service/msgUtils";
import KyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import { onMountedOrActivated } from "@/hooks/onMountedOrActivated";

import { useRoute } from "vue-router";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import SquareAvatar from "@/views/square/components/SquareAvatar.vue";
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer, shell } = LynkerSDK;
const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();
const { t } = useI18n();

const store = usePoliticsStore();
const isLoading = ref(false);
const keyword = ref("");
const isNetworkError = ref(false);
const square = computed(() => JSON.parse(decodeURIComponent(route.query?.square)));
const squareName = computed(() => `${square?.value?.name}的会刊`);
const squareAvatar = computed(() => square?.value?.avatar);
console.log("route.query", route.query);
const pagination = {
  pageSize: 24,
  page: 1,
  total: 0,
};

const richArr = ref([]);

const props = defineProps({
  platform: {
    type: String,
    default: "",
  },
});
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform;
});

const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  } else if (platformCpt.value === platform.digitalWorkbench) {
    return route.query?.teamId || 0;
  } else if (platformCpt.value === platform.square) {
    return route.query?.team_id || 0;
  } else {
    return getPoliticsTeamID();
  }
});

const onSetArr = async (isCover = false) => {
  let channelType = "digital_platform";
  if (platformCpt.value === platform.digitalWorkbench) {
    channelType = "digital_work";
  } else if (platformCpt.value === platform.square) {
    channelType = "square";
  }

  const params = {
    team_id: currentTeamId.value,
    name: keyword.value,
    pageSize: 24,
    digital_type: "government",
    page: pagination.page,
    channel_type: channelType,
  };
  // 缓存信息存储
  const caches = store.getStorageDatas;
  const cache = caches.find((v) => v.teamId === currentTeamId.value);
  isLoading.value = true;
  const [err, res] = await to(getEbookShareList(params));
  isLoading.value = false;
  if (err) {
    console.log(err, "rich-err");
    if (err?.message === "Network Error" && cache) {
      isNetworkError.value = true;
      richArr.value = cache.memberRich?.items;
      pagination.total = cache.memberRich?.total;
    }
    return;
  }
  isNetworkError.value = false;
  let { data } = res;
  data = data.data;
  console.log(data);
  const richs = data.total > 0 ? data.list : [];
  pagination.total = data.total;
  if (isCover) {
    richArr.value = richs;
  } else {
    richArr.value = richArr.value.concat(richs);
  }
  const memberRich = {
    items: toRaw(richArr.value),
    total: data.total,
  };
  if (cache) {
    cache.memberRich = memberRich;
  } else {
    caches.push({ teamId: currentTeamId.value, memberRich });
  }
};
onMountedOrActivated(() => {
  // onSetArr(true); // 会员名录列表
  onSearch();
});

const handleInfiniteOnLoad = () => {
  console.log("触发了");
  if (!scrollDisabled.value) {
    onMore();
  }
};
const onMore = () => {
  pagination.page += 1;
  onSetArr();
};
const scrollDisabled = computed(() => richArr.value.length >= pagination.total);

const onSearch = () => {
  console.log("onSearch");
  pagination.page = 1;
  onSetArr(true);
};

// watch(
//   () => store.activeAccount,
//   (val) => {
//     if (val) {
//       onSearch();
//     }
//   },
//   {
//     deep: true
//   }
// );

const onOpenEbook = (data: any) => {
  let url = "";
  if (data.file.type === "pdf") {
    url = `${getBaseUrl("h5")}/ebook/browse?id=${data.uuid}`;
  } else {
    url = data.file.url;
  }
  openUrlByBrowser(url);
};

const openUrlByBrowser = (url: string) => {
  shell.openExternal(url);
};
</script>

<style lang="less" scoped>
// lss 加个滚动条样式
@import "@renderer/views/engineer/less/common.less";
.page {
  display: flex;
  flex-direction: column;
  align-items: center;
  // justify-content: center;
  width: 100%;
  height: inherit;
  padding: 16px 24px;
  // background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  height: 100%;
  background: url("@/assets/bench/ebook_bg.png");
  background-repeat: no-repeat;
  background-size: cover;
}

.between {
  display: flex;
  justify-content: space-between;
  padding-right: 12px;
  .app-name {
    color: var(--text-kyy_color_text_1, #1a2139);
    font-family: "PingFang SC";
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 26px;
    text-indent: 144px;
    display: flex;
    justify-content: end;
    align-items: end;
  }
}
.rich {
  padding: 16px 24px;
  width: 100%;
  border-radius: 16px;
  background: var(--bg-kyy_color_bg_light, #fff);
  min-height: 350px;
  max-height: 100%;
  overflow-y: hidden;
  padding-right: 2px;
  max-width: 1168px;
  &-tabs {
    display: flex;
    gap: 8px;
    .tem {
      padding: 4px 16px;
      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #d5dbe4);
      background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #fff);
      color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
      text-align: center;
      min-width: 80px;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
    }
    .active {
      background: var(--color-button_primary-kyy_color_button_primary_bg_default, #4d5eff);
      color: var(--lingke-White-100, #fff);
      border: 1px solid var(--color-button_primary-kyy_color_button_primary_bg_default, #4d5eff);
    }
  }
  .line {
    background: #fff;
    margin-bottom: 16px;
    margin-top: 16px;
    height: 1px;
  }
  .lists::-webkit-scrollbar {
    height: 20px;
  }
  .lists::-webkit-scrollbar-thumb {
    height: 20px;
    border-radius: 4px;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
  }
  .lists {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    width: 100%;
    margin-top: 24px;
    height: calc(100% - 40px);
    overflow-y: auto;
    &-item {
      padding: 12px;
      border-radius: 8px;
      width: 176px;
      height: 262px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      transition: all 0.15s linear;
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_deep, #f5f8fe);
      border: 1px solid #f5f8fe;
      .cover {
        display: flex;
        justify-content: center;
        padding: 0 5px;
        img {
          width: 152px;
          height: 208px;
          object-fit: cover;
        }
      }
      .name {
        // padding: 0 5px;
        overflow: hidden;
        color: var(--text-kyy_color_text_1, #1a2139);
        text-overflow: ellipsis;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
        display: -webkit-box;
        width: 156px;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
      }

      &:hover {
        border-radius: 8px;
        border: 1px solid var(--brand-kyy_color_brand_default, #4d5eff);
        background: var(--bg-kyy_color_bg_light, #fff);
        transition: all 0.15s linear;
      }
      .left {
        width: 72px;
        height: 72px;
        position: relative;
        .lo {
          width: 72px;
          height: 72px;
          border-radius: 3.6px;
        }
        .ri {
          height: 20px;
          position: absolute;
          top: 0;
          left: 0;
        }
      }
      .right {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 8px;
        .top {
          .name {
            color: var(--text-kyy_color_text_1, #1a2139);
            /* kyy_fontSize_2/bold */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px; /* 157.143% */
            .tip {
              color: var(--kyy_color_tag_text_brand, #4d5eff);
              text-align: center;
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px; /* 166.667% */
              padding: 0 4px;
              border-radius: var(--kyy_radius_tag_s, 4px);
              background: var(--kyy_color_tag_bg_brand, #eaecff);
            }
            .yellow {
              color: var(--kyy_color_tag_text_warning, #fc7c14);
              background: var(--kyy_color_tag_bg_warning, #ffe5d1);
            }
          }
        }
        .bottom {
          display: flex;
          align-items: center;

          .company {
            flex: 1 1 auto;
            display: flex;
            align-items: center;
            gap: 4px;
            height: 20px;

            .av {
              flex: none;
            }
            .text {
              flex: 1;
              max-width: 140px;
              color: var(--text-kyy_color_text_3, #828da5);
              /* kyy_fontSize_1/regular */
              font-family: "PingFang SC";
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px; /* 166.667% */
            }
          }
          .area {
            flex: none;
            //  width: 72px;
            display: flex;
            align-items: center;
            .icon {
              color: #21acfa;
              font-size: 20px;
            }
            .text {
              width: 60px;
            }
          }
        }
      }
    }
  }
  .example-more {
    display: flex;
    align-items: center;
    justify-content: center;

    .more {
      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #d5dbe4);
      background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #fff);
      color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      padding: 4px 16px;
    }
    .noempty {
      color: var(--text-kyy_color_text_2, #516082);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      display: flex;
      align-items: center;
      width: 100%;
      gap: 12px;
      padding-bottom: 16px;
      .toText {
        flex: none;
      }
      .line {
        height: 1px;
        background: var(--divider-kyy_color_divider_deep, #d5dbe4);
        width: 100%;
      }
    }
  }
}

.change-box {
  width: 100%;
  min-width: 1088px;
  display: flex;
  justify-content: flex-end;
  position: relative;
  min-height: 48px;
  max-width: 1168px;
  .logo {
    position: absolute;
    top: 0;
    left: 24px;
    z-index: 20;
  }
}
</style>
