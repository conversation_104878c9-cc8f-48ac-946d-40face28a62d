<template>
  <div class="map-selector-page containerPc">
    <div class="map-header">
      <div class="title">
        <span class="t-c" @click="handleCloseSearch">
          <iconpark-icon name="iconarrowlift" class="header-icon"></iconpark-icon>
        </span>
        <span class="t-t">位置</span>
        <span class="t-q" style="opacity: 0">确定</span>
      </div>
      <div class="search-wrap">
        <span class="t-c" @click="handleCloseSearch" v-if="isWechat">
          <iconpark-icon name="iconarrowlift" class="header-icon"></iconpark-icon>
        </span>

        <t-input
          v-model="keyword"
          ref="searchInput"
          autofocus
          clearable
          @clear="cancelSearch"
          type="text"
          placeholder="搜索地址"
          @input="getPlaceListDebounce"
        >
          <template #prefix-icon>
            <iconpark-icon name="iconsearch" class="iconsearch"></iconpark-icon>
          </template>
        </t-input>
      </div>
    </div>

    <!-- <div v-if="!onlyShow" class="input-wrap">
      <t-input
        v-model="keyword"
        placeholder="搜索地址"
        clearable
        autofocus
        @clear="cancelSearch"
        @input="getPlaceListDebounce"
      >
        <template #prefix-icon>
          <iconpark-icon name="iconsearch" class="icon-search" />
        </template>
      </t-input>

      <t-link
        v-if="keyword"
        theme="default"
        hover="color"
        class="ml-12"
        @click="cancelSearch"
      >
        {{ $t('square.action.cancel') }}
      </t-link>
    </div> -->

    <div class="main-content">
      <t-loading v-if="loading" class="my-20" size="small" />

      <div v-show="!loading && mapVisible" class="map-container">
        <BMap
          :api-url="BAIDU_API_URL"
          :center="center"
          :zoom="zoom"
          :height="320"
          enable-scroll-wheel-zoom
          v-bind="mapOptions"
          @initd="mapInit"
          @click="mapClick"
        >
          <!-- <BZoom anchor="BMAP_ANCHOR_BOTTOM_LEFT" /> -->
          <BLocation anchor="BMAP_ANCHOR_BOTTOM_LEFT" />
          <BMarker
            v-if="markerPoint"
            :position="markerPoint"
            :enable-clicking="false"
            :offset="{ x: -14, y: -48 }"
            :icon="markerIcon"
          />
        </BMap>
      </div>

      <!-- 搜索的列表 -->
      <t-loading style="margin-top: 160px;" v-if="listLoading"  text="加载中..." size="small">
        <div v-if="posList.length" class="search-result">
          <div
            v-for="item in posList"
            :ref="(el) => setItemRef(el, item.uid)"
            :key="item.uid"
            :class="['pos-item', { active: selectItem?.uid === item.uid }]"
            @click="itemClick(item)"
          >
            <iconpark-icon name="iconorientation" class="icon" />
            <div class="dri flex-1">
              <div class="name" v-html="highlight(item.name, keyword)" />
              <div class="address" v-html="highlight(item.address, keyword)" />
            </div>
            <iconpark-icon
              v-if="item.name === markerInfo.name || item.title === markerInfo.name"
              name="iconcheckone"
              class="iconcheckone"
            />
          </div>

          <Empty v-if="!posList.length && listLoaded && !mapVisible" name="no-result" center />

          <div
            v-if="showMore && isAbroad"
            :class="['pos-item', 'load-more', { 'pointer-events-none!': !hasMore }]"
            @click="loadMore"
          >
            {{ posList.length && !hasMore ? '暂无更多地点信息' : '点击加载更多' }}
          </div>
        </div>
      </t-loading>
      <!-- 地图上选点 -->
      <div v-else-if="markerInfo" class="search-result">
        <div
          class="pos-item"
          v-for="item in markerInfo.surroundingPois"
          :ref="(el) => setItemRef(el, item.uid)"
          :key="item.uid"
          @click="handleseMap(item)"
        >
          <iconpark-icon name="iconorientation" class="iconorientation" />
          <div class="dri flex-1">
            <div class="name">{{ item.name || item.title }}</div>
            <div class="address">{{ item.address }}</div>
          </div>
          <iconpark-icon
            v-if="item.name === markerInfo.name || item.title === markerInfo.name"
            name="iconcheckone"
            class="iconcheckone"
          />
        </div>
      </div>
    </div>

    <!-- <div v-if="!onlyShow && (posList.length || markerInfo)" class="text-right mt-24">
      <t-button variant="outline" class="!mr-8 min-w-80" @click="oncancel">{{ $t('square.action.cancel') }}</t-button>
      <t-button
        theme="primary"
        :disabled="!(selectItem || markerInfo) || !mapVisible"
        class="min-w-80"
        @click="submit"
      >
        确定
      </t-button>
    </div> -->
  </div>
</template>
<script setup lang="ts">
import { computed, nextTick, onBeforeMount, onMounted, ref, watch } from 'vue';
import debounce from 'lodash/debounce';
import { useVModels } from '@vueuse/core';
import { useIpLocation, usePointGeocoder } from 'vue3-baidu-map-gl';
import to from 'await-to-js';
import { mapOptions, markerIcon, covertData, openExternalMap, BAIDU_API_URL } from './utils';
import Empty from '@/components/Empty.vue';
import { highlight } from './utils';
import { baiduMapApi } from '@/api/order';

// HACK: md5 is not defined
// https://github.com/yue1123/vue3-baidu-map-gl/issues/27#issuecomment-2219696323
// let moduleObject: any;
// onBeforeMount(() => {
//   moduleObject = module || {};
//   // eslint-disable-next-line no-global-assign
//   (module as unknown) = undefined;
// });
const onBMapInitdHandler = () => {
  // eslint-disable-next-line no-global-assign
  (module as unknown) = moduleObject;
};

const props = withDefaults(
  defineProps<{
    visible: boolean;
    // 显示
    loc?: Record<string, any>;
    // 仅显示
    onlyShow?: boolean;
  }>(),
  {
    loc: () => ({}),
  },
);
const emit = defineEmits(['update:visible', 'confirm', 'activity-confirm', 'close']);
const { visible } = useVModels(props, emit);

const mapVisible = ref(true);
onMounted(() => {
  mapVisible.value = true;
  isWechat.value = /micromessenger/i.test(navigator.userAgent);
});

// 搜索信息
const keyword = ref('');
const loading = ref(false);
const listLoaded = ref(false);
const listLoading = ref(false);
const isWechat = ref(false);

// 搜索结果列表
const posList = ref([]);
// 搜索结果选中项
const selectItem = ref(null);

// 地图中心点
const center = ref();
// 当前城市
const city = ref('');
// 地图缩放级别
const zoom = ref(10);
// 拖拽或点击地图得到的marker
const markerInfo = ref(null);
// 标记点坐标
const markerPoint = ref<{ lng: number; lat: number }>(center.value);

const locPoint = computed(() => {
  const { latLng } = props.loc || {};
  if (!latLng) return null;

  const { longitude, latitude } = latLng;
  return { lat: latitude, lng: longitude };
});

// 定位到指定经纬度
const handleSelectedData = (loc) => {
  if (!loc) return false;

  center.value = loc;
  markerPoint.value = loc;

  selectPOI(loc);
  getPointGeocoder(loc);
  return true;
};
const needConfirm = ref(false);
// 由坐标点解析地址信息（逆向地理编码）
const { get: getPointGeocoder, isEmpty } = usePointGeocoder({}, (res) => {
  if (isEmpty.value) return;

  console.log('pointGeocoder', res.value);
  const data = covertData(res.value);
  console.log('covertData', data);
  if (selectItem.value) {
    const { name, address } = selectItem.value;
    markerInfo.value = {
      ...data,
      name,
      address: address || name,
      location: selectItem.value.location || selectItem.value.point,
    };
  } else {
    markerInfo.value = data;
  }
  console.log('markerInfo.value:', markerInfo.value);
  if (needConfirm.value) {
    emit('confirm', markerInfo.value);
    needConfirm.value = false;
  }
});

// IP 定位（用于获取用户所在的城市位置信息，根据用户 IP 自动定位到城市）
const { get: getIPLocation, location } = useIpLocation(() => {
  console.log('getIPLocation getIPLocation', location);

  if (!location.value) return;
  if (handleSelectedData(locPoint.value)) return;

  const { point, name } = location.value;
  center.value = point;
  city.value = name;
  zoom.value = 18;

  console.log('location.value:', location.value);
  markerPoint.value = point;
  getPointGeocoder(point);
});

// 地图初始化
const mapInit = () => {
  console.log('mapInit mapInit');

  // onBMapInitdHandler();

  if (handleSelectedData(locPoint.value)) return;

  // 不指定目标地址时，定位到当前城市中心
  getIPLocation();
};

// 点击地图设置新的标记点，并获取其详情地址
const mapClick = ({ latlng }) => {
  posList.value = [];
  console.log('mapClick latlng:', latlng);
  if (props.onlyShow) {
    const { latLng, name } = props.loc || {};
    const { lng, lat } = latLng;
    if (!lng) return;

    openExternalMap({ lng, lat, name });
    return;
  }

  selectPOI(latlng);
  getPointGeocoder(latlng);
};

const getRegion = (keyword: string) => {
  if (!keyword) return '';
  if (keyword.startsWith('澳门') || keyword.startsWith('澳門')) return '澳门';
  if (keyword.startsWith('香港')) return '香港';
  return '';
};

const pageSize = ref(10);
const pageNum = ref(0);
const hasMore = ref(true);
const showMore = computed(() => !listLoading.value && listLoaded.value && !mapVisible.value);

const isAbroad = computed(() => !!getRegion(keyword.value));

const searchParams = computed(() => {
  if (isAbroad.value) {
    return {
      query: keyword.value,
      page_size: String(pageSize.value),
      page_num: String(pageNum.value),
      scope: '1',
      region: getRegion(keyword.value),
    };
  }

  return {
    q: keyword.value,
    region: city.value || '全国',
  };
});

// 搜索结果列表
const getPlaceList = async () => {
  // mapVisible.value = false;
  listLoading.value = true;
  listLoaded.value = false;

  const [err, res] = await to(baiduMapApi(searchParams.value, isAbroad.value ? 'abroad' : 'suggestion'));
  console.log('resresres', res);

  listLoading.value = false;
  listLoaded.value = true;
  if (err || res.code !== 0) return;

  try {
    const resultObj = JSON.parse(res.data.result);
    console.log('resultObj', resultObj);

    const placeList = resultObj.results || resultObj.result || [];
    posList.value = [...posList.value, ...placeList.filter((v) => v.location)];
    hasMore.value = placeList.length === pageSize.value;
  } catch (error) {
    console.error(error);
  }
};

// 搜索关键字变化时，重新发起请求
const getPlaceListDebounce = debounce(() => {
  if (!keyword.value) {
    mapVisible.value = true;
    return;
  }

  hasMore.value = true;
  pageNum.value = 0;
  posList.value = [];
  getPlaceList();
}, 400);

// 取消搜索
const cancelSearch = () => {
  keyword.value = '';
  mapVisible.value = true;
};

// 关闭弹窗事件
const onclose = () => {
  keyword.value = '';
};
// 手动关闭弹窗
const oncancel = () => {
  onclose();
  visible.value = false;
};

// 清空搜索条件后，重置为初始值
watch(keyword, (val) => {
  if (!val && markerPoint.value && !posList.value.length) {
    getPointGeocoder(markerPoint.value);
  }
});

// 标记并定位选中的点
const selectPOI = (item) => {
  markerPoint.value = item;
  center.value = item;
  zoom.value = 18;
  mapVisible.value = true;
};

// 获取搜索结果项实例（用于自动定位到所选项）
let itemRefs = ref<{ [key: string]: HTMLInputElement }>({});
const setItemRef = (el: HTMLInputElement, uid: string) => {
  if (el) itemRefs.value[uid] = el;
};

// 搜索结果项点击
const itemClick = async (item) => {
  console.log('itemClick item:', item);
  needConfirm.value = true;

  selectItem.value = item;
  selectPOI(item.location);
  getPointGeocoder(item.location, true);

  // await nextTick();
  // itemRefs.value[item.uid]?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  console.log('confirmmarkerInfo.value', markerInfo.value);

  emit('confirm', markerInfo.value);
};

// 回显地址
watch(
  () => props.loc,
  (val) => {
    if (!val || !val.latLng) return;

    handleSelectedData(locPoint.value);
  },
  { immediate: true, deep: true },
);

// 提交地址
const submit = () => {
  emit('confirm', markerInfo.value);

  oncancel();
};

// 加载更多
const loadMore = () => {
  pageNum.value++;
  getPlaceList();
};

// 关闭搜索
const handleCloseSearch = () => {
  emit('close', markerInfo.value);
};

// 确认选择
const handleMapConfirm = () => {
  emit('confirm', markerInfo.value);
};
const handleseMap = async (item) => {
  console.log('handleseMap item:', item);
  needConfirm.value = true;
  // markerInfo.value = item;
  // emit('confirm', markerInfo.value);
  selectItem.value = item;
  if (!selectItem.value?.name) {
    selectItem.value.name = selectItem.value?.title;
  }
  selectPOI(item.location || item.point);
  getPointGeocoder(item.location || item.point);
  // markerInfo.value = {
  //     ...item,
  //     name: selectItem.value?.title,
  //     location: selectItem.value?.point,
  //   };
  console.log('handleseMap item:markerInfo.value', markerInfo.value);
  // await nextTick();
  // itemRefs.value[item.uid]?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  // emit('confirm', markerInfo.value);
};
</script>
<style lang="less" scoped>
.map-selector-page {
  width: 100%;
  height: 100vh;
  overflow-y: hidden;
  background-color: #fff;
  .map-header {
    background-color: #fff;
    .title {
      display: flex;
      width: 375px;
      height: 44px;
      padding: 9px 16px;
      justify-content: space-between;
      align-items: flex-start;
      .t-c {
        color: var(--color-button_text_secondray-kyy_color_button_text_secondray_font_default, #1a2139);
        text-align: center;
        font-feature-settings: 'liga' off, 'clig' off;

        /* kyy_fontSize_3/regular */
        font-family: 'PingFang SC';
        font-size: 17px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px; /* 152.941% */
      }
      .t-t {
        color: var(--text-kyy_color_text_1, #1a2139);
        text-align: center;
        font-feature-settings: 'liga' off, 'clig' off;

        /* kyy_fontSize_4/bold */
        font-family: 'PingFang SC';
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: 26px; /* 144.444% */
      }
      .t-q {
        color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
        text-align: center;
        font-feature-settings: 'liga' off, 'clig' off;

        /* kyy_fontSize_3/regular */
        font-family: 'PingFang SC';
        font-size: 17px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px; /* 152.941% */
      }
    }
    .search-wrap {
      display: flex;
      padding: 8px 16px;
      align-items: center;
      align-self: stretch;

      .search-input {
        display: flex;
        height: 40px;
        padding: 0px 8px;
        align-items: center;
        gap: 4px;
        border-radius: 4px;
        border: 1px solid var(--search-kyy_color_search_border_default, #d5dbe4);
        background: var(--search-kyy_color_search_bg_default, #fff);
        background: #fff;
        backdrop-filter: blur(10px);
        position: relative;
        .search-icon {
          font-size: 24px;
          color: #828da5;
        }
        .search-i {
          // 去除输入框边框 hover样式
          border: none;
          outline: none;
          width: 100%;
          height: 28px;
          color: #1a2139;
          font-size: 17px;
          padding-right: 23px;
        }
        .search-i::placeholder {
          color: var(--text-kyy_color_text_5, #acb3c0);
          font-feature-settings: 'liga' off, 'clig' off;

          /* kyy_fontSize_3/regular */
          font-family: 'PingFang SC';
          font-size: 17px;
          font-style: normal;
          font-weight: 400;
          line-height: 26px; /* 152.941% */
        }
      }
      .search-inputw1 {
        width: 100%;
      }
      .search-inputw2 {
        flex: 1;
      }
    }
  }
}

.b-map-selector-dialog {
  .t-dialog {
    padding-bottom: 24px;
  }

  .t-dialog__close {
    color: var(--icon-kyy-color-icon-deep, #516082);
    width: 24px;
    height: 24px;
  }

  .t-dialog__body {
    display: flex;
    flex-direction: column;
    height: 488px;
    padding-top: 24px;
    padding-bottom: 0;
  }

  .main-content {
    flex: 1;
    // height: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .input-wrap {
    margin-bottom: 16px;
    display: flex;
    .t-input__wrap {
      flex: 1;
    }
    input::placeholder {
      color: var(--text-kyy-color-text-5, #acb3c0) !important;
    }
    .icon-search {
      font-size: 20px;
      color: var(--icon-kyy-color-icon-default, #828da5);
    }
    .btn-cancel {
      display: flex;
      align-items: center;
      padding-left: 12px;
      color: var(--color-button_text_secondray-kyy_color_button_text_secondray_font_default, #516082);
      text-align: center;
      font-size: 14px;
      line-height: 22px; /* 157.143% */
      cursor: pointer;
    }
  }

  .map-container {
    width: 100%;
    // height: 216px;
    // margin: 8px 0;
    border-radius: 8px;
    border: 1px solid var(--border-kyy_color_border_default, #d5dbe4);
    flex-shrink: 0;
    overflow: hidden;
    :deep(.amap-container) {
      border-radius: 8px;
    }
  }

  .t-loading {
    width: 100%;
    color: #2069e3;
    .t-loading__gradient-conic {
      background: conic-gradient(from 90deg at 50% 50%, rgba(161, 162, 164, 0) 0deg, rgb(4 85 248) 360deg);
    }
  }

  //.scrollbar();
}
// :deep(.t-input--borderless:not(.t-input--focused):hover) {
//   border: none;
//   background-color: transparent;

// }
// :deep(.t-input--borderless:not(.t-input--focused)){
//   border: none;
//   background-color: transparent;
// }
// :deep(.t-input:focus) {
//   border: none;
//   background-color: transparent;
//   box-shadow: none;
// }
// :deep(.t-input:hover) {
//   border: none;
//   background-color: transparent;
// }
// :deep(.t-input--focused) {
//   border: none;
//   box-shadow: none;
// }
.search-result {
  overflow-y: auto;
  display: flex;
  padding: 4px 16px 0 16px;
  flex-direction: column;
  align-items: flex-start;
  flex: 1 0 0;
  align-self: stretch;
  height: calc(100vh - 416px);
  .pos-item {
    display: flex;
    padding: 12px 0;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
    .iconorientation {
      color: #fc7c14;
      font-size: 20px;
    }
    .iconcheckone {
      font-size: 20px;
    }
    .dri {
      .name {
        color: var(--text-kyy_color_text_1, #1a2139);
        font-feature-settings: 'liga' off, 'clig' off;

        /* kyy_fontSize_3/regular */
        font-family: 'PingFang SC';
        font-size: 17px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px; /* 152.941% */
      }
      .address {
        color: var(--text-kyy_color_text_3, #828da5);
        font-feature-settings: 'liga' off, 'clig' off;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        align-self: stretch;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
    }
  }

  .icon {
    font-size: 20px;
    color: var(--warning-kyy_color_warning_default, #fc7c14);
    flex-shrink: 0;
    align-self: flex-start;
    margin-top: 3px;
  }

  .address {
    font-size: 14px;
    color: var(--text-kyy_color_text_2, #516082);
  }

  .load-more {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    color: var(--text-kyy-color-text-2, #516082);
    font-size: 14px;
    cursor: pointer;
  }
}
.iconsearch {
  font-size: 24px;
  color: #828da5;
}
.header-icon {
  font-size: 28px;
  color: #1a2139;
}
:deep(.t-loading__gradient-conic) {
  transform: scale(1) !important;
}
</style>
