<template>
  <div class="cantainer">
    <div class="title">
      <span>{{ t('contacts.friend') }}</span>
      <t-tooltip :content="t('contacts.addContanc')" :show-arrow="false" placement="bottom">
        <img style="cursor:pointer;width: 24px;height: 24px;" src="@renderer/assets/svg/icon-peopleJoin-new.svg" alt="" @click="addFriend">
      </t-tooltip>
    </div>
    <div v-if="empty" class="recent-empty">
      <img src="@renderer/assets/emptydata.png" alt="">
      <div class="tip">暂无数据</div>
    </div>
    <div v-else :class="['t-menu-container', isNotMac ? 'scrollbar' : '']">
      <t-anchor class="anchor-list" @click="handleClick">
        <t-anchor-item v-for="item in options" :key="item.initial" :href="`#${route.path}#${item.initial}`" :title="item.initial" :class="activeAnchorTitle === item.initial ? 'activeAnchorTitle' : ''" />
      </t-anchor>
      <t-menu theme="light" defaultValue="dashboard" style="width: calc(100% - 37px)">
        <div v-for="item in options" :key="item.initial">
          <h1 class="anchor-title" :id="`${route.path}#${item.initial}`">{{ item.initial }}</h1>
          <t-menu-item v-for="friend in item.data" :key="friend.cardId" :value="friend.cardId" @mouseover="showAct(friend)" @mouseleave="hiddenAct" @click="showCard(friend)">
            <template #icon>
              <avatar class="avatar-icon" roundRadius :imageUrl="friend.avatar" :userName="friend.title || friend.name" avatarSize="44px" />
            </template>
            <div class="user-info">
              <div class="user-name">
                <div>{{ friend.name }}</div>
              </div>
            </div>
            <div class="act-groups" v-if="friend.cardId === hoverValue">
              <div v-if="friend.has_del">
                <t-button variant="base" theme="primary">
                  {{ t('contacts.addStatusAdd') }}
                </t-button>
              </div>
              <div v-else>
                <!-- <t-button class="mr-12" shape="circle" theme="primary" @click.stop="vioce(friend)">
                  <template #icon>
                    <t-tooltip :content="t('zx.contacts.voiceCall')" :show-arrow="false" placement="bottom">
                      <img src="@renderer/assets/svg/voicefill_new.svg" alt="">
                    </t-tooltip>
                  </template>
                </t-button> -->
                  <!--                {{ t('zx.contacts.voice') }}-->
                  <!--                {{ t('zx.contacts.video') }}-->
                  <!--                {{ t('contacts.msg') }}-->
                <!-- <t-button class="mr-12" shape="circle" theme="primary" @click.stop="video(friend)">
                  <template #icon>
                    <t-tooltip :content="t('zx.contacts.videoCall')" :show-arrow="false" placement="bottom">
                      <img src="@renderer/assets/svg/videofill_new.svg" alt="">
                    </t-tooltip>
                  </template>
                </t-button> -->
                <t-button shape="circle" theme="primary" @click.stop="msg(friend)">
                  <template #icon>
                    <t-tooltip :content="t('zx.contacts.msgCall')" :show-arrow="false" placement="bottom">
                      <img src="@renderer/assets/svg/commentfill_new.svg" alt="">
                    </t-tooltip>
                  </template>
                </t-button>
              </div>
            </div>
          </t-menu-item>
        </div>
      </t-menu>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { pairsList } from '@renderer/api/contacts/api/organize';
import avatar from '@renderer/components/kyy-avatar/index.vue';
import { pySegSort, videoMsg, voiceMsg } from '../utils';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { getProfilesInfo, getOpenid } from '@renderer/utils/auth';
import { getNoteList } from "@renderer/api/contacts/api/common";
import { isNotMac } from '@renderer/views/zhixing/util';
import { useContactsStore } from '@/store/modules/contacts';
const contactsStore = useContactsStore();
import { openChat } from '@/utils/share';
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();
const route = useRoute();
const options = ref([]);
const hoverValue = ref('');
const empty = ref(false);
const activeAnchorTitle = ref('');

const handleClick = ({ title }) => {
  activeAnchorTitle.value = title
};
const friendListCardId = () => {
  pairsList({all:true}).then((res) => {
    console.log(res, 'getPairsList');
    if (res.status === 200) {
      res.data.data.relations?.length ? polyPairsList(res.data.data.relations) : empty.value = true;
    }
  });
};
const replaceNote = (list) => {
  console.log(list, 'replaceNote');
  // 获取备注信息
  getNoteList({ cardIds: list.map((v) => v.cardId) }).then((res) => {
    console.log(res, "getNoteList");
    if (res.data.code === 0) {
      res.data.data?.cardRemarks?.forEach((item) => {
        const itemFollow = list.find((v) => v.cardId === item.cardId);
        if(itemFollow){
          itemFollow.CardName = itemFollow?.name;
          itemFollow.name = item.remarks ? item.remarks : itemFollow?.name;
        }
      });
      options.value = pySegSort(list);
    }
  });
};
const polyPairsList = (list) => {
  const parisList = [];
  const listData = list.reduce((acc, v) => {
    if (v.pairType === 'FRIEND') {
       const obj = {
        title: v.friendCard.cardName,
        name: v.comment || v.friendCard.cardName,
        avatar: v.friendCard.avatar || '',
        cardId: v.cardIdFriend,
        has_del:false,
        // v.friendCard.removed != 0 || !!v.friendCard.removed
      }
      parisList.push(obj)
      acc.push(v.peer);
    }
    return acc;
  }, []);
  if (!listData.length) {
    empty.value = true;
    return;
  }
  replaceNote(parisList)
};

const msg = (item) => {
  const profilesInfo = getProfilesInfo();
  openChat({main: profilesInfo.openid, peer: item.cardId});
};
const video = (item) => {
  const profilesInfo = getProfilesInfo();
  videoMsg(item.cardId, profilesInfo.openid);
};
const vioce = (item) => {
  const profilesInfo = getProfilesInfo();
  voiceMsg(item.cardId, profilesInfo.openid);
};
const showAct = (item) => {
  hoverValue.value = item.cardId;
};
const hiddenAct = () => {
  hoverValue.value = '';
};
const showCard = (item) => {
  ipcRenderer.invoke("identity-card", { cardId: item.cardId, myId: getOpenid() });
};
const addFriend = () => {
  contactsStore.addContactsType = 'friend'
  contactsStore.addContactsVisible = true
};

const friendListListener = (event,arg) => {
  console.log('====>friend');
  friendListCardId();
};

onMounted(() => {
  friendListCardId();
  ipcRenderer.on("update-contact-list", friendListListener);
});

onUnmounted(() => {
  ipcRenderer.off("update-contact-list", friendListListener);
});
</script>

<style lang="less" scoped>
.mr-12 {
  margin-right: 12px;
}
.cantainer {
  width: 100%;
  position: relative;
  .title {
    color: var(--text-kyy-color-text-1, #1A2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    margin:20px 24px 20px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .recent-empty {
    width: 100%;
    position: absolute;
    top: 64px;
    left: 8px;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    img {
      width: 200px;
      height: 200px;
    }
    .tip{
      color: var(--text-kyy_color_text_2, #516082);

    }
  }
  .t-menu-container {
    width: calc(100% - 48px);
    position: absolute;
    top: 64px;
    left: 24px;
    bottom: 0;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .anchor-list {
    position: fixed;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
  }
  .t-anchor {
    width: auto;
    background-color: transparent;

    :deep(.t-anchor__line) {
      display: none;
    }
    :deep(.t-anchor__item) {
      padding: 0 16px;
    }
    :deep(.t-anchor__item-link) {
      width: 21px;
      height: 21px;
      font-size: 14px;

      text-align: center;
      color: #2069e3;
    }
    :deep(.activeAnchorTitle > a) {
      background: #daecff;
      border-radius: 4px;
      color: #2069e3;
    }
  }
  :deep(.t-default-menu__inner .t-menu) {
    padding: 0;
  }
  :deep(.t-default-menu:not(.t-menu--dark) .t-menu__item.t-is-active:not(.t-is-opened)) {
    background: transparent !important;
    color: #13161b !important;
    &:hover {
      background: #f0f8ff !important;
      border-radius: 4px;
    }
  }
  :deep(.t-default-menu .t-menu__item) {
    position: relative;
    color: var(--text-kyy-color-text-1, #1A2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */
    height: 68px !important;
    padding-left: 16px !important;
    padding-right: 16px !important;
    &:hover {
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_list_hover, #F3F6FA) !important;
    }
  }
  .anchor-title {
    color: var(--text-kyy-color-text-2, #516082);
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    padding: 12px 0;
  }
  .avatar-icon {
    margin-right: 12px;
  }
  .user-info {
    .user-name {
      display: flex;
      align-items: flex-start;
    }
  }
  .act-groups {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    .t-button {
      min-width: auto;
    }
    .t-button--shape-circle{
      border: none;
      background-color: transparent;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      img {
        width: 36px;
        height: 36px;
      }
    }
  }
}
</style>
