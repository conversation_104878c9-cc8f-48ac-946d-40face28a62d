import { loadAcountCards } from '@renderer/views/message/service/accountUtils';
import { getImCardIds, getCards, getStaff, getOpenid, getPlatform, getCurrentAccount } from "@renderer/utils/auth";
import { createMessage, searchPairs } from '@renderer/api/contacts/api/common';
import {
  getPrivateChatApi,
  createPrivate<PERSON>hat<PERSON><PERSON>,
  getGroupChatApi,
  getGroupMemberListApi,
  updatePrivate<PERSON>hat<PERSON><PERSON>,
  updateGroupMemberApi,
  imSearchApi,
  makeFilePreviewApi,
  getVoiceMsgTextApi,
  groupFileSync,
  groupFileSyncRevoke,
  getGroupCardChatApi,
  refreshPrivateChatApi,
  refreshGroupChat<PERSON><PERSON>,
  listGroupLabel,
  updateHelperChatListApi,
  getAllChatListApi
} from "@renderer/api/im/api";
import { useMessageStore } from "./store";

import {
  IGroupMember,
  IRelationGroup,
  IRelationPrivate,
} from "@renderer/api/im/model/relation";

import { v4 as uuidv4 } from "uuid";

import { __awaiter } from "tslib";
import { getStsToken } from '@renderer/api/cloud';

// import OSS from 'ali-oss';
import moment from 'moment';

import {
  getSessionByPrivateRelation2,
  getSessionByGroupRelation,
  getSessionLocalIdByCards,
  getAssistantsSessions,
  getSessionByPairs,
  getSessionByGroup,
  getGroupData,
  getpairSessionMember
} from "./utils";
import { combMsgInfo, _transPlatformDetail } from '@/views/identitycard/data';
import { innerCardsDetails, outerCardsDetails, platformCardsDetails } from "@renderer/api/contacts/api/follow";
import { logHandler } from "@renderer/log";
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer, fs, path } = LynkerSDK;


/**
 * 获取所有会话列表，三合一
 */
export const getAllSessions = async (cards?: string[],) => {
  const cardIds = cards?.length > 0 ? cards : getImCardIds();
  const res = await getAllChatListApi({cardIds,  showReserveSession:true, sortByJoinTimeDesc: true});
  const { pairs, assistants, groups } = res.data?.data
  const { sessions, members } = getPrivateSessions2(pairs)
  const { groupSessions, groupData} = getGroupSessions2(groups)
  const assistansSessions = getAllHelperChatList2(assistants)
  return {
    sessions: [...sessions, ...groupSessions, ...assistansSessions],
    members,
    groups: groupData,
  };
}
/**
 * 获取所有单聊会话列表
 * @param list
 * @returns
 */
export const getPrivateSessions2 = (list) => {
  const sessions = []
  let members = [];
  list.forEach((item) => {
    if (!item.card || !item.myCard) {
      console.error('===>getPrivateSessions2', item);
      return
    };
    const session = getSessionByPairs(item);
    sessions.push(session);
    const peerInfo: ConversationMemberToSave = getpairSessionMember(
      session.localSessionId,
      item.card,
      item.comment,
      item.describe
    );
    const mainInfo: ConversationMemberToSave = getpairSessionMember(
      session.localSessionId,
      item.myCard
    );
    if(item.bindCard) {
      peerInfo.bindCard = item.bindCard
    }
    if(item.myBindCard) {
      mainInfo.myBindCard = item.myBindCard
    }
    console.log('===>getPrivateSessions2', item, peerInfo);
    members = members.concat([mainInfo, peerInfo]);
  });
  return { sessions, members };
};
/**
 * 获取所有群聊会话列表
 */
export const getGroupSessions2 = (list) => {
  const groupSessions: ConversationToSave[] = [];
  const groupData = []
  list.forEach(item => {
    const sessionUsers = getSessionByGroup(item);
    groupSessions.push(...sessionUsers);
    const group = getGroupData(item)
    groupData.push(group);
  });
  return { groupSessions, groupData};
}
/**
 * 获取所有群聊成员
 */
export const getGroupMember = async(list) => {
  // 群所有成员
  const memberResList = await Promise.all(
    list.map((item) =>{
      if(item.group){
        return  getPagedList(
            getGroupMemberListApi,
            { member: { group: item.group } },
            ["data", "array", "members", "arr"]
          )
      }
    })
  );
  const allMembers = []
  list.map((item, index) => {
     //将群成员 members 字段赋给群
     if (memberResList[index]?.length === 0) return;
      const member = memberResList[index].map((m) => getGroupSessionMember(item.group, m));
      allMembers.push(...member);
  });
  return allMembers;
}
/**
 * 获取所有小助手会话
 */

const getAllHelperChatList2 = (list) => {
  const sessions = []
  list.forEach(item => {
   const chat = getAssistantsSessions(item)
   sessions.push(chat)
  });
  return sessions;
}

export const getPrivateSessionInfo = async (mainId: string, peerId: string) => {
  try {
      const { data } = await getPrivateChatApi(mainId, peerId);
      return data.data as IRelationPrivate;
  } catch (error) {
    console.error('===>getPrivateChatApi', error);
    // MessagePlugin.error(error.message);
  }

};

// 检查当前身份的单聊和群聊会话
export const checkPrivateOrGroupSessionInfo = async (params: {
  // 自己的身份卡
  main: string;
  // 单聊对方的身份卡
  peer?: string;
  // 群id
  group?: string;
  // 助手id
  assistant?: string;
  // 扩展信息
  extInfo?: {
    // 来源
    type: 'service_content',
    // 内容
    content: any
  }
}) => {
  let noChat = true;
  if (params.group) {
    const info = await getGroupInfo(params.group, params.main);
    const member = info?.members?.find((item) => item.openid === params.main) ?? { removed: true };
    noChat = !!(info?.removed || member?.removed);
  } else if (params.peer) {
    const { data } = await searchPairs({ mains: [params.main], peers: [params.peer] });
    if (data.data?.rela) {
      const kys = Object.keys(data.data.rela);
      noChat = !kys.length
    }else{
      noChat = true
    }
    if (noChat) {
      noChat = !(await getCoWorkerRelation(params.main, params.peer))
    }
  }
  return noChat;
};

export const loadPrivateSession = async (main: string, peer: string, originData?:any) => {
  let res = await getPrivateSession(main, peer);
  console.log('=====>getPrivateSession',res,originData);
  if (res && res.session.relation) {
    return res;
  }

  const relationInfo = await combMsgInfo(peer, main);
  const members = relationInfo.attachment.member;
  const m = members?.[0];
  const p = members?.[1];
  if (m?.openId && m.openId === p?.openId) {
    const currentAccountTemp = getCurrentAccount();
    const msgError = new Error(`[自己加自己]`);
    const msgErrorStack = msgError.stack;
    const msgErrorSession = `[自己加自己] main:[${m.cardId}] peer:[${p.cardId}] openid:[${m.openId}] params:${JSON.stringify({peer, main})} relationInfo:${JSON.stringify(relationInfo)};originData:${JSON.stringify(originData)}`
    logHandler({ name: 'im-会话错误', info: msgErrorSession, desc: `name:${currentAccountTemp?.name}; openid:${currentAccountTemp?.openid}；${msgErrorStack}` });
  }
  console.log('====relationInfo', relationInfo)
  // 平台身份与平台身份的平台会话；员工身份与平台身份的平台会话；
  if (relationInfo.origin === 'CO_WORKER' || relationInfo.origin === 'PLATFORM_FRIEND' || relationInfo.origin === 'TEMPORARY') {
    relationInfo.attachment.member.forEach((item) => {
      item.jobId = `${item.jobId}`;
    });
    await createMessage(relationInfo);
    res = await getPrivateSession(main, peer);
    return res;
  }
  return null;
};

export const getPrivateSession = async (mainId: string, peerId: string) => {
  const data = await getPrivateSessionInfo(mainId, peerId);
  console.log('====>getPairsSearch', data)
  if (!data.main) {
    data.main = mainId
  }
  if (!data.peer) {
    data.peer = peerId
  }
  if(!data?.mainOpenImID || !data?.peerOpenImID){
      logHandler({ name: 'im-构造单聊会话获取pairs', info: JSON.stringify(data), desc: `${mainId} ${peerId}` });
  }
  return parsePrivateRelation2(data);
};

export const createPrivateChat = async (
  parms: Omit<IRelationPrivate, "updated" | "removed">
) => {
  const members = parms.attachment.member;
  const main = members?.[0];
  const peer = members?.[1];

  if (main?.openId && main.openId === peer?.openId) {
    const msgError = new Error(`[自己加自己]`);
    const msgErrorStack = msgError.stack;
    const msgErrorSession = `[自己加自己createPrivateChat] main:[${main.cardId}] peer:[${peer.cardId}] openid:[${main.openId}] parms:${JSON.stringify(parms)}`
    logHandler({ name: 'im-会话错误', info: msgErrorSession, desc: msgErrorStack});
  }
  const { data } = await createPrivateChatApi(parms);
  return data;
};

// 群所有成员
export const getGroupMembers = async (groupId: string) => {
  const memberResList = await getPagedList(
    getGroupMemberListApi,
    { member: { group: groupId } },
    ["data", "array", "members", "arr"]
  );
  return memberResList as IGroupMember[];
};

// 获取群会话成员列表
export const getGroupSessionMembers = async (groupId: string) => {
  const memberResList = await getPagedList(
    getGroupMemberListApi,
    { member: { group: groupId } },
    ["data", "array", "members", "arr"]
  );
  if (memberResList?.length) {
    return memberResList.map((item) => getGroupSessionMember(groupId, item));
  }
  return null;
};

export const getGroupSession = async (groupId: string) => {
  let data = await getGroupSessionInfo(groupId);
  // 群信息不存在可能新增了身份卡，重新获取身份卡再拉一次群信息
  if (!data) {
    await loadAcountCards();
    data = await getGroupSessionInfo(groupId);
  }
  if (data) {
    return parseGroupRelation(data);
  }
  return null;
};

/**
 * 获取群会话信息,并更新群成员信息和GroupInfo
 * @param groupId 群id
 * @param needUpdate 是否更新群成员信息和GroupInfo
 * @returns
 */
export const getGroupSessionInfo = async (groupId: string, needUpdate = true) => {
  const [group, members] = await Promise.all([
    getGroupChatApi(groupId, getImCardIds()),
    getPagedList(getGroupMemberListApi, { member: { group: groupId, get_platform: true } }, [
      "data",
      "array",
      "members",
      "arr",
    ]),
  ]);
  const data = group?.data as IRelationGroup;
  if (!data) return null;
  // 暂存我自己的身份卡
  data.myCards = data?.members;

  if (members?.length) {
    data.members = members;
    // 更新群member
    if (needUpdate){
      const groupMembers = members.map((item) => getGroupSessionMember(groupId, item));
      useMessageStore().onGroupMembersUpdate({ groupId, members: groupMembers });
    }
  } else {
    data.members = null;
  }
  //更新群组信息
  if (needUpdate){
     useMessageStore().updateGroupInfo(data)
  }
  return data;
};

export async function getGroupInfo(groupId: string, card?: string) {
  const data = await getGroupChatApi(groupId, card ? [card] : getImCardIds());
  if (data?.data?.members) {
    data.data.myCards = data.data.members;
  }
  //更新群组信息
  useMessageStore().updateGroupInfo(data.data)
  return data?.data as IRelationGroup;
}

export async function getGroupDetailByCard(groupId: string, card: string) {
  const res = await getGroupCardChatApi(groupId, card);
  return res?.data as IRelationGroup;
}

export async function updateConversation(conversation: ConversationToSave) {
  if (conversation.conversationType === 1) {
    const res = await updatePrivateConversation(conversation)
    return res;
  } else if (conversation.conversationType === 6) {
    const stayOn = conversation.isTop ? 1 : 0
    const noDisturb = conversation.isMute ? 1 : 0
    updateHelperChat({assistantId:conversation.targetId, stayOn, noDisturb}, 'stayOn')
    return true
  } else{
    const res = await updateGroupConversation(conversation);
    return res;
  }
}

export const updateHelperChat = async(data,type) => {
  await updateHelperChatListApi(data)
  if (type === 'stayOn') {
    setTimeout(() => {
      useMessageStore().sortSessions('updateHelperChat');
    }, 200);
  }
}

export async function renewConversationUpdateTime(
  conversation: ConversationToSave
) {
  if (conversation.conversationType === 1) {
    refreshPrivateChatApi(conversation.myCardId, conversation.targetCardId);
  } else {
    refreshGroupChatApi(conversation.targetId);
  }
}

async function updateGroupConversation(conversation: ConversationToSave) {
  try {
    const info = await getGroupInfo(conversation.targetId, conversation.myCardId);
    if (info?.members) {
      const myCard = info.members[0];
      myCard.attachments = myCard.attachments || { openId: getOpenid(), cardId: myCard.openid } as any;
      myCard.attachments.noDisturb = conversation.isMute ? 1 : 0;
      myCard.attachments.stayOn = conversation.isTop ? 1 : 0;
      myCard.pin = conversation.isTop;
      myCard.no_disturb = conversation.isMute;
      myCard.group_type = conversation.group_type;
      await updateGroupMemberApi(myCard);
    } 
    return true
  } catch (error) {
    console.error('===>updateGroupConversation', error);
    return false
  }
}

async function updatePrivateConversation(conversation: ConversationToSave) {
  try {
    const info = await getPrivateSessionInfo(
      conversation.myCardId,
      conversation.targetCardId
    );
    console.log('updatePrivateConversation', info);
    if (info?.origin) {
      info.pin = conversation.isTop ? true : false;
      info.noDisturb = conversation.isMute ? true : false;
      info.groupType = conversation.group_type === 1 ? 'GT_FREQUENTLY' : 'GT_DEFAULT'
      await updatePrivateChatApi(info);
    }
    return true
  } catch (error) {
    console.error('===>updatePrivateConversation', error);
    return false
  }
}

export function getSessionMember(
  id: string,
  attachment: ConversationMemberToSave
): ConversationMemberToSave {
  return {
    sessionId: id,
    openId: attachment.openId,
    cardId: attachment.cardId,
    openImId: attachment.openImUserID || attachment.openId,
    staffName: attachment.staffName,
    nickname: attachment.nickname,
    avatar: attachment.avatar,
    roles: attachment.roles,
    comment: attachment.comment,
    describe: attachment.describe,
    teamId: attachment.teamId,
    teamName: attachment.teamName,
    jobId: attachment.jobId,
    jobName: attachment.jobName,
    departmentId: attachment.departmentId,
    departmentName: attachment.departmentName,
    stayOn: attachment.stayOn,
    noDisturb: attachment.noDisturb,
    viewHistory: attachment.viewHistory,
    label_id: null,
    joined: 0,
  };
}
export function getSessionMemberByCard(
  id: string,
  card: ConversationMemberToSave,
  others?: {
    comment?: string;
    describe?: string;
  }
): ConversationMemberToSave {
  return {
    sessionId: id,
    openId: card.openid,
    cardId: card.cardId,
    openImId: card.openImUserID || card.openid,
    staffName: card.cardName,
    nickname: card.cardName,
    avatar: card.avatar,
    roles: card.roles,
    teamId: card.internalTeamId,
    teamName: card.internalTeamName,
    jobId: card.departments?.[0]?.jobId,
    jobName: card.departments?.[0]?.jobName,
    departmentId: card.departments?.[0]?.id,
    departmentName: card.departments?.[0]?.name,
    comment: others?.comment,
    describe: others?.describe,
    label_id: null,
    joined: 0,
  };
}
export function getGroupSessionMember(id: string, member: IGroupMember): ConversationMemberToSave {

  return {
    sessionId: id,
    openId: member?.attachments?.openId,
    cardId: member?.attachments?.cardId,
    openImId: member?.attachments?.openImUserID || member?.attachments?.openId,
    staffName: member?.attachments?.staffName || '',
    nickname: member?.attachments?.nickname,
    avatar: member?.attachments?.avatar,
    roles: Array.isArray(member?.roles) ? member?.roles.join(',') : member?.roles,
    comment: member?.attachments?.comment,
    describe: member?.attachments?.describe,
    teamId: member?.attachments?.teamId,
    teamName: member?.attachments?.teamName,
    jobId: member?.attachments?.jobId,
    jobName: member?.attachments?.jobName,
    departmentId: member?.attachments?.departmentId,
    departmentName: member?.attachments?.departmentName,
    stayOn: member?.attachments?.stayOn,
    noDisturb: member?.attachments?.noDisturb,
    viewHistory: member?.attachments?.viewHistory,
    label_id: member?.label_id,
    joined: !Number.isNaN(member?.joined) ? member?.joined * 1000 : null,
    group_type: member?.group_type || 0,
  };
}

function getAssistantMember(groupMember: IGroupMember) {
  return {
    sessionId: groupMember.group,
    openId: groupMember.openid,
    cardId: groupMember.openid,
    openImId: groupMember.openImUserID || groupMember.openid,
    describe: null,
    staffName: null,
    nickname: null,
    avatar: null,
    roles: null,
    comment: null,
    teamId: null,
    teamName: null,
    jobId: null,
    jobName: null,
    departmentId: null,
    departmentName: null,
    label_id: null,
    joined: 0,
  };
}

export function getGroupItem(group: GroupToSave): GroupToSave {
  return {
    attachment: {
      avatar: group.attachment?.avatar || (group as any)?.avatar,
      teamName: group.attachment?.teamName,
      hasInviteMember: group.attachment?.hasInviteMember,
      hasLookForHistory: group.attachment?.hasLookForHistory,
      openCloudFile: group.attachment?.openCloudFile,
      openPictureFile: group.attachment?.openPictureFile,
    },
    created: group.created,
    group: group.group,
    owner: group.owner,
    name: group.name,
    path: group.path,
    total: group.total,
    type: group.type,
    updated: group.updated,
    host: group.host,
    creator: group.creator,
    removed: group.removed,
    scene: group.scene,
    team: group.team,
    creator_card: group.creator_card,
    sync_disk: group.sync_disk ?? false,
    disk_folder: group.disk_folder ?? null,
  };
}

export function parsePrivateRelation2(relation: IRelationPrivate) {
  const session = getSessionByPrivateRelation2(relation);
  const main = relation.myCard
  const mainInfo: ConversationMemberToSave = getSessionMemberByCard(
    session.localSessionId,
    main,
  );
  if(relation.myBindCard) {
    mainInfo.myBindCard = relation.myBindCard
  }
  const peer = relation.card

  const peerInfo: ConversationMemberToSave = getSessionMemberByCard(
    session.localSessionId,
    peer,
    {comment:relation.comment, describe:relation.describe}
  );
  if(relation.bindCard) {
    peerInfo.bindCard = relation.bindCard
  }
  return { session, members: [mainInfo, peerInfo] };
}

export function parseGroupRelation(relation: IRelationGroup) {
  const session = getSessionByGroupRelation(relation);
  // 助手只有自己一个人在群, 且 attachmets 为空
  const groupMembers = [0, 1, 2, 3, 10, 15, 20, 22].includes(relation.type) ? relation.members : relation.myCards;
  const members: ConversationMemberToSave[] = groupMembers?.map((member) => (member?.attachments
    ? getGroupSessionMember(session.localSessionId, member)
    : getAssistantMember(member)));
  const myGroupCards = relation.myCards;
  const group = getGroupItem(relation);
  return { session, members, group, myGroupCards };
}

export function parseGroupSessionInfo(relation: IRelationGroup) {
  const session = getSessionByGroupRelation(relation);
  // 助手只有自己一个人在群, 且 attachmets 为空
  const groupMembers = [0, 1, 2, 3, 10, 15, 20, 22].includes(relation.type) ? relation.members : relation.myCards;
  const members: ConversationMemberToSave[] = groupMembers?.map((member) => (member?.attachments
    ? getGroupSessionMember(session.localSessionId, member)
    : getAssistantMember(member)));
  const myGroupCards = relation.myCards;
  const group = getGroupItem(relation);
  return { session, members, group, myGroupCards };
}

/**
 * 通过我的身份卡获取群会话
 * @param session 群会话
 * @param member 身份卡
 * @returns
 */
export function getGroupCardSession(
  session: ConversationToSave,
  member: IGroupMember
): ConversationToSave {
  return {
    ...session,
    localSessionId: getSessionLocalIdByCards(member.openid, session.targetId),
    myCardId: member.openid,
    myOpenImId: member.open_im_id ?? session.myOpenImId,
    targetCardId: session.targetId,
    isMute: member.attachments?.noDisturb === 1,
    isTop: member.attachments?.stayOn === 1,
    updateTime: (member.latest || member.joined || 0) * 1000,
    createTime: (member.joined ?? 0) * 1000,
  };
}
// 请求判断是否同事关系 内部->内部 平台->平台 外部->外部 内部->外部 内部->平台
export async function getCoWorkerRelation(main: string, peer: string) {
  const ids = [main, peer];
  if (ids.every((c) => c.startsWith('$'))) {
    const getInners = ids?.map(v => v.substring(1));
    const res = await innerCardsDetails({ ids: getInners });
    const list = res?.data?.data as Array<any>;
    return list[0]?.teamId === list[1]?.teamId;

  } if (ids.every((c) => c.startsWith('#'))) {
    const outers = ids?.map(v => v.substring(1));
    const res = await outerCardsDetails({ ids: outers });
    const list = res?.data?.data as Array<any>;
    return list[0]?.teamId === list[1]?.teamId;

  } if (ids.every((c) => /^PT/.test(c))) {
    // 平台->平台
    const platform = ids.filter((c) => /^PT/.test(c))?.map(v => v.substring(2));
    const res = await platformCardsDetails({ ids:platform });
    const list = [];
    res?.data?.data.forEach(val => {
      list.push(_transPlatformDetail(val))
    });
    return list[0]?.teamId === list[1]?.teamId;

  } if (ids.every((c) => c.startsWith('$') || c.startsWith('#'))) {
    const getOuters = ids.filter((c) => c.startsWith('#'))?.map(v => v.substring(1));
    const getInners = ids.filter((c) => c.startsWith('$'))?.map(v => v.substring(1));
    const [outerRes, innerRes] = await Promise.all([
      outerCardsDetails({ ids: getOuters }),
      innerCardsDetails({ ids: getInners }),
    ]);
    const outerCard = outerRes?.data?.data?.[0];
    const innerCard = innerRes?.data?.data?.[0];
    return innerCard?.teamId === outerCard?.internal_teamId;

  } if (ids.every((c) => c.startsWith('$') || /^PT/.test(c))) {
    const getOuters = ids.filter((c) => /^PT/.test(c))?.map(v => v.substring(2));
    const getInners = ids.filter((c) => c.startsWith('$'))?.map(v => v.substring(1));
    const [outerRes, innerRes] = await Promise.all([
      platformCardsDetails({ ids: getOuters }),
      innerCardsDetails({ ids: getInners }),
    ]);
    const outerCard = _transPlatformDetail(outerRes?.data?.data?.[0]);
    const innerCard = innerRes?.data?.data?.[0];
    return innerCard?.teamId === outerCard?.teamId;

  }
  return false;

}

// 分页请求一次性获取所有数据，不考虑分页
type GetPagedListApi = <
  T extends (params: any, index: number, size: number) => Promise<any>
>(
  apiMethod: T,
  params: Parameters<T>[0],
  listPath: string[],
  count?:number, // 指定返回多少条数据
) => Promise<any[]>;
const getPagedList: GetPagedListApi = async (apiMethod, params, listPath, count?:number) => {
  let index = 0;
  const page = count || 100;

  const allData = [];
  try {
    do {
      const res = await apiMethod(params, index, page);
      const data = listPath.reduce((prev, cur) => prev?.[cur], res);
      if (Array.isArray(data) && data.length > 0) {
        allData.push(...data);
        if (count) { // 指定返回count条数据
          index = -1;
        } else {
          // 分页判断逻辑
          index = data.length < page ? -1 : index + 1;
        }
      } else {
        index = -1;
      }
    } while (index > 0);
    return allData;
  } catch (error) {
    return allData;
  }
};

export const imSearch = async (keyword: string, extend?) => {
  const outers = getCards() ?? [];
  const inners = getStaff() ?? [];
  const platforms = getPlatform() ?? [];
  const { params } = extend ?? {};
  const teams = [];
  [...outers, ...inners, ...platforms].forEach((element) => {
    const teamId = `${element.teamId ?? ""}`;
    if (teamId.length && !teams.includes(teamId)) {
      teams.push(`${element.teamId}`);
    }
  });
  const { data } = await imSearchApi({
    title: keyword,
    teams,
    innerCardIds: inners.map((item) => item.uuid),
    outerCardIds: outers.map((item) => item.uuid),
    platformCardIds: platforms.map((item) => item.uuid),
    ...params
  });
  return data;
};

// --------------------------------- 上传文件 ---------------------------------

const getAccessParams = (res) => {
  const { data } = res.data;
  return {
    accessKeyId: data.AccessKeyId,
    accessKeySecret: data.AccessKeySecret,
    stsToken: data.SecurityToken,
  };
};

export async function getFilePreviewId(
  url: string,
  name: string,
  size: number
) {
  const { data } = await makeFilePreviewApi({ url, name, size });
  return data as { file_id: string };
}

export async function uploadFile(uploadFile: { name: string, type: string, path: string }, uploadProgress:any): Promise<{url: string}> {
  return new Promise((resolve, reject) => {
    fs.readFile(path.normalize(uploadFile.path), async (err, data) => {
      try {
        if (err) {
          return reject(err);
        }

        const res = await getStsToken();
        const client = new OSS({
            // 从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）
            ...getAccessParams(res),
            region: 'oss-cn-shenzhen',
            // 从STS服务获取的安全令牌（SecurityToken）
            bucket: 'kuaiyouyi',
            refreshSTSToken: async () => {
                // 向搭建的STS服务获取临时访问凭证
                const res = await getStsToken();
                return getAccessParams(res);
            },
            // 刷新临时访问凭证的时间间隔，单位为毫秒
            refreshSTSTokenInterval: 300000,
        });

        // 文件类型位 mime 类型的前半部分
        const fileName = getFilePath(uploadFile.type);

        // !!! 阿里云put上传file参数必须是  Buffer/Blob/File 类型
        const multiRes = await client.multipartUpload(fileName, data, {
          progress: (p, cpt, res) => {
            uploadProgress(p, cpt, res, client);
          },
          // 每传输完一个分片 进度条会走一个百分比 不设置的话由于partsize过大 可能导致很长时间进度条都看不到效果
          partSize: 102400 // 指定上传的每个分片的大小，范围为100 KB~5 GB。单个分片默认大小为1 * 1024 * 1024（即1 MB）
          // (p, partInfo, partRes) => {
          //   console.log(p, partInfo, partRes);
          // }
        }) as any;
        const uploadResult = multiRes.res?.requestUrls?.[0];
        const url = uploadResult?.split('?')[0] as string;
        console.log(multiRes);
        if (url?.length) {
          resolve({ url });
        } else {
          reject('上传失败');
        }

      } catch (error) {
        reject(error);
      }
    });
  });
}

const getFilePath = (type: string) => {
  const day = moment().format("YYYY-MM-DD");
  const ext = type ? `.${type}` : "";
  return `message/${day}/${uuidv4()}${ext}`;
};

// --------------------------------- 语音转文字 ---------------------------------

export const getVoiceMsgText = async (id: string) => {
  const { data } = await getVoiceMsgTextApi(id);
  return data;
};

export const setGroupFileSync = async (dataObj) => {
  const { data } = await groupFileSync(dataObj);
  return data;
};

export const delGroupFileSyncRevoke = async (id: string | number) => {
  const { data } = await groupFileSyncRevoke(id);
  return data;
};


// --------------------------------- 群身份 ---------------------------------
export const reqGroupLabels = async (group_id = ''): Promise<ConversationMemberLabel[]> => {
  try {
    const { data } = await listGroupLabel({group_id}) as { data: { labels: ConversationMemberLabel[] } };
    return data.labels;
  } catch (error) {
    return [];
  }
}
