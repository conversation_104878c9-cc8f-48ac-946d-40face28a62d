import { lssClientOrganizeMemRequest as client_orgRequest, lssSquareMemRequest } from "@renderer/utils/apiRequest";
import { getAssociationTeamID } from "@renderer/views/association/utils/auth";


// 获取会刊设置信息
export function getIdleCountAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: '/idle/channel/examine/count',
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}