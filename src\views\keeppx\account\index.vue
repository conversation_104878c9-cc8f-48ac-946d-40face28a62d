<template>
  <div class="container containerPc">
    <div class="container">
      <div class="logo">
        <img v-if="linkInfo.logo" :src="linkInfo.logo" alt="">
        <img v-else src="@/assets/svg/building-fill.svg" alt="">
      </div>
      <div class="invite">
          {{ linkInfo?.name }} {{ t('account.invite') }}
      </div>
      <div class="team">
        {{ linkInfo?.team }}
      </div>
      <div class="department">
        {{ t('account.department') }}{{ linkInfo?.department_name }}
      </div>
      <div class="btn-group">
        <div class="login">
          <div>{{ t('account.accountExist') }}</div>
          <t-button class="btn" @click="goLogin">{{ t('account.login') }}</t-button>
        </div>
        <div class="register">
          <div>{{ t('account.noAccount') }}</div>
          <t-button class="btn" variant="outline" theme="default" @click="goRegister">{{ t('account.register') }}</t-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useAccountStore } from '@/stores/account';
const accountStore = useAccountStore();
const { t } = useI18n();
const router = useRouter();
const linkInfo:any = ref(accountStore.$state.linkInfo);

const goLogin = () => {
  // router.push({name: 'accountLogin',  query: {
  //     redirect: encodeURIComponent(router.currentRoute.value.fullPath),
  //   },
  // });
  router.push({name: 'accountLogin'});
}

const goRegister = () => {
  // router.push({name: 'accountRegister', query: {
  //     redirect: encodeURIComponent(router.currentRoute.value.fullPath),
  //   },
  // });
  router.push({name: 'accountRegister'});
}
</script>

<style lang="less" scoped>
@import url('../css/base.less');
.f-c-c {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
// @media screen and (max-width:600px) {
  .container {
    // max-width: 600px;
    // min-height: 100vh;
    // margin: auto;
    // // position: absolute;
    // // top: 0;
    // // bottom: 0;
    // // left: 50%;
    // // transform: translateX(-50%);
    // background-color: #fff;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    .logo {
      width: 64px;
      height: 64px;
      font-size: 0;
      margin-top: 88px;
      margin-bottom: 28px;
      img{
        width:100%;
        height:100%;
      }
    }
    .invite {
      font-size: 17px;
      font-family: Microsoft YaHei, Microsoft YaHei-Regular;
      color: #1A2139;
      line-height: 22px;
    }
    .team {
      font-size: 18px;
      font-family: Microsoft YaHei, Microsoft YaHei-Bold;
      font-weight: 700;
      color: #1A2139;
      line-height: 24px;
      margin-top: 8px;
      margin-bottom: 4px;
    }
    .department {
      font-size: 17px;
      font-family: Microsoft YaHei, Microsoft YaHei-Regular;
      color: #828DA5;
      line-height: 22px;
    }
    .btn-group {
      width: 320px;
      font-size: 17px;
      font-family: Microsoft YaHei, Microsoft YaHei-Regular;
      font-weight: 400;
      text-align: left;
      color: #1A2139;
      line-height: 22px;
      margin-top: 32px;
      .register {
        margin-top: 24px;
      }
      .btn {
        width:100%;
        height: 40px;
        font-size: 17px;
        line-height: 24px;
        margin-top: 8px;
        border-radius:4px;
      }
      .register .t-button{
        color:#4D5EFF;
        border: 1px solid #4D5EFF;
      }
    }
  }
// }
</style>
