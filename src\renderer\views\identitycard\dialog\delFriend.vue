<template>
  <div>
    <t-dialog
      v-if="type === 'personal'"
      :visible="visible"
      theme="warning"
      :header="t('identity.deleteContact')"
      width="300"
      :closeBtn="false"
      @close="close"
    >
      <template #body>
        <div>
          <div class="tip">{{ t('identity.afterDeleteTip') }}</div>
          <div>
            <t-checkbox v-model="clearMsg">{{ t('identity.clearChat') }}</t-checkbox>
          </div>
        </div>
      </template>
      <template #footer>
        <div style="margin-right: 8px">
          <t-button
            class="btn cancel"
            variant="outline"
            theme="default"
            @click="close"
          >{{ t('account.cancel') }}</t-button>
          <t-button
            class="btn confirm"
            theme="danger"
            variant="base"
            @click="confirm"
          >{{ t('identity.delete') }}</t-button>
        </div>
      </template>
    </t-dialog>
    <t-dialog v-if="type === 'outer'"  :visible="visible" :header="t('identity.deleteContact')" width="300" :footer="false" @close="close">
      <template #body>
        <div>
          <div class="f-align">
            {{ t('identity.deleteContactExamineTip') }}
          </div>
          <div class="remark">
            <t-input v-model="remark" clearable :placeholder="t('identity.notesInfo')"></t-input>
          </div>
          <div style="margin-bottom: 10px;">
            <t-checkbox v-model="clearMsg">{{ t('identity.clearChat') }}</t-checkbox>
          </div>
          <div>
            <t-button style="width:100%" @click="confirm">{{ t('identity.send') }}</t-button>
          </div>
        </div>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { delMsg, delApply } from '@renderer/api/identity/api/card';
import { terminationSession } from '@renderer/api/customerService';
import { getOneConversationDetail, deleteConversationAllMessages } from '@renderer/views/message/service/ipcIMBridge';
import { MessagePlugin } from 'tdesign-vue-next';
import { ref, computed } from 'vue';
import { cardIdType, cardData } from '../data'
import { getOpenid } from '@renderer/utils/auth';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  relation: { type: String, default: ''},
  cardId: { type: String, default: ''},
  myId: { type: String, default: '' },
  cardOpenid: { type: String, default: '' },
  cardTeamId: { type: [String, Number], default: '' },
})
const clearMsg = ref(true);
const type = computed(() => cardIdType(props.myId));
const remark = ref('');

const emits = defineEmits(['update:visible', 'onconfirm']);
const close = () => {
  clearMsg.value = true;
  remark.value = '';
  emits('update:visible', false);
};
const confirm = () => {
  type.value === 'personal' ? del() : apply();
};
const delConsilt = () => {
  terminationSession({
    reliever: props.myId,
    the_other_side: props.cardId,
    team_id: props.cardTeamId,
    keep_history: clearMsg.value ? 2 : 1,
  }).then(()=>{
     emits('onconfirm');
    close();
  })
}
const del = () => {
  console.log('props', props)
  if(props.relation === 'CONSULT'){
    delConsilt()
    return;
  }
  delMsg(props.myId, props.cardId, clearMsg.value ? 2 : 1 ).then(async () => {
    if(clearMsg.value && props.relation === 'FRIEND'){
      const info = { conversationType: 1, targetId: props.cardId, myOpenImId: props.myId  };
      const result = await getOneConversationDetail(info);
      deleteConversationAllMessages({conversationID:result.data.conversationID, bindUserID:result.data.bindUserID})
    }
    emits('onconfirm');
    close();
  }).catch(err => {
    MessagePlugin.error(err.response.data);
    close();
  })
};
const apply = () => {
  cardData(props.myId).then(res => {
    const params = {
      applicant_card_id: props.myId,
      applicant_openid: getOpenid(),
      friend_card_id: props.cardId,
      friend_openid: props.cardOpenid,
      remark: remark.value,
      is_clear: clearMsg.value ? 1 : 2,
    }
    delApply(params, res.teamId).then(res => {
      emits('onconfirm');
      close();
    }).catch(err => {
      MessagePlugin.error(err.response.data.message);
      close();
    })
  })
}
</script>

<style lang="less" scoped>
.tip {
  font-size: 14px;
  
  font-weight: 400;
  text-align: left;
  color: #717376;
  line-height: 22px;
}
.f-align {
  display: flex;
  align-items: center;
}
.remark {
  margin: 12px 0 12px;
}
</style>
