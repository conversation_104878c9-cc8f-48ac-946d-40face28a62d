<template>
  <div class="container containerPc">
    <div class="lang">
      <t-dropdown attch="body" :options="options" trigger="click" @click="changeLanguage">
        <t-space>
          <t-button variant="text">
            {{ useLang }}
            <template #suffix> <t-icon name="chevron-down" size="16" /></template>
          </t-button>
        </t-space>
      </t-dropdown>
    </div>
    <div class="logo">
      <img src="@/assets/img/logo_img_logo.svg" alt="" />
    </div>
    <div class="logo-name">
      {{ t('account.welcomkyy') }}
    </div>
    <!--    <div class="login-tab">-->
    <!--      <div :class="['tab-item', loginType === 2 && 'active']" @click="changeTab(2)">{{ t('account.passwordLogin') }}</div>-->
    <!--      <div :class="['tab-item', loginType === 1 && 'active']" style="margin: 0 25px" @click="changeTab(1)">{{ t('account.captcha') }}</div>-->
    <!--      <div :class="['tab-item', loginType === 4 && 'active']" style="margin-right:25px" @click="changeTab(4)">{{ t('account.emailLogin') }}</div> -->
    <!--      <div :class="['tab-item', loginType === 3 && 'active']" @click="changeTab(3)">{{ t('account.scanLogin') }}</div> -->
    <!--    </div>-->
    <div class="login-tab-content">
      <div v-show="loginType === 1" class="content-item password mt12">
        <div class="adornment tel-code-style">
          <!-- <area-code v-model="ma.region" class="area" />
          <div class="adornment--line"></div>
          <t-input v-model="ma.mobile" class="phone" clearable :placeholder="t('account.inputTel')" /> -->

          <tel-code
            :required="true"
            :tel-code="ma.region"
            :tel-number="ma.mobile"
            @changeCode="(v) => (ma.region = v)"
            @changeTel="(v) => (ma.mobile = v)"
          />
        </div>
        <t-input v-model="ma.code" class="mt12 codeBox" clearable :placeholder="t('account.inputCode')">
          <template #suffix>
            <div style="display: flex; align-items: center">
              <div class="suffix--line"></div>
              <div v-if="countdown <= 0" class="verify pointer" id="getCode" aria-role="button" @click="checkSM">
                {{ countdown === -2 ? t('account.sendCode') : t('account.resend') }}
              </div>
              <div v-else class="verify">{{ `${countdown}s` }}</div>
            </div>
          </template>
        </t-input>
      </div>
      <div v-show="loginType === 2" class="content-item password">
        <t-input v-model="ma.mobile" class="mt12" clearable :placeholder="t('account.inputTelOrEmail')" />
        <t-input v-model="ma.password" class="mt12" type="password" clearable :placeholder="t('account.inputPw')">

        </t-input>
      </div>
      <!-- <div v-show="loginType === 4" class="content-item password mt12">
        <t-input
          v-model="ea.mail"
          class="mt12"
          autofocus
          :placeholder="t('account.inputEamil')"
        />
        <t-input
          v-model="ea.password"
          class="mt12"
          type="password"
          :placeholder="t('account.inputPw')"
        />
      </div> -->
      <div v-show="loginType === 3" class="content-item password mt12">
        <div class="qrcode-container">
          <div v-if="scanSuc" class="scan-suc-container">
            <img src="" alt="" />
            <div class="scan-suc">{{ t('account.scanSuc') }}</div>
            <div class="tip">{{ t('account.scanTip') }}</div>
          </div>
          <div v-else class="qrcode">
            <img :src="qrcodeBase64" alt="" />
          </div>
          <div class="f-c scan-tip">
            <img style="margin-right: 8px" class="scan-logo" src="@/assets/svg/scan.svg" alt="" />
            <span>{{ t('account.open') }}</span>
            <span style="color: #4d5eff">{{ t('account.appName') }}</span>
            <span>{{ t('account.scanLogin') }}</span>
          </div>
        </div>
      </div>
      <div v-show="loginType !== 3" class="login-act">
        <t-button
          v-loading="loginLoading"
          class="login-btn"
          :disabled="loginDisabled"
          block
          theme="primary"
          variant="base"
          @touchstart.stop.prevent="login"
          @click="login"
          >{{ t('account.login') }}</t-button
        >
        <!-- <div class="mt12 login-act-ext">
          <div v-show="loginType === 2" class="pointer">{{ t('account.forget') }}</div>
        </div> -->
      </div>
    </div>

    <div class="forget-bottom">
      <div class="pointer">
        <!--        <span v-if="loginType === 2" @click="forget">{{ t('account.forget') }}</span>-->
        <div class="pointer" @click="goRegister">{{ t('account.registerAccount') }}</div>
      </div>
      <div class="pointer" @click="changeTab(loginType === 1 ? 2 : 1)">
        {{ loginType === 1 ? t('account.passwordLogin') : t('account.captcha') }}
      </div>
    </div>

    <!-- <div class="content-bottom mt12"> -->
    <!--      <div class="pointer">{{ t('account.exclusiveAccount') }}</div>-->
    <!--      <div class="split-line"></div>-->
    <!-- <div class="pointer" @click="goRegister">{{ t('account.registerAccount') }}</div> -->
    <!-- </div> -->
  </div>

  <!--  <t-drawer v-model:visible="forgetVisible" :header="false" :footer="false" size="100%">-->
  <!--    <forget-pass-word v-if="forgetVisible" ref="setPhoneDom" :forgetInfo="forgetInfo" @back="forgetVisible = false" @confirm="forgetConfirm" />-->
  <!--  </t-drawer>-->

  <tip
    v-model:visible="checkPhoneVisible"
    :tip="checkPhoneTip"
    :btn-confirm="t('zx.other.confirmChange')"
    :btn-cancel="t('account.cancel')"
    @onconfirm="changeRegion"
  />
</template>

<script setup lang="ts">
import tip from './tipCommon.vue';
import { useRouter, useRoute } from 'vue-router';
import { onMounted, reactive, ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { err_reason, handleTrim, checkPhoneAndMatch } from './util';
import { getJwt, getIdentifyCode, loginAccount, loginAccountV2, getQrCode, getProfile } from '@/api/account/login';
import { useI18n } from 'vue-i18n';
import areaCode from '@/components/keeppx/account/AreaCode.vue';
import telCode from '@/components/keeppx/account/TelCode.vue';
import qrcodeParser from 'qrcode-parser';
import {
  setAccesstoken,
  setLang,
  getLang,
  setOpenid,
  removeOpenid,
  setSMDeviceId,
  removeSMDeviceId,
} from '@/utils/auth';
import _ from 'lodash';
import forgetPassWord from './components/forgetPassWord.vue';
import { encrypt } from '@/utils/myUtils';
import { initSM, dealSmDeviceId, getSMCaptcha, getSMCaptchaResult } from '@/utils/shumei';

const router = useRouter();
const route = useRoute();
import { useAccountStore } from '@/stores/account';
import { setLanguage } from '@/i18n';
const accountStore = useAccountStore();
const redirect = route.query.redirect as string;
// 携带来源参数
const sourceParams = route.query.link as string;
const { t } = useI18n();
const loginType = ref(2);
const loginDisabled = ref(true);
const ma = ref({
  mobile: '',
  region: '86',
  code: '',
  password: '',
});
const ea = ref({
  mail: '',
  password: '',
});
const checkPhoneVisible = ref(false);
const checkPhoneTip = ref('');
const countdown = ref(-2);
const loginLoading = ref(false);
const timer: any = ref(null);
const qrcodeBase64 = ref(''); // 二维码base64地址
const qrcCode = ref(''); // 二维码解析结果
const qrCodeExpire = ref(); // 二维码过期时间
const scanSuc = ref(false);
let checkRegion: any = 0;

const checkSM = () => {
  if (!SMCaptcha.value) {
    return;
  }
  getSMCaptchaResult(SMCaptcha.value, getCode);
};

const SMCaptcha = ref(null);
onMounted(async () => {
  try {
    SMCaptcha.value = await getSMCaptcha({ width: 300 });
    console.error(SMCaptcha.value);
  } catch (error) {
    console.error(error);
  }
  // qrCode();
  // setInterval(() => {
  //   if (Math.trunc(Date.now()/1000) > qrCodeExpire.value) {
  //     qrCode();
  //   }
  //   if (loginType.value === 3) {
  //     login();
  //   }
  // }, 1000)
  initSM();
  removeOpenid();
  removeSMDeviceId();
  useLang.value = getLang() ? options.value.find((v) => v.value === getLang()).content : '简体中文';
  options.value.forEach((v) => (v.active = v.value === (getLang() ?? 'zh-cn')));
  setLanguage(getLang() ?? 'zh-cn');
});

const options = ref([
  {
    content: '简体中文',
    value: 'zh-hans-CN',
    active: false,
  },
  {
    content: '繁體中文',
    value: 'zh-hant-MO',
    active: false,
  },
]);
const useLang = ref('');
const changeLanguage = (data) => {
  setLanguage(data.value);
  setLang(data.value);
  useLang.value = options.value.find((v) => v.value === data.value).content;
  options.value.forEach((v) => (v.active = v.value === data.value));
};
const forgetVisible = ref(false);
const forgetInfo = reactive({
  region: '',
  mobile: '',
});
const forgetConfirm = () => {
  console.log('forgetConfirm');
};
// h5屏蔽忘记密码
const forget = () => {
  if (!checkPhone()) return;
  forgetInfo.region = '86';
  forgetInfo.mobile = ma.value.mobile;
  forgetVisible.value = true;
};

const qrCode = async () => {
  const data: any = await getQrCode();
  qrcodeBase64.value = 'data:image/png;base64,' + data.png;
  qrcodeParser(qrcodeBase64.value).then((res) => {
    const qrInfo = JSON.parse(res);
    qrcCode.value = qrInfo.id;
    qrCodeExpire.value = qrInfo.expire;
  });
};

const changeTab = (type: number) => {
  loginType.value = type;
  countdown.value = -2;
  checkLoginDisabled();
  timer.value && (clearInterval(timer.value), (timer.value = null));
};

const changeRegion = () => {
  checkPhoneVisible.value = false;
  ma.value.region = checkRegion.toString();
  ma.value.code ? login() : getCode();
};




// let SMCaptchaVal: any = null;
// const smCaptchaCallback = (SMCaptcha) => {
//     // 验证码实例，可以调用实例上的方法
//     console.log(SMCaptcha, '验证码实例，可以调用实例上的方法')
//     SMCaptchaVal = SMCaptcha;
//     const captchaUuid = SMCaptcha.captchaUuid;
//     // 验证码校验情况回调
//     SMCaptcha.onSuccess(function (data) {
//         // data格式：{rid: '2017080810405655b3377d25de478233', pass: false}
//         console.log(data);
//         if (data.pass) {
//             // 验证通过后
//         }
//         else {
//             // 验证失败后
//         }
//     });
// }

// initSMCaptcha({
//     organization: 'OioUFNPPerhbBNzpgJoi', // 数美后台可以查看公司标识
//     width: 300,
//     // 连接新加坡机房特殊配置项，仅供验证码数据上报新加坡机房客户使用
//     // domains: ["captcha-xjp.fengkongcloud.cn"],
//     onInit: function (captchaUuid) {
//         // 第一时间获取到验证码内部的uuid，供业务方进行打点
//         console.log('收到', captchaUuid);
//     },
//     // 连接美国机房特殊配置项，仅供验证码数据上报美国机房客户使用
//     // domains: ["captcha-fjny.fengkongcloud.cn"],
//     product: 'popup', // 展现形式，具体选项见下表
//     mode: 'slide', // 验证码模式，具体选项见下表
//     // appendTo: 'getCode', // 验证码dom元素的id，
// }, smCaptchaCallback);
const getCode = (data?) => {

  // console.log(initSMCaptcha)
  // // 测试代码
  // SMCaptchaVal?.verify();
  // return;



  ma.value = handleTrim(ma.value);
  if (!checkPhone()) return;
  const params = {
    mobile: { mobile: ma.value.mobile, region: ma.value.region },
    typ: 'LOGIN',
  };
  if (data) {
      params.captcha = {
        code: data?.rid,
        mode: 'slide',
      };
    }
  getIdentifyCode(params)
    .then((res: any) => {
      console.log(res, 'getIdentifyCode');
      countdown.value = 60;
      timer.value = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          clearInterval(timer.value);
          timer.value = null;
        }
      }, 1000);
    })
    .catch((err) => {
      const reason = err.response.data.reason;
      MessagePlugin.error({
        content: err_reason[reason] || '获取验证码失败',
        duration: 3000,
      });
    });
};

const checkPhone = () => {
  checkRegion = checkPhoneAndMatch(Number(ma.value.region), ma.value.mobile);
  if (!checkRegion) {
    MessagePlugin.error({
      content: t('zx.account.phoneIllegal'),
      duration: 3000,
    });
    return false;
  }
  if (checkRegion !== Number(ma.value.region)) {
    (checkPhoneTip.value = `${t('zx.account.checkPhoneRegion1')}“+${checkRegion}”，${t(
      'zx.account.checkPhoneRegion2',
    )}`),
      (checkPhoneVisible.value = true);
    return false;
  }
  return true;
};

const login = () => {
  dealSmDeviceId(async (deviceId) => {
    console.log('回调执行成功，设备标识为：' + deviceId);
    setSMDeviceId(deviceId);
    ma.value = handleTrim(ma.value);
    const data = _.cloneDeepWith(ma.value);
    let params = {};
    let paramsV2: any = {
      info: {
        encode: true,
        app: 'RINGKOL',
        region: 'CN',
        platform: 'H5'
      }
    };
    switch (loginType.value) {
      case 1:
        if (!checkPhone()) return;
        data.password = '';
        params = { ma: data };
        break;
      case 2:
        // // 验证输入的是否是邮箱
        // const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
        // const isEmail = reg.test(data.mobile);
        // data.password = encrypt(data.password)
        // if (isEmail) {
        //   ea.value.mail = data.mobile;
        //   ea.value.password = data.password;
        //   params = { ea: ea.value };
        // } else {
        //   data.code = '';
        //   data.region = '';
        //   params = { ma: data };
        // }
        params = {
          aa: {
            account: encrypt(data.mobile),
            password: encrypt(data.password),
          },
          encode: true
        }
        paramsV2 = {
          ...paramsV2,
          account: {
            account: encrypt(data.mobile),
            password: encrypt(data.password),
          }
        }
        break;
      case 3:
        params = { qrc: { code: qrcCode.value } };
        break;
    }
    if (loginType.value !== 3) {
      loginLoading.value = true;
      loginDisabled.value = true;
    }
    const extraParams = {
      app: 'RINGKOL',
      zone: 'CN',
      platform: 'H5',
    };
    const fn = loginType.value === 2 ? () => loginAccountV2(paramsV2) : () => loginAccount({...params, ...extraParams})
    fn()
      .then((res: any) => {
        console.log(res, 'loginAccount');
        // 是否扫描二维码登录
        if (res.code === 0 || Object.keys(res).length) {
          const data = loginType.value === 2 ? res.data : res;
          loginType.value === 3 ? checkQrCode(data) : loginSuc(data);
        }
      })
      .catch((err) => {
        const reason = err.response.data.reason;
        MessagePlugin.error({
          content: err_reason[reason] || err.response.data?.message || '登录失败',
          duration: 3000,
        });
        resetBtnStatus();
      });
  }).catch((e) => {
    console.error(e);
    MessagePlugin.error('网络繁忙，请稍后刷新重试');
  });
};
// 扫描二维码过程
const checkQrCode = (data: any) => {
  if (data?.step === 'SCAN') {
    scanSuc.value = true;
  }
  if (data?.step === 'VERIFY') {
    scanSuc.value = true;
    loginSuc(data);
  }
};

const loginSuc = (jwtParams: any) => {
  setAccesstoken(jwtParams.jwt);
  setOpenid(jwtParams.openid);
  getUserInfo();
};

const saveJwt = (jwtParams: any) => {
  getJwt(jwtParams)
    .then((res: any) => {
      console.log(res, 'getJwt');
      setAccesstoken(res.jwt);
      setOpenid(res.openid);
      getUserInfo();
    })
    .catch(() => {
      resetBtnStatus();
    });
};

const getUserInfo = () => {
  getProfile()
    .then((res: any) => {
      accountStore.setUserInfo(res);
      console.log('hah', res);
      localStorage.setItem('profile', JSON.stringify(res));
      localStorage.setItem('userInfo', JSON.stringify(res));
      localStorage.removeItem('unifunc');
      if (redirect) {
        if (sourceParams) {
          router.push({ path: decodeURIComponent(redirect), query: { link: sourceParams } });
        } else {
          router.push(decodeURIComponent(redirect));
        }
        return;
      }
      router.push({ name: 'accountJoin' });
    })
    .finally(() => {
      resetBtnStatus();
    });
};

const resetBtnStatus = () => {
  loginLoading.value = false;
  loginDisabled.value = false;
};

const goRegister = () => {
  // const redirectUrl = redirect ? decodeURIComponent(redirect) : ;
  // router.push(redirectUrl);

  router.push({ name: 'accountRegister', query: { redirect } });
};

const checkLoginDisabled = () => {
  if (loginType.value === 1 && ma.value.code && ma.value.mobile) {
    loginDisabled.value = false;
  } else if (loginType.value === 2 && ma.value.password && ma.value.mobile) {
    loginDisabled.value = false;
  } else {
    loginDisabled.value = true;
  }
};

watch(
  ma,
  () => {
    checkLoginDisabled();
  },
  {
    deep: true,
  },
);
</script>

<style lang="less" scoped>
@import url('../css/base.less');

:global(.t-dropdown) {
  border: none;
}
:global(.t-dropdown .t-dropdown__menu) {
  padding: 8px;
}
:global(.t-dropdown .t-dropdown__item) {
  height: 42px;
  padding: 12px;
}

:deep(.tel-code-style) {
  & > div {
    width: 100%;
    border-bottom: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
  }
  .kyy-cell {
    width: 93px !important;
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 8px;
    margin-bottom: 0;
    .van-field__control {
      height: 66px;
      font-size: 15px;
      color: var(--input-kyy_color_input_text_completed, #1a2139);
    }
  }
  .van-bottom {
    width: calc(100% - 93px) !important;
    padding-top: 0;
    padding-bottom: 0;
    margin-bottom: 0;
    .van-field__control {
      height: 66px;
      font-size: 18px;
      color: var(--input-kyy_color_input_text_completed, #1a2139);
    }
  }
  .line {
    height: 24px;
  }
}

.container {
  min-height: 600px;
  height: 100vh;
  position: relative;
  // max-width: 600px;
  // min-height: 100vh;
  // margin: auto;
  // // position: absolute;
  // // top: 0;
  // // bottom: 0;
  // // left: 50%;
  // // transform: translateX(-50%);
  // background-color: #fff;
  overflow: hidden;

  :deep(.t-input) {
    box-shadow: none;
  }
}

.mt12 {
  margin-top: 12px;
}

.f-c {
  display: flex;
  justify-content: center;
  align-items: center;
}

.pointer {
  cursor: pointer;
}

.logo {
  margin-top: 24px;
  text-align: center;

  img {
    width: 80px;
    height: 80px;
    vertical-align: bottom;
  }
}

.logo-name {
  text-align: center;
  font-size: 18px;
  font-family: MicrosoftYaHei, MicrosoftYaHei-Bold;
  font-weight: 700;
  color: var(--text-kyy-color-text-1, #1a2139);
  line-height: 26px;
  margin-top: 12px;
}

.login-tab {
  display: flex;
  justify-content: start;
  font-size: 14px;
  font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
  font-weight: normal;
  color: var(--text-kyy-color-text-1, #1a2139);
  line-height: 22px;
  margin: 25px auto;
  width: 320px;

  .tab-item {
    cursor: pointer;
    margin-bottom: 9px !important;
  }

  .active {
    color: var(--brand-kyy-color-brand-default, #4d5eff);

    /* kyy_fontSize_3/bold */
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
    overflow: visible;
    position: relative;

    &:before {
      height: 2px;
      width: 16px;
      content: '';
      border-radius: 1.5px;
      background: var(--brand-kyy-color-brand-default, #4d5eff);
      position: absolute;
      left: 50%;
      bottom: -9px;
      transform: translateX(-50%);
    }
  }
}

.login-tab-content {
  margin: auto;
  width: 320px;
  margin-top: 24px;

  .content-item {
    height: 132px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;

    .adornment {
      display: flex;
      align-items: center;
      font-size: 18px;
      height: 66px;
      .area {
        // min-width: 78px;
        width: 80px !important;
        margin-right: -1px;
        display: flex;
        font-size: 18px;

        :deep(.t-input) {
          border-bottom-right-radius: 0;
          border-top-right-radius: 0;
          font-size: 18px !important;
          height: 66px;
        }
      }

      .phone {
        width: calc(100% - 80px) !important;

        :deep(.t-input) {
          border-bottom-left-radius: 0;
          border-top-left-radius: 0;
          font-size: 18px !important;
          height: 66px;
        }
        :deep(.t-input__inner) {
          text-indent: 12px !important;
        }
      }
    }

    :deep(.t-input) {
      font-size: 18px !important;
      height: 66px;
    }

    :deep(.t-input-adornment__prepend) {
      background: #fff;
    }

    :deep(.codeBox .t-input__clear) {
      position: absolute;
      right: 120px;
    }
  }

  .login-btn {
    margin-top: 32px;
    height: 40px;
    font-size: 16px;
    font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
    color: #ffffff;
    line-height: 24px;
    border-radius: 4px;
  }

  .login-act-ext {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
    color: var(--text-kyy-color-text-1, #1a2139);
    line-height: 24px;
  }
}

.content-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 44px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 14px;
  font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
  color: var(--text-kyy-color-text-1, #1a2139);
  line-height: 22px;

  .split-line {
    width: 1px;
    height: 18px;
    background: #e3e6eb;
    margin: 0 16px;
  }
}

.suffix--line {
  width: 1px;
  height: 24px;
  background-color: #f6f6f6;
  margin-right: 16px;
}

.adornment--line {
  width: 2px;
  height: 24px;
  background-color: #f6f6f6;
}

.verify {
  font-size: 14px;
  font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
  color: #4d5eff;
  line-height: 22px;
}

.logo-large {
  margin-top: 174px;
  text-align: center;

  img {
    vertical-align: bottom;
  }
}

.login-progress {
  width: 215px;
  margin: 48px auto 0;
}

.zzdl {
  font-size: 16px;
  font-family: MicrosoftYaHei, MicrosoftYaHei-Bold;
  font-weight: 700;
  color: var(--text-kyy-color-text-1, #1a2139);
  text-align: center;
  margin-top: 20px;
  line-height: 24px;
}

.qrcode-container {
  width: 320px;
  height: 230px;
  background: #f1f2f5;
  overflow: hidden;
  position: relative;

  .qrcode {
    width: 144px;
    height: 144px;
    font-size: 0;
    margin: 24px auto 16px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .scan-logo {
    width: 20px;
    height: 20px;
  }

  .scan-tip {
    font-size: 14px;
    font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
    color: var(--text-kyy-color-text-1, #1a2139);
    line-height: 22px;
    position: absolute;
    bottom: 25px;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
  }
}

.scan-suc-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 40px;

  img {
    width: 40px;
    height: 40px;
  }

  .scan-suc {
    font-size: 16px;
    font-family: MicrosoftYaHei, MicrosoftYaHei-Bold;
    font-weight: 700;
    color: var(--text-kyy-color-text-1, #1a2139);
    line-height: 24px;
    margin: 12px 0;
  }

  .tip {
    font-size: 12px;
    font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
    font-weight: normal;
    color: #717376;
    line-height: 20px;
  }
}

.lang {
  display: flex;
  height: 88px;
  padding: 8px 16px;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  align-self: stretch;

  .t-button {
    color: var(--text-kyy-color-text-3, #828da5);
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }
}

.forget-bottom {
  width: 320px;
  margin: 12px auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
  color: var(--text-kyy-color-text-1, #1a2139);
  line-height: 22px;
}

:deep(.t-input) {
  border: none;
  border-radius: 0;
  border-bottom: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
}
</style>
