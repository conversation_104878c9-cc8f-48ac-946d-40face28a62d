<template>
	<div style="height: 100%">
		<div class="linkTable">
			<div class="headBox">
				<div class="head1200box">
					<!-- <div v-if="isToken" class="passwordBoxHeadBox">
						<img src="../../assets/logo.svg" alt="" />
						<div class="passwordBoxtitleBox">
							<div class="passwordBoxtitleText">{{ resData.disk_type === 0 ? '个人盘' : resData.team }}</div>
							<div class="passwordBoxtitleSlogan">
								{{ resData.sharer }} 通过云盘分享的{{ resData.is_folder === 1 ? '文件夹' : '文件' }}
							</div>
						</div>
					</div> -->
					<div class="passwordBoxHeadBox">
						<img src="../../assets/logo_44.png" alt="" />

						<div class="passwordBoxtitleBox">
							<div class="passwordBoxtitleText">另可云盘</div>
						</div>
					</div>
					<div class="btnHead">
						<t-button v-if="!store.userInfo.title" variant="outline" class="mr12" @click="goLogin(100)">登录</t-button>
						<t-button v-if="!store.userInfo.title" variant="outline" class="mr12" @click="goLogin(200)">注册</t-button>
						<t-button @click="dowKyyApp"> 客户端下载</t-button>
						<div style="position: relative">
							<div v-if="store.userInfo.title" data-id="isclick" class="hy" @click="optionFlag = !optionFlag">
								欢迎,{{ store.userInfo.title }}
							</div>
							<div v-if="optionFlag" data-id="isclick" class="name-option-box">
								<div data-id="isclick" class="head-box">
									<kyyAvatar
										style="margin: 0 4px"
										:image-url="store.userInfo.avatar"
										data-id="isclick"
										avatar-size="40px"
										:user-name="store.userInfo.title"
									/>

									<span data-id="isclick">{{ store.userInfo.title }}</span>
								</div>
								<div class="out-login" @click="outLogin">退出登录</div>
							</div>
						</div>
					</div>
				</div>
			</div>


			<div class="bodyBox" >
				<div class="fistBox">
					<div class="fileNameBox">
						<img :src="fileImage(resData.type)" alt="" />
						<div>
							<div class="fileNames">{{ resData.title }}</div>
							<div class="fileTimes">到期时间：{{ timestampToTime(resData.expired_at) }}</div>
						</div>
					</div>
					<div class="fistBtnBox">
						<t-button v-if="KBTOMB(resData.size) !== '--'" variant="outline" theme="primary" @click="dowFiles"
							>下载({{ KBTOMB(resData.size) }})</t-button
						>

						<t-button
							v-if="KBTOMB(resData.size) === '--'"
							:disabled="selectedRowKeys.length === 0"
							variant="outline"
							theme="primary"
							@click="dowFiles"
							>下载{{
								allOptionsBreadcrumb() === 0 || allOptionsBreadcrumb() === '--' ? '' : `(${allOptionsBreadcrumb()} ) `
							}}</t-button
						>
						<!-- -->
						<t-button theme="primary" class="ml12" @click="saveFile">保存到云盘</t-button>

						<!-- <t-popup>
							<t-button variant="outline" theme="primary">
								<template #icon>
									<icon class="menu-icons" name="two-dimensional-code-one" :url="iconUrl" />
								</template>
								保存到手机
							</t-button>
							<template #content>
								<div class="qcode">
									<img src="../../assets/img/twoCode.jpg" alt="" />
									<div class="qcodeText">使用另可APP扫一扫，放入手机云盘</div>
								</div>
							</template>
						</t-popup> -->
					</div>
				</div>
				<div  v-if="codem === 418" style="margin: 0 auto;text-align: center;height: 100%;background: #fff;">
					<img class="noteFileImg" style="width: 212px; height: 212px; margin-top: 160px" src="@/assets/sq.png" />
					<div
						style="
							color: #516082;
							text-align: center;
							font-size: 17px;
							font-style: normal;
							font-weight: 400;
							line-height: 26px; /* 152.941% */
						"
					>
						内容违规平台规范，请勿重复发布此类信息
					</div>
				</div>
				<div v-if="codem !== 418&&resData.is_folder === 1" style="flex: 1" class="flex1overflow contentBox">
					<div class="mianbaoxie">
						<div class="flex-align">
							<span v-if="optionsBreadcrumb.length === 0" class="all-text">全部</span>
							<span
								v-if="optionsBreadcrumb.length !== 0"
								class="btn back"
								@click="
									changBreadcrumbItem(optionsBreadcrumb[optionsBreadcrumb.length - 2], optionsBreadcrumb.length - 2)
								"
								>返回上一级</span
							>
							<div v-if="optionsBreadcrumb.length < 4" class="breadcrumb-box">
								<div
									v-for="(item, index) in optionsBreadcrumb"
									:key="item.id"
									class="click-btn color-black"
									style="display: flex; align-items: center"
								>
									<img
										v-if="index !== 0"
										style="padding: 0 8px"
										src="../../assets/img/<EMAIL>"
										alt=""
									/>
									<t-tooltip :content="item.title">
										<div class="title-ovfler" @click="changBreadcrumbItem(item, index)">
											{{ item.title }}
										</div>
									</t-tooltip>
								</div>
							</div>
							<div v-if="optionsBreadcrumb.length >= 4" style="display: flex; align-items: center; padding-left: 12px">
								<div class="flex-align click-btn">
									<span class="title-ovfler" @click="changBreadcrumbItem(optionsBreadcrumb[0], -1)"> 全部 </span>
									<img style="padding: 0 8px" src="../../assets/img/<EMAIL>" alt="" />
								</div>
								<div class="flex-align">
									<span> ... </span>
									<img style="padding: 0 8px" src="../../assets/img/<EMAIL>" alt="" />
								</div>
								<div class="flex-align click-btn">
									<t-tooltip :content="optionsBreadcrumb[optionsBreadcrumb.length - 2].title">
										<span
											class="title-ovfler"
											@click="
												changBreadcrumbItem(
													optionsBreadcrumb[optionsBreadcrumb.length - 2],
													optionsBreadcrumb.length - 2,
												)
											"
										>
											{{ optionsBreadcrumb[optionsBreadcrumb.length - 2].title }}
										</span>
									</t-tooltip>

									<img
										v-if="optionsBreadcrumb.length - 2 > -1"
										style="padding: 0 8px"
										src="../../assets/img/<EMAIL>"
										alt=""
									/>
									<t-tooltip :content="optionsBreadcrumb[optionsBreadcrumb.length - 1].title">
										<span
											class="title-ovfler"
											style="color: #13161b"
											@click="
												changBreadcrumbItem(
													optionsBreadcrumb[optionsBreadcrumb.length - 1],
													optionsBreadcrumb.length - 1,
												)
											"
										>
											{{ optionsBreadcrumb[optionsBreadcrumb.length - 1].title }}
										</span>
									</t-tooltip>
								</div>
							</div>
						</div>
					</div>
					<div>
						<div class="sort-texts">
							<t-checkbox
								v-model="checkboxAllData"
								:indeterminate="selectedRowKeys.length > 0 && selectedRowKeys.length != data.length"
								@change="changeCheckboxAllData"
							/>
							<span v-if="selectedRowKeys.length > 0"> 已选 {{ selectedRowKeys.length }} 项 </span>
							<span v-else> 共 {{ data.length }} 项 </span>
						</div>
					</div>
					<div class="tableBox">
						<t-table
							row-key="id"
							height="100%"
							class="clouddiskhome-talbe"
							:columns="columns"
							:data="data"
							:loading="isLoading"
							:selected-row-keys="selectedRowKeys"
							:select-on-row-click="false"
							@select-change="columnChange"
							@row-click="rowClick"
						>
							<template #title="{ row }">
								<div class="flex-align">
									<img v-if="row.illegal!==1" style="width: 32px; height: 32px" :src="fileImage(row.type)" />
									<img v-else style="width: 32px; height: 32px" :src="row.url" />

									<!-- <div v-if="row.tag" class="important-box">重要</div> -->
									<div class="tableFileName">
										<t-tooltip :content="row.title"> {{ row.title }} </t-tooltip>
									</div>
								</div>
							</template>
							<template #size="{ row }">
								<div>
									{{ KBTOMB(row.size) }}
								</div>
							</template>
							<template #empty>
								<div>
									<img
										style="width: 200px; height: 200px; display: block; margin: 49px auto 8px"
										src="@/assets/img/notdata.png"
									/>
									<div style="font-size: 14px; color: #13161b; text-align: center">暂无数据</div>
								</div>
							</template>

							<template #updatedAt="{ row }">
								<div style="display: flex; align-items: center">
									<div>{{ getTimes(new Date(row.updatedAt * 1000)) }}</div>
								</div>
							</template>
						</t-table>
					</div>
				</div>
				<!-- -->
				<div v-if="codem !== 418&&fileterFile() === '其他' && resData.is_folder !== 1" class="flex1overflow">
					<div class="noDataBox">
						<img src="../../assets/bzc.png" />
					</div>
				</div>
				<div v-if="codem !== 418&&fileterFile() === '文件'" ref="iframeWrapper" class="flex1overflow">
					<!-- <iframe
						ref="iframeRef"
						class="iframeImg"
						:src="filterUrl(viewFileData.file_url)"
						width="1200px"
						:height="iframeHeight"
						style="position: fixed; text-align: center"
						@onload="setIframeHeight(this)"
					/> -->
					<div
						id="viewer"
						ref="iframeRef"
						class="iframeImg"
						></div>
						<!-- style="position: absolute; left: 0; right: 0; top: 0px; bottom: 0; user-select: all" -->
				</div>
				<div v-if="codem !== 418&&fileterFile() === '图片'" class="flex1overflow">
					<img style="margin: 0px auto; display: block" :src="viewFileData.file_url" alt="" />
				</div>
				<div  style="padding: 32px; overflow: auto" v-if="codem !== 418&&fileterFile() === '文本'">
					{{ textData }}
				</div>
				<div class="advertisementBox">
					<div class="advertisementBoxTowText">
						<div class="advertisementBoxTitle">企业云盘惊喜上线！</div>
						<div class="advertisementBoxText">企业文件云保存，文件误删好找回，团队文件互分享，登录就送云存储</div>
					</div>
					<t-button
						variant="outline"
						style="
							height: 40px;
							width: 112px;
							color: var(--brand-kyy-color-brand-default, #4d5eff) !important;
							font-weight: 600;
						"
						@click="dowKyyApp"
						>立即体验</t-button
					>
				</div>
			</div>
			<div class="footText">Copyright©2021-2023 All Rights Reserved 新海通版权所有</div>
		</div>

		<!-- 选中了文件夹 -->
		<t-dialog v-model:visible="visible1" :close-btn="false" :header="true" :footer="true" width="384">
			<template #header>
				<div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
					<div style="font-weight: 700">提示</div>
					<img
						style="width: 16px; cursor: pointer; height: 16px"
						src="@/assets/<EMAIL>"
						@click="visible1 = false"
					/>
				</div>
			</template>
			<div class="right-box">
				<div class="flie-max" style="width: 100%">
					<!-- <div class="value-text">文件夹名称</div> -->
					<div class="flex-align fileMaxBox">
						<!-- <img src="../../assets/svg/icon_staffFiles.svg" /> -->
						<img style="width: 32px; height: 32px" :src="fileImage(maxFileObj.type)" />
						<div class="maxFileName">
							{{ maxFileObj.title }}
						</div>
					</div>
					<div class="maxFileTip">
						你下载的{{ maxFileObj.isFolder === 0 ? '文件' : '文件夹' }}过大，请使用另可客户端
					</div>
				</div>
			</div>
			<template #footer>
				<div style="display: flex; align-items: center; width: 100%; justify-content: center">
					<t-button @click="dowkyy"> 安装最新版客户端 </t-button>
				</div>
			</template>
		</t-dialog>
		<!-- 登录 -->
		<t-dialog
			v-model:visible="denglu"
			:cancel-btn="false"
			:close-btn="false"
			:header="true"
      class="login-dialog"
			:footer="false"
			width="375"
		>
			<div style="width: 375px;height: 800px;">
				<loginup v-if="store.loginFlag === 100" @closewin="closewin"></loginup>
				<registerup v-if="store.loginFlag === 200" @closewin="closewin"></registerup>
			</div>
		</t-dialog>

		<!-- 移动文件夹 -->
		<move-file
			ref="newMoveFile"
			:left-data="fistFileData"
			:act-row="viewFileData"
			:tokens="tokens"
			:title-flag="moveFileTitleFlag"
			:act-group="actGroup"
			@get-file-list-main-add-file-falg="getFileListMainAddFileFalg"
			@get-file-list="getFileList"
			@clear-selected-row-keys="clearSelectedRowKeys"
			@set-move-act-item="setMoveActItem"
			@get-move-left-list="getMoveLeftList"
		></move-file>
		<t-dialog
			v-model:visible="logOutvisible1"
			theme="info"
			header="退出登录"
			body="退出登录后,你将无法接收到通知"
			:close-btn="false"
			class="login-out-btn"
			confirm-btn="退出登录"
			cancel-btn="取消"
			@confirm="onClickConfirm"
		>
		</t-dialog>
	</div>
</template>

<script setup lang="ts" name="link">
import process from 'process';
import { ref, onMounted, watchEffect, onUnmounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useRoute, useRouter } from 'vue-router';
// import { saveAs } from 'file-saver';
import kyyAvatar from '@/components/kyy-avatar/index.vue';
import MoveFile from '@/components/MoveFile.vue';
import { DiskFileList, fileShareFile, childPidToken } from '@/api/yunpanlink';
// import loginup from '../loginup.vue';
import loginup from '@/pages/login/login.vue';
import registerup from '@/pages/login/register.vue';
import { fileImage } from '@/utils/myUtils';
import { saveAs } from '@/utils/fileUtils';
import axios from 'axios';
import jschardet from 'jschardet';
import { Buffer } from 'buffer';
import WebOfficeSDK from "./web-office-sdk-solution-v2.0.4.es.js";

const router = useRouter();

const route = useRoute();
const iframeWrapper = ref(null);
const iframeRef = ref(null);
const maxFileObj = ref({});
const iframeHeight = ref(0);
const isToken = ref(null);
// 计算当前父级元素的高度，当窗口大小改变时重新计算
const onClickConfirm = () => {
	window.localStorage.removeItem('TokenData');
	window.localStorage.removeItem('refresh_token');
	window.localStorage.removeItem('main_token');
	window.localStorage.removeItem('openid');
	window.localStorage.removeItem('user');
	isToken.value = null;
	logOutvisible1.value = false;
	// store.userInfo = {};
	store.userInfo = { title: null, ID: null, avatar: null, telephone: null };
};
// 监听窗口高度变化
watchEffect(() => {
	// 获取父级元素的高度
	if (iframeRef.value) {
		const parentHeight = window.getComputedStyle(iframeRef.value.parentElement).height;
		// 将 iframe 的高度设置为父元素的高度
		iframeRef.value.style.height = parentHeight;
	}
});
const moveFileTitleFlag = ref('选择移动目录');
const actGroup = ref({
	id: 2,
});
const optionFlag = ref(false);
const optionsBreadcrumb = ref([]);
const documentOwnerArr = ref([]);
const fistFileData = ref([]);
const data = ref([]);
const visible1 = ref(false);
const logOutvisible1 = ref(false);
const rowData = ref([]);
const selectedRowKeys = ref([]);
const isLoading = ref(false);
const checkboxAllData = ref(false);
const newMoveFile = ref(null);
// const pros = ref(process.env.VITE_MANAGE_ENV);
const pros = import.meta.env.VITE_MANAGE_ENV;
const dowKyyApp = () => {
	// https://pre.ringkol.com/downloadCenter/
	// const pros = process.env.VITE_MANAGE_ENV;
	console.log(pros, 'prosprospros');
	// const isMac = /macintosh|mac os x/i.test(navigator.userAgent);
	let url = 'https://dev.ringkol.com/downloadCenter/';
	if (pros === 'PRE') {
		url = 'https://pre.ringkol.com/downloadCenter/';
	}
	if (pros === 'PROD') {
		url = 'https://ringkol.com/downloadCenter/';
	}
	window.open(url, '_blank');
};
const outLogin = () => {
	logOutvisible1.value = true;
};

const saveFile = () => {
	if (codem.value===418) {
		MessagePlugin.error('文件已违规，无法下载和保存到云盘。')
		return
	}
	const main_token = window.localStorage.getItem('main_token');
	store.loginFlag = 100;

	if (main_token) {
		newMoveFile.value.openWin();
	} else {
		denglu.value = true;
	}
};

const clearSelectedRowKeys = () => {
	selectedRowKeys.value = [];
	value2.value = [];
};
const cloudDiskType = {
	name: 'sda',
	id: 2,
};
const closewin = () => {
	const main_token = window.localStorage.getItem('main_token');

	if (main_token && main_token !== 'undefined') {
		isToken.value = true;
	} else {
		isToken.value = false;
	}

	denglu.value = false;
};
const getFileListMainAddFileFalg = () => {
	DiskFileList(actGroup.value.id).then((res) => {
		fistFileData.value = res.data.data.list;
	});
};
const getMoveLeftList = (id) => {
	DiskFileList(id).then((res) => {
		console.log(res, 'aaaaaaaaaaaaaa1111111');
		fistFileData.value = res.data.data.list;
	});
};
const setMoveActItem = (item) => {
	actGroup.value = item;
};
import { getUserStore } from '@/store';

const store = getUserStore();

const denglu = ref(false);
const goLogin = (val) => {
	store.loginFlag = val;

	denglu.value = true;
	// zhuangtaiflag.value = val;
};
const columns = ref([
	{
		colKey: 'row-select',
		type: 'multiple',
		width: 50,
	},
	{ colKey: 'title', title: '名称', ellipsis: true },
	{ colKey: 'size', title: '大小', width: 124 },
	{ colKey: 'updatedAt', title: '更新时间', width: 212 },
]);
onMounted(() => {
	if (route.query.resData) {
		isToken.value =
			window.localStorage.getItem('main_token') && window.localStorage.getItem('main_token') !== 'undefined';
		resData.value = JSON.parse(route.query.resData as any);
	} else {
		router.push({
			path: '/404',
		});
		return;
	}
	document.addEventListener('click', (e) => {
		if (e.target.dataset.id !== 'isclick') {
			optionFlag.value = false;
		}
	});
	// getFileListMainAddFileFalg();
	window.addEventListener('resize', handleResize);
	// if (tokens.value) {
	// getMoveLeftList(45);
	// }
	getFile();
});
onUnmounted(() => {
	window.removeEventListener('resize', handleResize);
});
// 监听窗口高度变化
watchEffect(() => {
	handleResize();
});
function handleResize() {
	if (iframeRef.value) {
		// 获取父级元素的高度
		const parentHeight = window.getComputedStyle(iframeWrapper.value).height;
		// 将 iframe 的高度设置为父级元素的高度
		iframeRef.value.style.height = parentHeight;
	}
}
const viewFileData = ref({});
const resData = ref({});
const tokens = ref(null);
const transformData = (data: any) => {
	return new Promise(async (resolve) => {
		let reader = new FileReader();
		console.log(await data.arrayBuffer(), 'await data.arrayBuffer()await data.arrayBuffer()');
		const length = await data.arrayBuffer().length;
		let buffers = Buffer.from(await data.arrayBuffer());
		let types = null;
		try {
			jschardet.detect(buffers).encoding === 'UTF-8';
		} catch (error) {
			types = 'GBK';
		}
		reader.readAsText(data, types);
		reader.onload = (e) => {
			resolve(reader.result);
		};
	});
};
const textData = ref('');
const filterUrl = (val) => {
	let urls = val;
	console.log(urls, 'urlsurlsurls');
	if (!urls) {
		return null;
	}
	const extension = urls.substr(urls.lastIndexOf('.') + 1);
	console.log(extension, 'extensionextensionextension');

	if (extension === 'doc' || extension === 'docx') {
		return `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(urls)}`;
	}
	if (extension === 'txt' || extension === 'text') {
		axios
			.get(urls, {
				responseType: 'blob',
				transformResponse: [
					async function (data) {
						return await transformData(data);
					},
				],
			})
			.then((res) => {
				res.data.then((data: any) => {
					textData.value = data;
				});
			});

		return;
	}
	if (extension === 'pdf') {
		return urls;
	}
	return `https://view.officeapps.live.com/op/view.aspx?src=${urls}`;
};
const getFileOfficeType = (fileType) => {
    console.log(fileType,'fileType================');

    const ext = fileType?.toLowerCase();
    if (["doc","dot","wps","wpt","docx","dotx","docm","dotm","rtf","txt","xml","mhtml","mht","html","htm","uof"].includes(ext)) {
        return 'w'

    } else if (["xls","xlt","et","xlsx","xltx","csv","xlsm","xltm","ett"].includes(ext)) {
        return 's'

    } else if(["ppt","pptx","pptm","ppsx","ppsm","pps","potx","potm","dpt","dps","pot"].includes(ext)) {
        return 'p'

    } else if (["pdf","ofd"].includes(ext)){
        return 'f'

    } else if(["otl"].includes(ext)) {
        return 'o'
    }
    return ''
}
const instance=ref(null)
const getViewer = () => {
	console.log(import.meta.env.VITE_MANAGE_ENV,'import.meta.env.VITE_MANAGE_ENVimport.meta.env.VITE_MANAGE_ENV');
  let urls = viewFileData.value.file_url
  const extension = urls.substr(urls.lastIndexOf(".") + 1);
  instance.value = WebOfficeSDK.init({
    officeType: getFileOfficeType(extension),
    appId: import.meta.env.VITE_MANAGE_ENV==='DEV'? 'SX20231102EWXHLC':'AK20231106IYVYFH',
    fileId: viewFileData.value.office_id,
	// fileId:'0172syj36lxq8',
    mode: "simple",
    mount: document.getElementById("viewer"),
  });
};
const codem=ref(null)
const getFile = () => {
	fileShareFile({
		key: route.query.key,
		code: route.query.code,
	}).then((res) => {
		if (res.status === 418||res.data.data.file_illegal===1) {
			codem.value = 418;
			return;
		}
		if (res.status !== 200) {
			MessagePlugin.error(res.data.message);
		} else {
			console.log(res, 'resresresresresres');

			viewFileData.value = res.data.data;

			tokens.value = res.data.data.token;
			if (resData.value.is_folder === 1) {
				// getViewData();
				data.value = [];
				// 傻逼
				data.value.push({
					title: res.data.data.file_title,
					size: res.data.data.file_size,
					id: res.data.data.file_id,
					type: res.data.data.file_type,
					updatedAt: res.data.data.file_updated_at,
				});
			}else{
				getViewer()
			}
		}
	});
};
const getViewData = () => {
	childPidToken(viewFileData.value.file_id, viewFileData.value.token).then((ele) => {
		if (ele.status !== 200) {
			MessagePlugin.error(ele.data.data.message);
		} else {
			selectedRowKeys.value = [];

			// 11111111
			console.log(ele, 'eleeeeeeeeeeee');

			data.value = ele.data.data.list;
		}
		checkboxAllData.value = false;
	});
};
const dowkyy = () => {
	console.log('下载');
	const isMac = /macintosh|mac os x/i.test(navigator.userAgent);
	let url = 'https://img.kuaiyouyi.com/app/android/kyy_im_prod.exe';
	if (isMac) {
		url = 'https://img.kuaiyouyi.com/app/ios/kyy_im_prod.dmg';
	}
	window.open(url, '_blank');
};
const columnChange = (val) => {
	selectedRowKeys.value = val;
	value2.value = val;

	if (val.length === data.value.length) {
		checkboxAllData.value = true;
	} else {
		checkboxAllData.value = false;
	}
};
// 			src = new URL('../../assets/svg/qita.svg', import.meta.url).href;

// 			break;
// 	}

// 	return src;
// };
const value2 = ref([]);
const sortIndex = ref(null);
// 下载文件夹
const dowFiles = () => {
	if (codem.value===418) {
		MessagePlugin.error('文件已违规，无法下载和保存到云盘。')
		return
	}
	if (resData.value.is_folder === 1) {
		const arr = selectedRowKeys.value.map((e) => data.value.find((ele) => e === ele.id));
		for (let i = 0; i < arr.length; i++) {
			if (arr[i].type === 'folder' || arr[i].size > 10000) {
				maxFileObj.value = arr[i];
				visible1.value = true;
				break;
			} else {
				saveAs(arr[i].url, arr[i].title);
			}
		}
	} else {
		saveAs(viewFileData.value.file_url, viewFileData.value.file_title);
	}
};

const getFileList = async () => {
	value2.value = [];
	if (optionsBreadcrumb.value.length === 0) {
		// let DiskListRes = null;
		// if (!cloudDiskType.name) {
		// 	const res = await DiskList(); // 右上角菜单选项后端给接口
		// 	DiskListRes = res.data.data.list[0].id;
		// } else {
		// 	DiskListRes = cloudDiskType.id;
		// }
		// 首层
		getViewData().then((res) => {
			data.value = res.data.data.list;
			value2.value = [];
		});
	} else {
		rowClick(
			{
				row: {
					type: 'folder',
					permission: optionsBreadcrumb.value[optionsBreadcrumb.value.length - 1].permission,
					title: optionsBreadcrumb.value[optionsBreadcrumb.value.length - 1].title,
					id: optionsBreadcrumb.value[optionsBreadcrumb.value.length - 1].id,
				},
			},
			true,
		);
	}
};
const rowClick = (row, flag) => {
	rowData.value = row.row;
	if (row.row.type === 'folder') {
		value2.value = [];

		selectedRowKeys.value = [];
		isLoading.value = true;
		if (!flag) {
			optionsBreadcrumb.value.push(row.row);
		}

		childPidToken(row.row.id, tokens.value).then((res) => {
			if (res.status === 200) {
				if (sortIndex.value === 1) {
					data.value = JSON.parse(JSON.stringify((res.data.data.list, 'size', true)));
				} else if (sortIndex.value === 2) {
					data.value = JSON.parse(JSON.stringify((res.data.data.list, 'size', false)));
				} else if (sortIndex.value === 3) {
					data.value = JSON.parse(JSON.stringify((res.data.data.list, 'createdAt', false)));
				} else if (sortIndex.value === 4) {
					data.value = JSON.parse(JSON.stringify((res.data.data.list, 'createdAt', true)));
				} else {
					data.value = res.data.data.list;
				}
			} else {
				MessagePlugin.error(res.data.message);
			}

			isLoading.value = false;
		});
		checkboxAllData.value = false;
	}
};
const getTimes = (date) => {
	const Y = date.getFullYear();
	const M = date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1;
	const D = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();
	const h = date.getHours() < 10 ? `0${date.getHours()}` : date.getHours();
	const m = date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes();
	const s = date.getSeconds() < 10 ? `0${date.getSeconds()}` : date.getSeconds();
	return `${Y}-${M}-${D} ${h}:${m}:${s}`;
};
const allOptionsBreadcrumb = () => {
	// let arr=[]
	const arr = selectedRowKeys.value.map((e) => data.value.find((ele) => e === ele.id).size);
	if (arr.length > 0) {
		const sum = arr.reduce((accumulator, currentValue) => accumulator + currentValue);
		console.log(sum, 'summmmmmmmmmmmm');
		return KBTOMB(sum);
	}
	return 0;
};
const KBTOMB = (bytes) => {
	if (bytes === 0) return '--';
	const k = 1000; // or 1024
	const sizes = ['KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	if (!sizes[i]) {
		return `${(bytes / k ** i).toPrecision(3)}B`;
	}
	return `${(bytes / k ** i).toPrecision(3)} ${sizes[i]}`;
};
const timestampToTime = (timestamp) => {
	// 时间戳为10位需*1000，时间戳为13位不需乘1000
	const date = new Date(timestamp * 1000);
	const Y = `${date.getFullYear()}-`;
	const M = `${date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1}-`;
	const D = `${date.getDate() < 10 ? `0${date.getDate()}` : date.getDate()} `;
	const h = `${date.getHours()}:`;
	const m = `${date.getMinutes()}:`;
	const s = date.getSeconds();
	return Y + M + D + h + m + s;
};
const changeCheckboxAllData = (e) => {
	checkboxAllData.value = e;
	if (e) {
		const arr = [];
		data.value.forEach((item) => {
			arr.push(item.id);
		});
		value2.value = arr;

		selectedRowKeys.value = arr;
	} else {
		value2.value = [];
		selectedRowKeys.value = [];
	}
};
const changBreadcrumbItem = (item, index) => {
	if (index < 0) {
		optionsBreadcrumb.value = [];
		documentOwnerArr.value = [];

		// getFileList();
		getFile();
	} else if (index !== optionsBreadcrumb.value.length - 1) {
		optionsBreadcrumb.value.splice(index + 1);
		rowClick(
			{
				row: item,
			},
			true,
		);
	}
};
const filterImgSrc = (src, type?) => {
	try {
		const url = new URL(src);
		if ((type && type.toLowerCase() === 'heic') || url.pathname.endsWith('.heic') || url.pathname.endsWith('.HEIC')) {
			url.searchParams.set('x-oss-process', 'image/format,webp');
			return url.href;
		} else {
			return src;
		}
	} catch (error) {
		return '';
	}
};
const fileterFile = () => {
	console.log(resData, 'resDataresDataresData');

	const texts = ['txt', 'text'];
	const validTypes = ['xlsx', 'xls', 'pptx', 'docx', 'doc', 'pdf', 'ppt', 'excel'];
	const arr = ['image', 'HEIC', 'heic', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'tiff', 'webp', 'PNG', 'JPG'];
	if (arr.includes(resData.value.type)) {
		viewFileData.value.file_url = filterImgSrc(viewFileData.value.file_url, resData.value.type);
		return '图片';
	}
	if (validTypes.includes(resData.value.type)) {
		return '文件';
	}
	if (texts.includes(resData.value.type)) {
		if (viewFileData.value.file_url) {
			console.log(resData.value, 'resDataviewFileDataviewFileDataviewFileData');
			console.log(viewFileData, 'viewFileDataviewFileDataviewFileData');

			filterUrl(viewFileData.value.file_url);
		}
		return '文本';
	}
	return '其他';
};
</script>

<style lang="less" scoped>
.noDataBox {
	text-align: center;
	margin-top: 132px;
	img {
		width: 220px;
		margin-bottom: 10px;
	}
	div {
		height: 22px;
		font-size: 14px;
		font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		font-weight: 400;
		text-align: center;
		color: #13161b;
		line-height: 22px;
	}
}
.qcode {
	width: 200px;
	height: 200px;
	border-radius: 8px;
	margin: -4px -8px;
	box-shadow: 0px 8px 24px 0px rgba(19, 22, 27, 0.16);
	padding: 16px 40px 8px;
	img {
		width: 100%;
	}
	.qcodeText {
		width: 120px;
		height: 44px;
		font-size: 14px;
		font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		font-weight: 400;
		text-align: center;
		color: #717376;
		line-height: 22px;
	}
}
.footText {
	height: 22px;
	font-size: 14px;
	font-family: Microsoft YaHei, Microsoft YaHei-Regular;
	font-weight: 400;
	text-align: left;
	color: var(--text-kyy-color-text-2, #516082);

	line-height: 22px;
	position: fixed;
	bottom: 32px;
	left: 50%;
	transform: translateX(-50%);
}
.back {
	width: 100px;
	font-size: 14px;
	font-family: Microsoft YaHei, Microsoft YaHei-Regular;
	font-weight: 400;
	text-align: left;
	color: #2069e3;
	position: relative;
	padding-right: 12px;
}
.back::after {
	content: '';
	width: 1px;
	height: 12px;
	position: absolute;
	background: #e3e6eb;
	top: 5px;
	right: 0;
}
.login-dialog{
  :deep(.t-dialog){
  padding:0 !important;

  }
  :deep(.t-dialog__body){
  padding:0 !important;
  margin:0 !important;
  }
}
.all-text {
	font-size: 14px;
	font-family: Microsoft YaHei, Microsoft YaHei-Bold;
	font-weight: 700;
	text-align: left;
	color: #13161b;
	line-height: 22px;
}
.breadcrumb-box {
	display: flex;
	align-items: center;
	margin-left: 12px;
}
.title-ovfler {
	max-width: 120px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.tableFileName {
	margin-left: 8px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.hy {
	margin-left: 16px;
	cursor: pointer;
}
.btnHead {
	display: flex;
	align-items: center;
}
.name-option-box-lang {
	background: #ffffff;
	border-radius: 6px;
	z-index: 9999;
	position: absolute;

	box-shadow: 0px 8px 24px 0px rgba(19, 22, 27, 0.16);

	// display: none;
	.option-box {
		padding: 8px;
		.option-item:hover {
			background: #f0f8ff;
		}

		.option-item {
			cursor: pointer;
			width: 200px;
			height: 32px;
			padding: 8px 5px;
			background: #ffffff;
			border-radius: 4px;
			display: flex;
			margin-bottom: 4px;
			align-items: center;
			justify-content: space-between;
			span {
				height: 22px;
				font-size: 14px;
				font-family: Microsoft YaHei, Microsoft YaHei-Regular;
				font-weight: 400;
				color: #13161b;
				line-height: 22px;
			}
		}
	}
	.head-box {
		display: flex;
		align-items: flex-start;
		padding: 16px 0 22px 16px;
		border-bottom: 1px solid #e3e6eb;
		img {
			width: 40px;
			height: 40px;
			border-radius: 6px;
			margin-right: 12px;
		}
		span {
			font-size: 14px;
			font-family: Microsoft YaHei, Microsoft YaHei-Bold;
			font-weight: 700;
			color: #13161b;
		}
	}
}
.name-option-box {
	width: 216px;
	height: 115px;
	background: #ffffff;
	border-radius: 6px;
	z-index: 9999;
	position: absolute;
	right: -200px;
	top: 30px;
	box-shadow: 0px 8px 24px 0px rgba(19, 22, 27, 0.16);
	.out-login {
		height: 22px;
		font-size: 14px;
		font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		font-weight: 400;
		color: #da2d19;
		padding: 0 16px;
		line-height: 33px;
		cursor: pointer;
	}
	.option-box {
		padding: 8px;
		border-bottom: 1px solid #e3e6eb;

		.option-item:hover {
			background: #f0f8ff;
		}
		.option-item {
			cursor: pointer;
			width: 200px;
			height: 32px;
			padding: 8px 5px;
			background: #ffffff;
			border-radius: 4px;
			display: flex;
			margin-bottom: 4px;
			align-items: center;
			justify-content: space-between;
			span {
				height: 22px;
				font-size: 14px;
				font-family: Microsoft YaHei, Microsoft YaHei-Regular;
				font-weight: 400;
				color: #13161b;
				line-height: 22px;
			}
		}
	}
	.head-box {
		display: flex;
		align-items: flex-start;
		padding: 16px 0 22px 16px;
		border-bottom: 1px solid #e3e6eb;
		img {
			width: 40px;
			height: 40px;
			border-radius: 6px;
			margin-right: 12px;
		}
		span {
			font-size: 14px;
			font-family: Microsoft YaHei, Microsoft YaHei-Bold;
			font-weight: 700;
			color: #13161b;
		}
	}
}
.table-view-r-item-file-img {
	text-align: center;
	margin-top: -20px;
	img {
		width: 72px;
		height: 72px;
	}
	.textovf {
		width: 132px;
		font-size: 14px;
		font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		font-weight: 400;
		text-align: center;
		margin: 0 auto;
		color: #13161b;
		line-height: 22px;
		// white-space: nowrap; // 强制一行显示
		// overflow: hidden; // 超出隐藏
		// text-overflow: ellipsis; // 省略号
		word-break: break-all;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2; /* 超出几行省略 */
		overflow: hidden;
	}
}
.advertisementBox {
	background-image: url(../../assets/<EMAIL>);
	background-repeat: no-repeat;
	background-position: 100%;
	background-size: 100%;
	width: 1200px;
	height: 114px;
	padding: 0 116px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin: 0 auto;
	// bg_pic_foot.svg
}
.color-black:last-child {
	color: #13161b;
}
.flie-max {
	img {
		width: 32px;
		height: 32px;
		margin-right: 8px;
	}
}
.btn {
	cursor: pointer;
	color: #2069e3;
}
.click-btn {
	cursor: pointer;
}
.breadcrumb-box {
	display: flex;
	align-items: center;
	margin-left: 12px;
}
.contentBox {
	margin-top: 32px;
	.mianbaoxie {
	}
}
.sort-texts {
	height: 56px;
	line-height: 56px;
	font-size: 12px;
	display: flex;
	align-items: center;
	color: #717376;
	background: #fff;
}
.fileMaxBox {
	margin-top: 8px;
	margin-bottom: 16px;
	img {
		width: 32px;
		height: 32px;
	}
	.maxFileName {
		width: 280px;
		font-size: 14px;
		font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		font-weight: 400;
		text-align: left;
		color: #13161b;
		line-height: 22px;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}
}
.flex-align {
	display: flex;
	align-items: center;
}
.iframeImg {
	img {
		text-align: center;
	}
}
.contentBox {
	margin: 32px 116px 0;
}

.advertisementBoxTitle {
	height: 24px;
	font-size: 16px;
	font-family: Microsoft YaHei, Microsoft YaHei-Bold;
	font-weight: 700;
	text-align: left;
	color: #ffffff;
	line-height: 24px;
	margin-bottom: 8px;
}
.advertisementBoxText {
	height: 22px;
	font-size: 14px;
	font-family: Microsoft YaHei, Microsoft YaHei-Regular;
	font-weight: 400;
	text-align: left;
	color: #ffffff;
	line-height: 22px;
}
.flex1overflow {
	flex: 1;
	overflow: auto;
}
.linkTable {
	background-image: url(../../assets/bg_pic.svg);
	background-size: cover;
	background-position: 100%;
	height: 100vh;
	padding-bottom: 86px;
	.headBox {
		width: 100%;
		height: 80px;
		background: #fff;
	}
	.menu-icons {
		width: 16px;
		height: 16px;
		font-size: 16px;
		margin-right: 4px;
	}
	.bodyBox {
		display: flex;
		flex-direction: column;
		width: 1200px;
		margin: 16px auto 0;
		height: calc(100% - 96px);
		background: #fff;
		.fistBtnBox {
			margin-right: 32px;
		}
		.fileNameBox {
			display: flex;
			align-items: center;
			margin-left: 32px;
			img {
				width: 32px;
				height: 32px;
				margin-right: 8px;
			}
			.fileNames {
				height: 22px;
				font-size: 14px;
				font-family: Microsoft YaHei, Microsoft YaHei-Regular;
				font-weight: 400;
				text-align: left;
				color: #13161b;
				line-height: 22px;
			}
			.fileTimes {
				height: 20px;
				font-size: 12px;
				font-family: Microsoft YaHei, Microsoft YaHei-Regular;
				font-weight: 400;
				text-align: left;
				color: #a1a2a4;
				line-height: 20px;
			}
		}
		.fistBox {
			border-bottom: 1px solid #e3e6eb;
			height: 80px;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}
	}
	.head1200box {
		width: 1200px;
		height: 80px;
		display: flex;
		margin: 0 auto;
		align-items: center;
		justify-content: space-between;
		.passwordBoxHeadBox {
			display: flex;
			align-items: center;
			img {
				width: 40px;
				height: 40px;
			}
			.passwordBoxtitleText {
				font-size: 16px;
				font-family: Microsoft YaHei, Microsoft YaHei-Bold;
				font-weight: 700;
				text-align: left;
				color: #13161b;
			}
			.passwordBoxtitleBox {
				margin-left: 20px;
			}
			.passwordBoxtitleSlogan {
				height: 22px;
				font-size: 14px;
				font-family: Microsoft YaHei, Microsoft YaHei-Regular;
				font-weight: 400;
				text-align: left;
				color: #717376;
				line-height: 22px;
			}
		}
	}
}
.mr12 {
	margin-right: 12px;
}
.ml12 {
	margin-left: 12px;
}
</style>
