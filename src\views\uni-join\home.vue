<template>
  <div class="digital-campus-container containerPc">
    <!-- 顶部背景及内容区域 -->
    <div class="header-bg">
      <div class="header-title" :style="{ background: showhs ? '#29C2D5' : 'transparent' }">
        <!-- <iconpark-icon name="iconarrowlift" class="header-icon"  @click="close"></iconpark-icon> -->
        <svg
          v-if="isRinkol"
          @click="close"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            d="M13.9166 17L6.91663 10L13.9166 3"
            stroke="white"
            stroke-width="1.8"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>

        <div class="latbox" v-if="!showhs" @click="selectLat">
          <img
            class="lat-icon"
            src="http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/icon_local.svg"
            alt=""
          />
          <div class="lattext">
            {{ props.markerInfo.name }}
          </div>
          <img
            class="lat-icon"
            src="http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/dr.svg"
            alt=""
          />
        </div>

        <div
          class="search-input search-inputw2"
          @click="tosearch(1)"
          v-if="showhs"
          :style="{ opacity: showhs ? 1 : 0 }"
        >
          <iconpark-icon name="iconsearch" class="search-icon"></iconpark-icon>
          <input v-model="keyword" type="text" placeholder="搜索学校名称" class="search-i" />
        </div>
        <!-- 分享 -->
        <svg
          v-if="isRinkol"
          style="position: absolute; top: 28px; right: 40px"
          @click="shareRun"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            d="M4.90921 8.11424C6.03392 8.11438 6.918 9.00576 6.918 10.0713C6.91776 11.1366 6.03376 12.0282 4.90921 12.0283C3.78453 12.0283 2.90066 11.1367 2.90042 10.0713C2.90042 9.00567 3.78437 8.11424 4.90921 8.11424Z"
            stroke="white"
            stroke-width="1.8"
          />
          <path
            d="M15.0913 2.40023C16.216 2.40038 17.1001 3.29175 17.1001 4.35727C17.0999 5.42259 16.2159 6.31415 15.0913 6.3143C13.9666 6.3143 13.0828 5.42267 13.0825 4.35727C13.0825 3.29166 13.9665 2.40023 15.0913 2.40023Z"
            stroke="white"
            stroke-width="1.8"
          />
          <path
            d="M15.0914 13.8291C16.2161 13.8293 17.1002 14.7206 17.1002 15.7862C17.0999 16.8515 16.216 17.743 15.0914 17.7432C13.9667 17.7432 13.0829 16.8516 13.0826 15.7862C13.0826 14.7206 13.9666 13.8291 15.0914 13.8291Z"
            stroke="white"
            stroke-width="1.8"
          />
          <path d="M12.9074 5.07157L6.50722 8.6428M12.9074 15.071L6.50722 11.4998" stroke="white" stroke-width="1.8" />
        </svg>

        <svg
          v-if="isRinkol"
          @click="close"
          style="position: absolute; top: 28px; right: 24px"
          xmlns="http://www.w3.org/2000/svg"
          width="14"
          height="14"
          viewBox="0 0 14 14"
          fill="none"
        >
          <path d="M1.00476 1.00098L12.998 12.9988" stroke="white" stroke-width="1.8" stroke-linecap="round" />
          <path d="M12.9913 1.00098L0.998066 12.9988" stroke="white" stroke-width="1.8" stroke-linecap="round" />
        </svg>
        <!-- <iconpark-icon name="iconerror" class="header-icon" v-if="isRinkol" @click="close"></iconpark-icon> -->
      </div>
    </div>
    <!-- 学校列表区域 -->
    <div ref="scrollContainer" class="school-list" @scroll="handleScroll">
      <div class="header-content">
        <h1 class="title">用另可<span style="color: #fff9e8; font-weight: 600">数字高校</span></h1>
        <p class="sub-title">让青春焕发光彩</p>
        <p class="desc">高校生活一站式数字平台</p>
        <div
          class="search-input search-inputw1"
          @click="tosearch"
          v-if="displaySchools.length"
          style="margin-bottom: 12px"
        >
          <iconpark-icon name="iconsearch" class="search-icon"></iconpark-icon>
          <input v-model="searchVal" type="text" placeholder="搜索学校名称" class="search-i" />
        </div>
      </div>
      <div v-for="(item, index) in displaySchools" :key="item.id || index" class="school-item">
        <div class="school-info">
          <img :src="item.team_logo || deflogo" alt="school logo" class="school-logo" />
        </div>
        <div class="school-name">
          <div class="school-name">
            {{ item.exclusive_name || item.team_name }}
          </div>
          <div class="school-address" v-if="item.distance_extend">
            <iconpark-icon name="iconpositioning" class="iconpositioning"></iconpark-icon>
            <span style="margin-top: 2px">{{ distanceCalc(item.distance_extend?.distance) }}</span>
          </div>
        </div>
        <button class="join-btn" @click="toapply(item)">申请加入</button>
      </div>

      <Empty
        style="margin-top: 100px"
        v-if="displaySchools.length === 0 && !isLoading"
        name="no-firend-list"
        tip="高校建设中，请耐心等待"
      />
      <div class="loading-tip" v-if="isLoading">
        <t-loading v-if="isLoading" size="14px" text="加载中..."></t-loading>
      </div>
      <div v-if="!hasMore && !isLoading && displaySchools.length > 0" class="loading-tip">已经到底啦~</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Empty from '@/components/Empty.vue';

import { teamListByUni } from '@/api/order';
import { ref, onMounted, onActivated, onDeactivated, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useAccountStore } from '@/stores/account';
const deflogo = 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/defaultOrgLogo.svg';
const emit = defineEmits(['search', 'selectLat']); // 数据相关变量
const displaySchools: any = ref([]); // 展示的学校列表，改为普通变量
const searchVal = ref(''); // 搜索框内容
const currentPage = ref(1); // 当前页码
const totalVal = ref(0); // 每页条数
const isLoading = ref(false); // 是否正在加载
const hasMore = ref(true); // 是否还有更多数据
const keyword = ref(''); // 搜索关键词
const showhs = ref(false);
const scrollContainer = ref(null);
const isRinkol = useAccountStore().isRingkol;
// 保存滚动位置
const scrollPosition = ref(0);

const props = defineProps({
  markerInfo: {
    type: Object,
    default: () => ({}),
  },
});
// watch(() => props.markerInfo, (newVal) => {
//   if (newVal) {
//     console.log('newVal:', newVal);
//     fetchSchools();
//   }
// })

const getDataByMapChange = () => {
  fetchSchools();
};

// 初始化加载第一页数据
onMounted(() => {
  fetchSchools(1);
});

// 组件被激活时（从keep-alive中恢复）
onActivated(() => {
  // 恢复滚动位置
  setTimeout(() => {
    if (scrollContainer.value && scrollPosition.value > 0) {
      scrollContainer.value.scrollTop = scrollPosition.value;
    }
  }, 100);
});
const scrollTopRun = () => {
  if (scrollPosition.value) {
    scrollContainer.value.scrollTop = scrollPosition.value;
  }
};

// 组件被停用时（进入keep-alive缓存）
onDeactivated(() => {
  // 保存滚动位置
  // if (scrollContainer.value) {
  //   scrollPosition.value = scrollContainer.value.scrollTop;
  // }
});

// 从接口获取学校数据
const fetchSchools = async (record?) => {
  if (isLoading.value || !hasMore.value) return;

  isLoading.value = true;
  try {
    const params = {
      is_record: record || 0,
      page: currentPage.value,
      pageSize: 20,
      longitude: props.markerInfo.location.lng,
      latitude: props.markerInfo.location.lat,
    };
    teamListByUni(params).then((res: any) => {
      console.log(res);
      const { team_list, total } = res.data;
      if (currentPage.value === 1) {
        displaySchools.value = team_list;
      } else {
        displaySchools.value = [...displaySchools.value, ...team_list];
      }
      totalVal.value = total;
      if (displaySchools.value.length >= total) {
        hasMore.value = false;
      }
      setTimeout(() => {
        isLoading.value = false;
      }, 500);
    });
  } catch (error) {
    console.error('获取学校数据失败:', error);
    setTimeout(() => {
      isLoading.value = false;
    }, 500);
  } finally {
  }
};
const distanceCalc = (distance) => {
  if (distance) {
    distance = distance > 999 ? `${(distance / 1000).toFixed(1)}km` : `${distance}m`;
  }
  return distance;
};
// 滚动事件处理函数，判断是否触底
const handleScroll = (e) => {
  const container = e.target;
  if (container.scrollTop >= 150) {
    showhs.value = true;
  } else {
    showhs.value = false;
  }
  // 触底判断，留10px误差
  if (
    container.scrollTop + container.clientHeight >= container.scrollHeight - 400 &&
    !isLoading.value &&
    hasMore.value
  ) {
    loadMore();
  }
  scrollPosition.value = container.scrollTop;
};

// 加载下一页数据
const loadMore = () => {
  currentPage.value++;
  fetchSchools();
};

// 搜索功能（可以根据需要实现）
const handleSearch = () => {
  // 重置分页，重新加载第一页数据
  currentPage.value = 1;
  hasMore.value = true;
  fetchSchools();
};
const router = useRouter();
const tosearch = (type) => {
  if (type === 1 && !showhs.value) {
    return;
  }
  //   router.push({
  //     path: '/uni-join/search',
  //   });
  emit('search', keyword.value);
};
const toapply = (item) => {
  router.push({
    path: `/account/jump?to=uniInvite&link=${item.team_link}`,
    query: {
      to: 'uniInvite',
      link: item.team_link,
      orgkey: 'uniJoin',
    },
  });
};
const close = () => {
  window.RingkolAppChannel.postMessage(JSON.stringify({ method: 'appRoutePop' }));
};

const selectLat = () => {
  emit('selectLat');
};
const shareRun = () => {
  window.RingkolAppChannel.postMessage(JSON.stringify({ method: 'uniShare', url: window.location.href }));
};
defineExpose({
  scrollTopRun,
  getDataByMapChange,
});
</script>

<style lang="less" scoped>
.digital-campus-container {
  width: 100%;
  height: 100vh;
  background-color: #eaf8f9;
  background-image: url('http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/uni_bg_img2.png');
  background-size: cover;
  background-position: center;
  overflow: hidden;
  -webkit-overflow-scrolling: touch;
}
.header-bg2 {
  width: 100%;
  height: 64px;
}

/* 顶部背景及内容样式 */
.header-bg {
  width: 100%;
  .header-title {
    display: flex;
    width: 100%;
    height: 64px;
    padding: 12px 16px;
    align-items: center;
    gap: 8px;
    position: relative;
    .header-icon {
      font-size: 24px;
      color: #fff;
    }
  }
}
.header-content {
  .title {
    color: var(--text-kyy_color_text_white, #fff);
    font-feature-settings: 'liga' off, 'clig' off;
    text-shadow: 0 0 12px #1697b5;
    font-family: 'PingFang SC';
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 36px; /* 150% */
  }

  .sub-title {
    color: #fff;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: 'PingFang SC';
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 36px;
  }

  .desc {
    color: #fff;
    font-feature-settings: 'liga' off, 'clig' off;

    /* kyy_fontSize_3/regular */
    font-family: 'PingFang SC';
    font-size: 17px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px; /* 152.941% */
    margin-bottom: 20px;
  }

  .search-box {
    width: 100%;
    max-width: 400px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    padding: 5px 10px;
    display: flex;
    align-items: center;

    .search-input {
      width: 100%;
      border: none;
      outline: none;
      background: transparent;
      font-size: 14px;
      padding: 8px 0;
    }
  }
}
/* 学校列表滚动容器样式 */
.school-list {
  width: 100%;
  height: calc(100vh - 80px);
  overflow-y: auto;
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch;
  padding: 0px 16px 12px 16px;
  /* 滚动条样式 */
  &::-webkit-scrollbar {
    width: 0px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-track {
    background-color: #f5f5f5;
  }
}

/* 学校列表项样式 */
.school-item {
  display: flex;
  width: 100%;
  min-height: 76px;
  padding: 12px;
  align-items: center;
  gap: 12px;
  border-radius: 12px;
  border: 1px solid #fff;
  background: rgba(255, 255, 255, 0.9);
  margin-bottom: 12px;
  .school-info {
    display: flex;
    align-items: center;

    .school-logo {
      width: 52px;
      height: 52px;
      margin-right: 10px;
      border-radius: 50%;
      object-fit: cover;
    }
  }
  .school-name {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    flex: 1 0 0;
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1a2139);
    font-feature-settings: 'liga' off, 'clig' off;
    text-overflow: ellipsis;
    font-family: 'PingFang SC';
    font-size: 17px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px; /* 152.941% */
    .school-address {
      .iconpositioning {
        font-size: 20px;
      }
      color: var(--text-kyy_color_text_3, #828da5);
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      display: flex;
      // align-items: center;
      margin-top: 2px;
    }
  }
  .join-btn {
    display: flex;
    height: 28px;
    padding: 10px 8px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 16px;
    border: 1px solid #0ea9d8;
    color: #0ea9d8;
    font-feature-settings: 'liga' off, 'clig' off;
    background-color: transparent;
    /* kyy_fontSize_2/regular */
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}

/* 加载提示样式 */
.loading-tip {
  text-align: center;
  color: #fff;
  padding: 8px 0;
  font-size: 14px;
}
.search-input {
  display: flex;
  height: 40px;
  padding: 5px 8px;
  align-items: center;
  gap: 4px;
  border-radius: 24px;
  border: 2px solid #347298;
  background: #fff;
  backdrop-filter: blur(10px);
  .search-icon {
    font-size: 24px;
    color: #828da5;
  }
  .search-i {
    // 去除输入框边框 hover样式
    border: none;
    outline: none;
    width: 100%;
    height: 26px;
  }
  .search-i::placeholder {
    color: var(--text-kyy_color_text_5, #acb3c0);
    font-feature-settings: 'liga' off, 'clig' off;

    /* kyy_fontSize_3/regular */
    font-family: 'PingFang SC';
    font-size: 17px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px; /* 152.941% */
  }
}
.search-inputw1 {
  width: 100%;
}
.search-inputw2 {
  flex: 1;
}
:deep(.tip) {
  color: #fff;
}
:deep(.t-loading) {
  color: #fff;
}
:deep(.t-loading__gradient-conic) {
  transform: scale(1) !important;
}
.containerPc {
  margin: 0 auto;
}
.touchn {
  touch-action: none;
}
.latbox {
  display: flex;
  max-width: 176px;
  padding: 2px 8px;
  align-items: center;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(25px);
  .lat-icon {
    width: 20px;
    height: 20px;
  }
  .lattext {
    //   overflow: hidden;
    // color: var(--text-kyy_color_text_2, #516082);
    // font-feature-settings: 'liga' off, 'clig' off;
    // text-overflow: ellipsis;
    // display: -webkit-box;
    // max-width: 120px;
    // -webkit-box-orient: vertical;
    // -webkit-line-clamp: 1;
    // flex: 1 0 0;
    // font-family: "PingFang SC";
    // font-size: 12px;
    // font-style: normal;
    // font-weight: 400;
    // line-height: 20px; /* 166.667% */

    overflow: hidden;
    color: var(--text-kyy_color_text_2, #516082);
    font-feature-settings: 'liga' off, 'clig' off;
    text-overflow: ellipsis;
    /* display: -webkit-box; */
    max-width: 120px;
    min-width: 20px;
    /* -webkit-box-orient: vertical; */
    /* -webkit-line-clamp: 1; */
    /* flex: 1 0 0; */
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    white-space: pre;
  }
}
</style>
