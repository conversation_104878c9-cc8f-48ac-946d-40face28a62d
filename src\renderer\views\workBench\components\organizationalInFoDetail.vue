<!-- 组织介绍详情页 -->
<template>
  <!-- <t-loading v-if="!isRequestData" text="加载中..." class="detail bg-transparent"></t-loading> -->
  <noData v-if="!tabs?.length || dataIsInvalid" :show-type="dataIsInvalid ? 'invalidData' : 'noData'"
    :text="dataIsInvalid ? deletedText : text" />
  <div v-else class="detail" :class="{ isDialogBox: data.isRepublish || dataIsInvalid }">
    <div class="detail-wrap">
    <div :class="{isheader:data.isRepublish || dataIsInvalid}" class="wrshare">
      <div class="detail-wrap-share">
        <t-popup v-if="showHeader && isShowToggleIcon" overlay-class-name="detail-toggle-popup">
          <div class="detail-tab-toggle-icon">
            <iconpark-icon name="iconmore" size="20"></iconpark-icon>
          </div>
          <template #content>
            <div class="detail-tab-list">
              <div v-for="(item, idx) in tabs" :key="idx" class="detail-tab-list-item"
                @click="handleSwitchTab(item, idx)">
                {{ item.title }}
              </div>
            </div>
          </template>
        </t-popup>
        <div
          v-if="(!data.isRepublish && !dataIsInvalid)&&isShowToggleIcon&&route.path!=='/workBenchIndex/introduce-detail'"
          style="width: 1px; background: var(--divider-kyy_color_divider_light, #eceff5); height: 16px; margin: 0 12px">
        </div>
        <div v-if="(!data.isRepublish && !dataIsInvalid)&&showShare&&route.path!=='/workBenchIndex/introduce-detail'"
          class="detail-tab-share">

          <shareBtn :shareData="ImshareData" :msg-share-type="'about_us_organize'" :extraData="extraData"
            :type="PostType.TeamIntro" :teamId="teamInfo?.teamId" :copyUrl="copyUrl" />
        </div>
      </div>
        <div v-if="showHeader" class="detail-tab-wrap"
          :class="{ 'no-toggle': !isShowToggleIcon, isDialog: data.isRepublish || dataIsInvalid }">
          <div class="detail-tab" :style="{ transform: `translate3d(${offsetX}px, 0, 0)` }">
            <div v-for="(item, idx) in tabs" ref="tabRef" :key="idx" class="detail-tab-item"
              :class="{ active: idx === tabActive }" @click="handleSwitchTab(item, idx)">
              {{ item.title }}
            </div>
          </div>
        </div>
        <!--
    <div
    :class="{  left0: data.isRepublish || dataIsInvalid }"

    class="detail-tab-wrap-footer"></div>
    -->
      </div>
      <div ref="boxRef" :class="{ padding0px: data.isRepublish || dataIsInvalid }" class="detail-content--box"
        @scroll="_onScrollBox">
        <div v-for="(item, idx) in tabs" :key="item.id" class="detail-content--box__item">
          <!-- <h2 :id="`tab-${item.id}`" class="introduce-detail--title">{{ item.title }}</h2> -->
          <div :id="`tab-${item.id}`">&nbsp;</div>
          <div class="organize-box-title introduce-detail--title">
            <div class="organize-box-title-left">
              <div class="organize-box-title-line"></div>
              <div class="organize-box-title-diamond"></div>
            </div>
            <div class="organize-box-title-name">{{ item?.title }}</div>
            <div class="organize-box-title-right">
              <div class="organize-box-title-diamond"></div>
              <div class="organize-box-title-line"></div>
            </div>
          </div>
          <EditorPlayground :content='JSON.parse(item.content)'
            class="introduce-detail--content ql-editor details-editor-special">
          </EditorPlayground>
          <!-- <div
            v-if="showHtml"
            id="detail-content"
            class="introduce-detail--content ql-editor details-editor-special"
            v-html="lkQuill?.[idx]?.getHtml()"
          ></div>
          <lk-editor
            ref="lkQuill"
            style="display: none"
            :options="{
              toolbar: ['annex'],
              readOnly: true,
              showTool: false,
            }"
            @ready="onEditoryReady(item, idx)"
          /> -->
        </div>
        <template v-if="data && !dataIsInvalid && !data.isRepublish">
          <div v-if="data.status === 2 || data.status === 1 || data.status === 5 || data.draft_status === 1"
            class="audit-content">
            <p>
              <template v-if="data.status === 2">
                <div style="color: #828da5; font-size: 14px" v-if="data.pv >= 10000">
                  阅读量：
                  <t-tooltip :content="data.pv" :show-arrow="false">
                    <span>{{ readVolume(data.pv) }}</span>
                  </t-tooltip>
                </div>
                <div v-else style="color: #828da5; font-size: 14px">阅读量：{{ readVolume(data.pv) }}</div>
              </template>
            </p>
            <p class="audit-item">{{ t("banch.createUserTitle") }}：{{ data.publisher }}</p>
            <p class="audit-item">{{ t("banch.createTimeTitle") }}：{{ data.created_at }}</p>
            <p v-if="data.reviewer" class="audit-item">{{ t("banch.auditUserTitle") }}：{{ data.reviewer }}</p>
            <p v-if="data.review_at" class="audit-item">{{ t("banch.auditTimeTitle") }}：{{ data.review_at }}</p>
          </div>
          <div v-else-if="data.status === 4" class="audit-content">
            <p class="audit-item">{{ t("banch.createUserTitle") }}：{{ data.publisher }}</p>
            <p class="audit-item">{{ t("banch.createTimeTitle") }}：{{ data.created_at }}</p>
            <p class="audit-item">{{ t("banch.auditUserTitle") }}：{{ data.reviewer }}</p>
            <p class="audit-item">{{ t("banch.auditTimeTitle") }}：{{ data.review_at }}</p>
            <p class="audit-item">{{ t("banch.refuseReasonTitle") }}：{{ data.review_reason || data.reason }}</p>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch, toRefs, computed, onMounted, onActivated, nextTick } from "vue";
  import { useI18n } from "vue-i18n";
  import { useRoute } from "vue-router";
  import { MessagePlugin } from "tdesign-vue-next";
  import { useThrottleFn } from "@vueuse/core";
  import { MsgShareType } from "@renderer/utils/share";
  import EditorPlayground from '@/components/editor/playground.vue';

  import { getSharedSquaresAxios } from "@renderer/politics/api/businessApi";
  import to from "await-to-js";
  import { getTeams } from "@renderer/api/contacts/api/organize";
  import { onMountedOrActivated } from "../../../hooks/onMountedOrActivated";
  import { addOSSWaterMarker } from "@/views/square/utils/waterMaker";
  import noData from "./noData.vue";
  import { getOssImgNormal } from "@/views/square/utils/OSSHelpler";
  import shareBtn from "@renderer/components/shareBtn/index.vue";
  import { PostType } from "@/views/square/constant";
  import { readVolume, shareLinkSplicing } from "@/utils/aboutUs";
  import zuzi from "@/assets/zuzhijies.svg";
  import { getTeamShareDataApi } from "../common/getShareData";
  import LynkerSDK from '@renderer/_jssdk';

  const { ipcRenderer, shell } = LynkerSDK;
  const route = useRoute();

  const props = withDefaults(
    defineProps < {
      data: any;
      shareData: any;
      notGetShare: Boolean;
      isRequestData: Boolean;
      showHeader?: Boolean;
      showShare?: boolean;
    } > (),
    {
      showShare: true,
    },
  );
  const { t } = useI18n();
  const { data, showHeader, shareData, notGetShare } = toRefs(props);
  const offsetX = ref(0);
  const copyUrl = ref("");
  const translating = ref(false);
  const translateTimer = ref();
  const extraData = ref(null);
  const teamId = route.query.team_id as string;

  const tabRef = ref();
  const tabActive = ref(0);
  const lkQuill = ref();
  const isShowToggleIcon = ref(false);
  const text = ref(t("banch.notIntroduce"));
  const deletedText = ref(t("banch.deletedData"));
  const dataIsInvalid = computed(
    () => (!route.query?.isDraft && data.value?.status === 5) || data.value?.is_delete === 1,
  );

  const boxRef = ref();
  /**
   * fix https://www.tapd.cn/69781318/bugtrace/bugs/view/1169781318001046524
   * 这块业务是有问题的，但怕影响别的业务，只能临时这样写
   */
  const showHtml = ref(false);

  // 图片预览
  const imagePreview = (url: string | string[], index: number) => {
    const mapper = (v: string) => ({
      title: v,
      url: getOssImgNormal(v),
      downloadUrl: addOSSWaterMarker(v, `广场号ID：${props.pid}`),
      type: "image",
      imgIndex: index,
    });
    const data = Array.isArray(url) ? url.map(mapper) : mapper(url);

    ipcRenderer.invoke("preview-file", JSON.stringify(data));
  };

  const getExtraData = async () => {
    await getTeamData();
    console.log(shareData.value, "prosasdasdasd");
    if (notGetShare.value || !props.showShare || route.path === "/workBenchIndex/introduce-detail") {
      return;
    }
    const { platform, team_id } = route.query;
    try {
      if (shareData.value?.share_token) {
        const [res, err] = await getTeamShareDataApi(pageType.value, shareData.value.share_token, teamInfo.value);
        console.log('请求分享333');

        if (!res) {
          err?.msg && MessagePlugin.warning(err?.msg || "内容已撤回");
          return;
        }
        // 组织介绍使用默认图
        res.extraData.fields.img = "";
        extraData.value = res?.extraData;
        ImshareData.value = res?.shareData;
        copyUrl.value = res?.copyUrl;
      }
    } catch (e) { }
  };

  /*
  const _teamInfo = localStorage.getItem("honorteam");
  if (_teamInfo) {
    teamInfo.value = JSON.parse(_teamInfo);
  }
  */
  onMountedOrActivated(() => {
    dataIsInvalid.value = false;
    offsetX.value = 0;
    if (data.value) {
      getExtraData();
    }
    console.log(data.value, "datadata");
    console.log(shareData.value, "shareDatashareData");
    console.log("route.query.isDraft", route.query.isDraft);

    console.log(extraData.value, "extraData.valueextraData.value");
  });
  watch(
    () => data.value,
    async (val, val1) => {
      await nextTick();
      setTimeout(() => {
        if (data.value) {
          console.log('这里请求数据1111');

          getExtraData();
          // 撤回提示
          if (!route.query?.isDraft && data.value.status === 5) {
            text.value = "内容已撤回";
            MessagePlugin.warning("内容已撤回");
            dataIsInvalid.value = true;
            return;
          }
          // 删除提示
          if (data.value.is_delete === 1) {
            text.value = t("banch.contentIsDeleted");
            MessagePlugin.warning(t("banch.contentIsDeleted"));
            dataIsInvalid.value = true;
            return;
          }
          let totalWidth = 0;
          const tabItems = document.querySelectorAll(".detail-tab-item");
          const tabWrap = document.querySelector(".detail-tab");
          for (const item of tabItems) {
            totalWidth += item.offsetWidth + 32;
          }
          if (tabWrap?.offsetWidth) {
            isShowToggleIcon.value = totalWidth > tabWrap.offsetWidth;
          }
          console.log("totalWidthtotalWidthtotalWidth", isShowToggleIcon.value);
        }

        const imgs = document.querySelectorAll("#detail-content img");
        const urls = Array.from(imgs).map((item) => item.src);
        console.log("🚀 ~ nextTick ~ urls:", urls);
        imgs.forEach((img, idx) => {
          img.onclick = () => {
            imagePreview(urls, idx);
          };
        });
      }, 500);
    },
    { immediate: true },
  );
  import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";

  const teamData = ref();
  const getTeamData = async () => {
    if (!route.path.includes("/square") || !teamId) return;
    const [err, res] = await to(getTeams({ teamId }));
    if (err) return;
    teamData.value = res.data.data;
  };

  const pageType = computed(() => {
    if (route.path.includes("/digitalPlatformIndex")) return 3;
    if (route.path.includes("/workBenchIndex")) return 2;
    if (route.path.includes("/square")) 1;
    return 1;
  });

  const teamInfo = computed(() => {
    if (route.path.includes("/digitalPlatformIndex")) {
      const digitalPlatformStore = useDigitalPlatformStore();
      return digitalPlatformStore.activeAccount;
    }
    if (route.path.includes("/workBenchIndex")) return JSON.parse(localStorage.getItem("honorteam"));
    if (route.path.includes("/square")) {
      const team = teamData.value || {};
      return {
        teamFullName: team.fullname,
        teamLogo: team.logo,
        teamId: team.teamId,
      };
    }
    return {};
  });

  const ImshareData = ref({});
  const lkQuillContent = ref(null);

  const setContent = (item, idx) => {
    if (!item) {
      return;
    }
    let content = "";
    if (data.value?.module_list) {
      content = item.content;
    } else {
      content = item.content;
    }
    try {
      const _data = JSON.parse(content || "[]");
      lkQuill.value[idx]?.renderContent({ ops: _data });
    } catch (err) {
      lkQuill.value[idx]?.setHtml(content);
    }
    console.log(content, 'contentcontentcontent');
    console.log(idx, 'idxidxidxidxidx');
    console.log(lkQuill.value, 'idxidxidxidxidx');
    showHtml.value = false;
    setTimeout(() => {
      showHtml.value = true;
    }, 100)
  };
  const onEditoryReady = (item, idx) => {
    if (!lkQuill.value[idx]) {
      lkQuill.value[idx] = {};
    }
    nextTick(() => {
      setContent(item, idx);
    });
  };

  const tabs = computed(() => {
    if (dataIsInvalid.value) {
      return [];
    }
    if (data.value?.module_list) {
      return data.value.module_list.map(({ id, title, content }) => ({ id, title, content }));
    }
    return data.value?.map(({ id, title, content }) => ({ id, title, content }));
  });

  const _onScrollBox = () => {
    if (translating.value) {
      translateTimer.value && clearTimeout(translateTimer.value);
      translateTimer.value = setTimeout(() => {
        translating.value = false;
      }, 500);
      return;
    }
    const scrollTop = boxRef.value.scrollTop;
    const titlesDom = document.querySelectorAll(".introduce-detail--title");
    titlesDom.forEach((item, i) => {
      if (scrollTop > item.offsetTop - item.offsetHeight - 40) {
        tabActive.value = i;
        handleTranslate(i);
      }
    });
  };
  const onScrollBox = useThrottleFn(_onScrollBox, 100);

  /**
   * 切换标签
   */
  const handleSwitchTab = (item, idx) => {
    console.log(item, idx, "index--------------------");
    tabActive.value = idx;
    translating.value = true;
    if (isShowToggleIcon.value) {
      handleTranslate(idx);
    }

    const target = document.querySelector(`#tab-${item.id}`);
    if (!data.value.isRepublish && !dataIsInvalid.value) {
      boxRef.value.scrollTo({
        top: target.offsetTop - 72,
        behavior: "smooth",
      });
    } else {
      target.scrollIntoView({ behavior: "smooth" });
    }
    tabActive.value = idx;
  };
  /**
   * 移动tab位置
   */
  const handleTranslate = (idx) => {
    const tab = document.querySelector(".detail-tab");
    const item = tabRef.value[idx];
    const { offsetLeft: itemLeft, offsetWidth: itemWidth } = item;
    const { offsetWidth: tabWidth } = tab;
    if (itemLeft + itemWidth > tabWidth) {
      offsetX.value = tabWidth - itemLeft - itemWidth;
    } else if (itemLeft < tabWidth) {
      offsetX.value = 0;
    } else if (itemLeft > tabWidth) {
      offsetX.value = tabWidth - item.left + itemWidth;
    }
  };
</script>

<style lang="less" scoped>
  .detail {
    width: 100%;
    display: flex;
    flex-direction: column;

    .ql-editor {
      padding: 0;
    }

    .audit-content {
      margin-top: 24px;
      padding-top: 16px;
      word-break: break-all;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        width: calc(100% - 16px);
        height: 1px;
        background: #eceff5;
      }

      .audit-item {
        color: var(--text-kyy_color_text_3, #828da5);
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        margin-top: 4px;
      }
    }

    &-content--box {
      flex: 1;
      overflow-y: auto;
      padding-right: 12px;

      &::-webkit-scrollbar-thumb {
        background: #d5dbe4;
      }

      &::-webkit-scrollbar-track {
        background: #fff;
      }

      &__item {
        margin: 24px 0;
        margin-top: -22px;
        // &:first-child {
        //   margin-top: 0;
        // }
      }
    }

    &-wrap {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      position: relative;
    }

    .introduce-detail--title {
      color: var(--text-kyy_color_text_1, #1a2139);
      font-family: "PingFang SC";
      margin: 24px 0 24px 0;
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: 26px;

      /* 144.444% */
      &:first-child {
        margin-top: 0;
      }
    }

    .introduce-detail--content {
      flex: 1;
      overflow-x: hidden;
      overflow-y: auto;
      line-height: 1.5;

      :deep(img, p) {
        max-width: 100%;
        display: block;
        margin-bottom: 12px;
      }

      :deep(p) {
        word-break: break-all;
      }

      :deep(.ql-align-center) {
        img {
          display: inline;
        }
      }
    }

    &-tab {
      display: flex;
      align-items: center;
      gap: 36px;
      position: relative;
      margin-bottom: 24px;
      transition: transform 0.5s;

      &-wrap {
        width: calc(100% - 56px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;

        &.no-toggle {
          width: 100% !important;
        }
      }

      &-toggle {
        position: absolute;
        width: 32px;
        height: 32px;
        right: 4px;
        top: -5px;
        z-index: 9;

        &:hover {
          cursor: pointer;
          border-radius: 8px;
          background-color: rgb(225, 234, 255);
        }
      }

      &-item {
        color: var(--text-kyy_color_text_1, #1a2139);
        text-align: center;
        height: 32px;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        /* 150% */
        white-space: nowrap;
        position: relative;
        cursor: pointer;

        &::after {
          content: "";
          position: absolute;
          left: 50%;
          bottom: 0;
          transform: translateX(-50%);
          width: 0;
          height: 3px;
          border-radius: 1.5px;
          background: var(--brand-kyy_color_brand_default, #4d5eff);
          transition: width 0.5s;
        }

        &.active {
          color: #4d5eff;
          font-weight: bold;

          // text-shadow: 0 0 1px #4d5eff;
          &::after {
            width: 16px;
          }
        }
      }
    }
  }

  .padding0px {
    padding: 0 !important;
  }

  .detail-content--box {
    padding: 0 18px;
    padding-right: 34px;
    height: 0;
  }

  .detail-wrap-share {
    position: absolute;
    right: 10px;
    display: flex;
    align-items: center;
    z-index: 12;
  }

  .detail-tab-share {
    display: flex;
    align-items: center;
    width: 28px;
    height: 24px;
    gap: 4px;
    cursor: pointer;
    font-size: 14px;
    position: relative;
    z-index: 9;
    padding-right: 4px;
    justify-content: center;
    border-radius: 4px;
    color: #828da5;

    .icon-share {
      font-size: 20px;
    }
  }

  .wrshare {
    padding: 0 18px;
  }

  .detail-tab-wrap {
    // padding-left: 40px;
    // padding: 0 18px;
    width: calc(100% - 80px);
  }

  .detail-tab-wrap-footer {
    position: absolute;
    top: 36px;
    left: 24px;
    width: -webkit-fill-available;
    height: 1px;
    background-color: #eceff5;
    z-index: 1;
  }

  .detail-tab-item {
    &::after {
      bottom: -4px;
      z-index: 999;
    }
  }

  .detail-tab-toggle-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    color: #828da5;
    cursor: pointer;
    width: 28px;
    height: 24px;

    &:hover {
      background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
      color: #707eff;
    }
  }

  .organize-box-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;

    .organize-box-title-left,
    .organize-box-title-right {
      display: flex;
      align-items: center;

      .organize-box-title-line {
        width: 80px;
        height: 4px;
        min-height: 1px;
        max-height: 1px;
        background: var(--kyy_blue-kyy_color_kyyBlue_disabled, #a0dbfd);
      }

      .organize-box-title-diamond {
        width: 6px;
        height: 6px;
        transform: rotate(-45deg);
        background: var(--kyy_blue-kyy_color_kyyBlue_disabled, #a0dbfd);
      }
    }

    .organize-box-title-name {
      overflow: hidden;
      color: var(--text-kyy_color_text_1, #1a2139);
      font-feature-settings: "liga" off, "clig" off;
      text-overflow: ellipsis;
      font-size: 24px;
      font-style: normal;
      font-weight: 600;
      line-height: 32px;
      /* 152.941% */
    }
  }
</style>

<style lang="less">
.detail{
  height: 100%;
}
  .detail-tab-list {
    display: flex;
    width: 168px;
    max-height: 253px;
    padding: 4px;
    flex-direction: column;
    gap: 2px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
      // height: 2px;
      // background-color: #f5f5f5;
    }

    /*定义滚动条轨道 内阴影+圆角*/
    &::-webkit-scrollbar-track {
      // background-color: #e3e6eb;
      // background-color: #fff;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
      background-color: #D5DBE4;
    }

    &-item {
      display: flex;
      height: 32px;
      padding: 7px 16px 7px 7px;
      border-radius: 4px;
      flex-shrink: 0;
      cursor: pointer;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        background-color: rgb(243, 246, 250);
      }
    }
  }

  .detail-wrap-share {
    z-index: 12;
  }

  .isheader {
    position: sticky;
    top: 0;
    padding-left: 0 !important;
    background: #fff;
    z-index: 11;
    /* fix1050944 */

  }

  .isDialogBox {
    .detail-wrap-share {
      position: absolute;
      right: 40px !important;
      display: flex;
      align-items: center !important;
      z-index: 12;
      // top: 75px !important;
    }
  }

  .isDialog {
    position: sticky;
    top: 0;
    padding-left: 0 !important;
    background: #fff;
    z-index: 1;

    .detail-tab {
      margin-bottom: 8px;
    }

    .detail-wrap {
      position: inherit !important;
    }

    .detail-tab-wrap {
      padding-left: 0;
    }

    .detail-tab-wrap-footer {
      left: 0 !important;
    }

    .detail-tab-wrap-footer {
      left: 0 !important;
    }
  }

  .left0 {
    left: 0 !important;
  }

  .detail-toggle-popup {
    .t-popup__content {
      padding: 8px 6px;
      right: 4px;
      top: 8px;
    }
  }

  .details-editor-special {
    font-size: 18px;
  }
</style>
