<template>
  <div
    style="
      width: 100%;
      height: 100vh;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
    "
    @click="onClose"
  >
    <div
      style="
        width: 672px;
        height: 600px;
        overflow: hidden;
        border-radius: 8px;
        padding: 32px;
        background-color: var(--bg-kyy-color-bg-deep, #f5f8fe);
      "
      @click.stop="() => {}"
    >
      <div
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div style="flex: 1; margin-right: 24px">
          <t-input
            ref="inputRef"
            v-model="searchValue"
            clearable
            :placeholder="t('im.public.search')"
            maxlength="40"
            @change="searchData"
          >
            <template #prefix-icon>
              <t-icon name="search" size="16px" />
            </template>
          </t-input>
        </div>
        <div style="padding: 6px" @click="onClose">
          <t-icon name="close" size="22px" style="color: #717376" />
        </div>
      </div>
      <div class="search-body">
        <t-tabs v-model="curTab">
          <t-tab-panel value="all" :label="t('im.public.all1')">
            <div
              v-if="
                !searchValue ||
                  !twoPairs?.length &&
                  !twoGroups?.length &&
                  !twoMessages?.length &&
                  !searchAssistantsResult?.length &&
                  !/^[0-9]*$/.test(searchValue)
              "
              class="search-empty"
            >
            <Empty name="no-result"></Empty>
            </div>
            <div v-else class="tab-content">
              <div v-if="twoPairs?.length" class="search-row-item-box">
                <div class="search-row-item search-row-item-title">{{t("im.public.contact")}}</div>
                <SearchContactRow
                  v-for="item in twoPairs"
                  :keyword="searchValue"
                  :item="item"
                  class="search-row-item hover"
                  @click="onClickPrivateChat(item)"
                />
                <div
                  v-if="searchPairsResult.length > 3"
                  class="search-more"
                  @click="curTab = 'contact'"
                >
                  <t-icon name="search" size="16px" style="color: inherit" />
                  {{t("im.public.moreContact")}}
                </div>
              </div>
              <div v-if="searchAssistantsResult?.length" class="search-row-item-box">
                <div class="search-row-item search-row-item-title">{{t("im.public.assistant")}}</div>
                <SearchGroupRow
                  v-for="item in searchAssistantsResult"
                  :keyword="searchValue"
                  :item="item"
                  class="search-row-item hover"
                  @click="onClickAssistantsChat(item)"
                />
                <!-- <div
                  v-if="searchAssistantsResult.length > 3"
                  class="search-more"
                  @click="curTab = 'assistant'"
                >
                  <t-icon name="search" size="16px" style="color: inherit" />
                  {{t("im.public.moreAssistant")}}
                </div> -->
              </div>
              <div v-if="twoGroups?.length" class="search-row-item-box">
                <div class="search-row-item search-row-item-title">
                  {{t("im.public.myGroup")}}
                </div>
                <SearchGroupRow
                  v-for="item in twoGroups"
                  :item="item"
                  class="search-row-item hover"
                  @click="onClickGroupChat(item.group)"
                />
                <div
                  v-if="searchGroupResult.length > 3"
                  class="search-more"
                  @click="curTab = 'group'"
                >
                  <t-icon name="search" size="16px" style="color: inherit" />
                  {{t("im.public.moreGroup")}}
                </div>
              </div>
              <div v-if="twoMessages?.length" class="search-row-item-box">
                <div class="search-row-item search-row-item-title">
                  {{t("im.public.chat_record")}}
                </div>
                <SearchChatRow
                  v-for="item in twoMessages"
                  :data-id="item.id"
                  class="search-row-item hover"
                  :is-group="item.conversationType === 3"
                  :item="getMsgInfo(item)"
                  @click="onClickMessage(item)"
                />
                <div
                  v-if="searchMsgResult.length > 3"
                  class="search-more"
                  @click="curTab = 'chat'"
                >
                  <t-icon name="search" size="16px" style="color: inherit" />
                  {{t("im.public.moreMsg")}}
                </div>
              </div>
              <div
                v-if="/^[0-9]*$/.test(searchValue)"
                class="search-row-item-box"
              >
                <div
                  class="search-row-item search-row-item-title"
                  style="font-size: 14px; font-weight: 700; color: #13161b"
                >
                {{t("im.public.more")}}
                </div>
                <div
                  class="search-more"
                  style="
                    display: flex;
                    align-items: center;
                    padding: 12px 0;
                    height: 66px;
                  "
                  @click="openContact"
                >
                  <img
                    src="@renderer/assets/im/search-more-contacts.png"
                    alt=""
                    style="
                      width: 40px;
                      height: 40px;
                      margin-right: 8px;
                      border-radius: 50%;
                    "
                  />
                  <div>
                    <div
                      style="font-size: 14px; font-weight: 400; color: #13161b"
                    >
                      {{t("im.public.moreContactTip")}}
                    </div>
                    <div
                      style="text-align:left;font-size: 12px; font-weight: 700; color: #2069e3"
                    >
                      {{ searchValue }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </t-tab-panel>
          <t-tab-panel value="contact" :label="t('im.public.contact')">
            <div v-if="!searchPairsResult.length" class="search-empty">
              <Empty name="no-result"></Empty>
            </div>
            <div
              v-else
              class="tab-content search-row-item-box tab-content-item"
            >
              <div class="search-row-item search-row-item-title">{{t("im.public.contact")}}</div>
              <SearchContactRow
                v-for="item in searchPairsResult"
                :keyword="searchValue"
                :item="item"
                class="search-row-item hover"
                @click="onClickPrivateChat(item)"
              />
            </div>
          </t-tab-panel>
          <t-tab-panel value="group" :label="t('im.public.group')">
            <div v-if="!searchGroupResult.length" class="search-empty">
              <Empty name="no-result"></Empty>
            </div>
            <div
              v-else
              class="tab-content search-row-item-box tab-content-item"
            >
              <div class="search-row-item search-row-item-title">
                  {{t("im.public.myGroup")}}
              </div>
              <SearchGroupRow
                v-for="item in searchGroupResult"
                :item="item"
                class="search-row-item hover"
                @click="onClickGroupChat(item.group)"
              />
            </div>
          </t-tab-panel>
          <t-tab-panel value="chat" :label="t('im.public.chat_record')">
            <div v-if="!searchMsgResult.length" class="search-empty">
              <Empty name="no-result"></Empty>
            </div>
            <div
              v-else
              class="tab-content search-row-item-box tab-content-item"
            >
              <div class="search-row-item search-row-item-title">
                  {{t("im.public.chat_record")}}
              </div>
              <SearchChatRow
                v-for="item in searchMsgResult"
                :data-id="item.id"
                class="search-row-item hover"
                :is-group="item.conversationType === 3"
                :item="getMsgInfo(item)"
                @click="onClickMessage(item)"
              />
            </div>
          </t-tab-panel>
        </t-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import _ from "lodash";
import { ref, toRefs, onMounted } from "vue";
import {
  IRelationPrivate,
} from "@renderer/api/im/model/relation";
import { getImCardIds, getOpenid } from "@renderer/utils/auth";
import { useGlobalSearch } from "./search";
import SearchChatRow from "./SearchChatRow.vue";
import SearchGroupRow from "./SearchGroupRow.vue";
import SearchContactRow from "./SearchContactRow.vue";
import { useI18n } from 'vue-i18n';
import Empty from "@renderer/components/common/Empty.vue";
import { getGroupMemberListApi } from "@/api/im/api";
import LynkerSDK from "@renderer/_jssdk";

const { ipcRenderer } = LynkerSDK;
const { t } = useI18n();

const globalSearch = useGlobalSearch();
const {
  twoMessages,
  twoGroups,
  twoPairs,
  searchPairsResult,
  searchGroupResult,
  searchMsgResult,
  searchAssistantsResult
} = toRefs(globalSearch);

const curTab = ref("all");
const inputRef = ref();
const searchValue = ref("");

const openContact = () => {
  ipcRenderer.invoke("set-popbv", {
    show: true,
    type: "dialogContacts",
    data: { searchValue: searchValue.value },
  });
};

const searchData = _.debounce((text: string) => {
  globalSearch.searchServerData(text);
  globalSearch.searchDbData(text);
  // globalSearch.searchGroup(text);
  if (text.includes("打开控制台")) {
    ipcRenderer.invoke("toggle-devtools", text);
  }
  if (text.includes("打开web开发窗口")) {
    LynkerSDK.openWebDebugWindow();
  }
  if (text.includes("打开另可开发者工具")) {
    LynkerSDK.openDebugTools();
  }
}, 800);

const onClickMessage = (msg: MessageToSave) => {
  ipcRenderer.invoke("im.msg.open", {
    sentTime: msg.sentTime,
    messageUId: msg.messageUId,
    localSessionId: msg.localSessionId,
    conversationType: msg.conversationType,
    targetId: msg.targetId
  });
  onClose();
};

const onClickPrivateChat = (item: IRelationPrivate) => {
  ipcRenderer.invoke("im.chat.open", { main: item.main, peer: item.peer });
  onClose();
};

const onClickAssistantsChat = (item) => {
  ipcRenderer.invoke("im.chat.open", { main: '', peer: item.assistantId });
  onClose();
};

const onClickGroupChat = async(item: GroupToSave) => {
  let members = globalSearch.members.value.filter(
    (member) => member.sessionId === item.group
  );
  const cardIds = getImCardIds();
  let mainCard
  if(!members?.length){
    const res = await getGroupMemberListApi({member:{group:item.group}})
    const array = res?.data?.array;
    members = array?.members?.arr;
    if(!members.length) return;
    console.log('=====>getGroupMemberListApi',members,cardIds);
    mainCard = members.filter((item) =>
    cardIds.includes(item.openid))?.[0]?.openid
  }else{
    console.log('=====>getGroupMemberListApi2',members,cardIds);
    mainCard = members.filter((item) =>
    cardIds.includes(item?.cardId))?.[0]?.["cardId"];
  }
  !mainCard && (mainCard = getOpenid());
  ipcRenderer.invoke("im.chat.open", { main: mainCard, group: item.group });
  onClose();
};

const onClose = () => {
  ipcRenderer.invoke("set-popbv", { show: false });
};

onMounted(() => {
  globalSearch.loadData();
  inputRef.value.focus();
});

const getMsgContent = (msg: MessageToSave) => {
  const { data } = msg.contentExtra;
  switch (msg.contentExtra?.contentType) {
    case "text":
      return data?.text ?? "";
    case "location":
      return `${data?.title} - ${data?.address}`;
    case "file":
      return data?.fileName ?? "";
    case "cloud_disk":
      return data?.title ?? "";
  }
  return "";
};

const getMsgConentPrefix = (msg: MessageToSave) => {
  if (msg.messageType === "RC:ReferenceMsg") {
    return `[${t("im.public.quote")}] `;
  }

  switch (msg.contentExtra?.contentType) {
    case "text":
      return "";
    case "location":
      return `[${t("im.public.location")}] `;
    case "file":
      return `[${t("im.public.file")}]`;
    case "cloud_disk":
      return `[${t("im.public.disk")}]`;
  }
  return "";
};

const getMsgInfo = (msg: MessageToSave) => {

  const { senderId } = msg.contentExtra;

  const content = getMsgContent(msg).replaceAll(
    searchValue.value,
    `<span style="color:#366ef4">${searchValue.value}</span>`
  );
  const info: any = { content };

  if (msg.conversationType === 1) {
    const member = globalSearch.members.value.find(
      (item) =>
        item.sessionId === msg.localSessionId && item.cardId === senderId
    );

    info.conversation =
      member?.comment || member?.nickname || member?.staffName || msg.senderNickname;
    info.sender = info.conversation;
    info.avatar = member?.avatar ?? msg.senderFaceUrl ?? "";
    info.relation = msg.relation;
  } else {
    const group = globalSearch.groups.value.find(
      (item) => item.group === msg.targetId
    );

    const member = globalSearch.members.value.find(
      (item) => msg.targetId === item.sessionId && item.cardId === senderId
    );
    info.conversation = group?.name ?? "";
    info.avatars = group?.attachment?.avatar;
    info.sender = member?.nickname || member?.staffName || msg.senderNickname;
  }

  info.msgPrefix = getMsgConentPrefix(msg);
  return info;
};
</script>

<style lang="less" scoped>
::-webkit-scrollbar {
  width: 5px;
  height: 8px;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}

.tab-content {
  height: 456px;
  overflow-y: overlay;
  font-size: 12px;
  color: #717376;

  .t-tabs__bar {
    display: none;
  }
}

.search-row-item {
  padding: 8px 12px 8px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

//.search-row-item:nth-of-type(n+1) {
//    border-bottom: 1px solid #E5E5E5;
//}

.tab-content-item .search-row-item:not(:nth-child(2)) {
  border-top: 1px solid var(--lingke-gray-1, #eceff5);
}
.tab-content .search-row-item-box .search-row-item:not(:nth-of-type(2)) {
  border-top: 1px solid var(--lingke-gray-1, #eceff5);
}

.hover:hover {
  cursor: pointer;
  background: #f1f2f5;
  border-radius: 8px;
}
.search-more:hover {
  cursor: pointer;
}

.search-empty {
  height: 456px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.search-more {
  display: flex;
  flex-direction: row;
  align-items: center;
  border-top: 1px solid var(--lingke-gray-1, #eceff5);
  color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4d5eff);
  text-align: left;

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 157.143% */
  padding:8px 12px 0;
}

.search-row-item-box {
  margin-bottom: 16px;
  background-color: #ffffff;
  padding: 0 12px 12px;
  border-radius: 8px;
}
.search-row-item-title {
  border: none !important;
  color: var(--text-kyy-color-text-1, #1a2139);
  text-align: center;

  /* kyy_fontSize_3/bold */
  font-family: PingFang SC;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 150% */
  padding: 12px;
}
:deep(.t-tabs) {
  background-color: var(--bg-kyy-color-bg-deep, #f5f8fe);
  .t-tabs__header {
    background-color: var(--bg-kyy-color-bg-deep, #f5f8fe);
  }
  .t-tabs__content {
    margin-top: 16px;
    border-radius: 8px;
  }
  .t-tabs__nav-container.t-is-top::after {
    content: none;
  }
}
</style>
