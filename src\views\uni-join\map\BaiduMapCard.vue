<script setup lang="ts">
import { computed, onBeforeMount, ref, watch } from 'vue';
import '@amap/amap-jsapi-types';
import BaiduMapSelector from '@renderer/components/common/map/BaiduMapSelector.vue';
import { Location } from '@/api/square/models/square';
import { markerIcon } from './utils';

// HACK: md5 is not defined
// https://github.com/yue1123/vue3-baidu-map-gl/issues/27#issuecomment-2219696323
let moduleObject;
onBeforeMount(() => {
  moduleObject = module;
  // eslint-disable-next-line no-global-assign
  (module as unknown) = undefined;
});
const onBMapInitdHandler = () => {
  // eslint-disable-next-line no-global-assign
  (module as unknown) = moduleObject;
};

const props = withDefaults(defineProps<{
  // 指定位置
  location: Location;
  // 是否可修改
  canUpdate?: boolean;
  // 是否显示为小尺寸
  mini?: boolean;
  // 是否显示为大尺寸
  large?: boolean;
  // 地图高度
  height?: string | number;
  // 卡片是否可删除
  removable?: boolean;
}>(), {
  location: () => ({} as Location),
  canUpdate: true,
});
const emit = defineEmits(['confirm', 'remove']);

const loc = ref(props.location);
const mapHeight = computed(() => {
  const { height, mini } = props;
  if (height) return height;
  if (mini) return 96;
  return '100%';
});
const mapVisible = ref(false);

// 地图中心点
const center = ref();
// 地图缩放级别
const zoom = ref(10);

// 标记点坐标
const markerPoint = ref<{ lng: number; lat: number; }>(center.value);

// 地图初始化
const mapInit = () => {
  onBMapInitdHandler();
};

// 重新选择地点
const mapConfirm = (data) => {
  const { lat, lng } = data.location;
  loc.value.latLng = {
    longitude: lng,
    latitude: lat,
  };
  loc.value.name = data.name;
  loc.value.address = data.address || data.name;

  mapClick({ latlng: data.location });
  emit('confirm', {
    ...loc.value,
    ...data,
  });
};

// 点击地图设置新的标记点，并获取其详情地址
const mapClick = ({ latlng }) => {
  markerPoint.value = latlng;
  center.value = latlng;
  zoom.value = 18;
};

// 定位到指定位置
watch(() => props.location, (val) => {
  if (!val || !val.latLng) {
    delete loc.value.latLng;
    return;
  }

  loc.value = val;
  const { longitude, latitude } = val.latLng;
  mapClick({ latlng: { lat: latitude, lng: longitude } });
}, { immediate: true, deep: true });
</script>

<template>
  <div :class="large ? 'block' : 'inline-block'">
    <div v-if="loc.latLng?.latitude" class="map-card" :class="{ mini, large }">
      <div class="header">
        <div class="address-wrap">
          <div class="name">
            <iconpark-icon name="iconorientation" class="icon" />
            {{ loc.name }}
          </div>
          <div class="address">{{ loc.address || '--' }}</div>
        </div>

        <template v-if="canUpdate">
          <t-link theme="primary" hover="color" @click="mapVisible = true">修改</t-link>
          <iconpark-icon
            v-if="removable"
            name="iconclosecircle"
            class="icon remove"
            @click="emit('remove')"
          />
        </template>
      </div>

      <div class="map-container">
        <BMap
          :center="center"
          :zoom="zoom"
          :height="mapHeight"
          enable-scroll-wheel-zoom
          @initd="mapInit"
        >
          <BMarker
            v-if="markerPoint"
            :enable-clicking="false"
            :position="markerPoint"
            :offset="{ x: -14, y: -48 }"
            :icon="markerIcon"
          />
        </BMap>
      </div>
    </div>

    <t-button
      v-else-if="canUpdate"
      variant="outline"
      theme="primary"
      class="plain"
      @click="mapVisible = true"
    >
      <template #icon><iconpark-icon name="iconorientation" /></template>选择地址
    </t-button>

    <BaiduMapSelector
      v-if="mapVisible"
      v-model:visible="mapVisible"
      :loc="location"
      @confirm="mapConfirm"
    />
  </div>
</template>

<style scoped lang="less">
.map-card {
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  align-self: stretch;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);
  background: var(--bg-kyy-color-bg-light, #FFF);
  width: 440px;

  &.mini {
    width: 240px;
    .map-container {
      height: 96px;
    }
  }

  &.large {
    width: 100%;
  }
}

.header {
  display: flex;
  height: 64px;
  padding: 8px 12px;
  align-items: center;
  gap: 16px;
  align-self: stretch;
  .address-wrap {
    flex: 1;
    width: 0;
  }
  .name {
    display: flex;
    color: var(--text-kyy-color-text-2, #516082);
    .ellipsis();
  }
  .address {
    color: var(--text-kyy-color-text-3, #828DA5);
    .ellipsis();
  }
  .icon {
    color: #FC7C14;
    font-size: 20px;
  }
  &:hover .remove {
    opacity: 1;
  }
  .remove {
    position: absolute;
    right: 0;
    top: -10px;
    opacity: 0;
    cursor: pointer;
  }
}

.map-container {
  width: 100%;
  height: 164px;
}
</style>
