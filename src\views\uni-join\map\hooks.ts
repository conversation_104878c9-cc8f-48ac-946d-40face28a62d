// import { onBeforeMount, Ref, ref } from 'vue';
// import { usePointGeocoder, useIpLocation, type PointGeocoderResult, Point } from 'vue3-baidu-map-gl';
// import { covertData, getDefaultLocation } from './utils';

// export { BMap } from 'vue3-baidu-map-gl';
// export { mapOptions, BAIDU_API_URL } from './utils';

// interface Location {
//   point: Point
//   code: number
//   name: string
// }

// interface BaiduMapOptions {
//   // 由坐标点解析地址信息成功后的回调函数
//   onPointGeocoder?: (result: Ref<PointGeocoderResult | PointGeocoderResult[] | null>) => void;
//   // IP定位成功后的回调函数
//   onIPLocation?: (location: Ref<Location>) => void;
// }

// /**
//  * 使用百度地图 API 进行定位
//  * @param options
//  * @returns
//  */
// export const useBaiduMap = (options: BaiduMapOptions = {}, initPoint: Ref<Point> = null) => {
//   // HACK: md5 is not defined
//   // https://github.com/yue1123/vue3-baidu-map-gl/issues/27#issuecomment-2219696323
//   let moduleObject;
//   onBeforeMount(() => {
//     moduleObject = module;
//     // eslint-disable-next-line no-global-assign
//     (module as unknown) = undefined;
//   });
//   const onBMapInitdHandler = () => {
//     // eslint-disable-next-line no-global-assign
//     (module as unknown) = moduleObject;
//   };

//   const { onPointGeocoder, onIPLocation } = options;

//   const city = ref('全国');
//   const markerInfo = ref();

//   // 由坐标点解析地址信息（逆向地理编码）
//   const { get: getPointGeocoder, isEmpty } = usePointGeocoder({}, (res) => {
//     if (isEmpty.value) return;

//     markerInfo.value = covertData(res.value);
//     onPointGeocoder?.(res);
//   });

//   // 浏览器定位（electron 中无法使用）
//   /*
//   const { get: getBrowserLocation, location: bLocation, isLoading, isError } = useBrowserLocation({
//     enableHighAccuracy: true,
//   }, () => {
//     if (isLoading.value) return;
//     if (isError.value) {
//       getIPLocation();
//       return;
//     }

//     const { point } = bLocation.value;

//     onIPLocation?.(location);
//     getPointGeocoder(point);
//   });
//   */

//   // IP 定位（用于获取用户所在的城市位置信息，根据用户 IP 自动定位到城市）
//   const { get: getIPLocation, location } = useIpLocation(() => {
//     if (!location.value) return;

//     // 定位失败时，point 为 undefined
//     if (!location.value?.point) {
//       location.value = getDefaultLocation();
//     }
//     const { point, name } = location.value;
//     city.value = name;

//     onIPLocation?.(location);
//     getPointGeocoder(point);
//   });

//   // 地图初始化
//   const onMapInit = () => {
//     onBMapInitdHandler();

//     if (initPoint?.value) {
//       getPointGeocoder(initPoint.value);
//       return;
//     }

//     // 不指定目标地址时，定位到当前城市中心
//     getIPLocation();
//   };

//   return {
//     city,
//     location,
//     markerInfo,
//     onMapInit,
//     getIPLocation,
//     getPointGeocoder,
//   };
// };
