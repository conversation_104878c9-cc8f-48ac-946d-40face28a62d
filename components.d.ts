/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AreaCode: typeof import('./src/components/keeppx/account/AreaCode.vue')['default']
    AvatarImageUpload: typeof import('./src/components/free-from/runtime/components/AvatarImageUpload.vue')['default']
    CAddress: typeof import('./src/components/free-from/runtime/components/CAddress.vue')['default']
    CImageUpload: typeof import('./src/components/free-from/runtime/components/CImageUpload.vue')['default']
    CRadio: typeof import('./src/components/free-from/runtime/components/CRadio.vue')['default']
    CropperDialog: typeof import('./src/components/free-from/runtime/components/CropperDialog.vue')['default']
    Customer_dialog_component: typeof import('./src/components/free-from/runtime/controls/customer_dialog_component.vue')['default']
    Empty: typeof import('./src/components/Empty.vue')['default']
    FAddress: typeof import('./src/components/free-from/runtime/controls/FAddress.vue')['default']
    FBaseInfoMember: typeof import('./src/components/free-from/runtime/controls/FBaseInfoMember.vue')['default']
    FBaseInfoPolitics: typeof import('./src/components/free-from/runtime/controls/FBaseInfoPolitics.vue')['default']
    FCustomer: typeof import('./src/components/free-from/runtime/controls/FCustomer.vue')['default']
    FDatePicker: typeof import('./src/components/free-from/runtime/controls/FDatePicker.vue')['default']
    FDivider: typeof import('./src/components/free-from/runtime/controls/FDivider.vue')['default']
    FFieldList: typeof import('./src/components/free-from/runtime/controls/FFieldList.vue')['default']
    FFileUpload: typeof import('./src/components/free-from/runtime/controls/FFileUpload.vue')['default']
    FFileUploadDown: typeof import('./src/components/free-from/runtime/controls/FFileUploadDown.vue')['default']
    FieldCode: typeof import('./src/components/keeppx/account/FieldCode.vue')['default']
    FImageUpload: typeof import('./src/components/free-from/runtime/controls/FImageUpload.vue')['default']
    FInput: typeof import('./src/components/free-from/runtime/controls/FInput.vue')['default']
    FMoneyInput: typeof import('./src/components/free-from/runtime/controls/FMoneyInput.vue')['default']
    FMultiSelect: typeof import('./src/components/free-from/runtime/controls/FMultiSelect.vue')['default']
    FNote: typeof import('./src/components/free-from/runtime/controls/FNote.vue')['default']
    FNumberInput: typeof import('./src/components/free-from/runtime/controls/FNumberInput.vue')['default']
    FRadio: typeof import('./src/components/free-from/runtime/controls/FRadio.vue')['default']
    FSelect: typeof import('./src/components/free-from/runtime/controls/FSelect.vue')['default']
    FTextArea: typeof import('./src/components/free-from/runtime/controls/FTextArea.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Runtime: typeof import('./src/components/free-from/runtime/index.vue')['default']
    Share: typeof import('./src/components/Share.vue')['default']
    SvgIcon: typeof import('./src/components/SvgIcon.vue')['default']
    TelCode: typeof import('./src/components/keeppx/account/TelCode.vue')['default']
    Upload: typeof import('./src/components/keeppx/upload.vue')['default']
    UploadAvatar: typeof import('./src/components/common/UploadAvatar.vue')['default']
    VanUploader: typeof import('./src/components/keeppx/account/VanUploader.vue')['default']
    View_all_dialog_component: typeof import('./src/components/free-from/runtime/controls/view_all_dialog_component.vue')['default']
  }
}
