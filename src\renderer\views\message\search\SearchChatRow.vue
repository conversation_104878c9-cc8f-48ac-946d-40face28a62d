<template>
    <div>
        <chat-avatar :size="44" v-if="item.avatar !== undefined" :src="item.avatar" :alt="item.conversation" />
        <chat-avatar :size="44" v-else  :multi-src="item.avatars" :trimLength="1" />
        <div style="flex: 1;">
            <div class="title">
                <div class="name">
                    {{ item.conversation }} 
                </div>
                <RelationTag v-if="item.relation" :relation="item.relation"/>
            </div>
            <div class="info" v-html="`${isGroup ? `${item.sender}: ${item.msgPrefix}` : item.msgPrefix}` +item.content" />
        </div>
    </div>

</template>

<script setup lang="ts">
import ChatAvatar from '../components/ChatAvatar.vue';
import RelationTag from '@renderer/components/contacts/relationTag.vue'

type  MsgInfoItem = {
    conversation: string;
    sender: string;
    avatar?: string;
    avatars?: string;
    content: string;
    relation?: string;
    contentExtra: {
        data: any;
    },
    msgPrefix: string;
}

const props = defineProps<{item: MsgInfoItem, isGroup: boolean}>()
</script>

<style scoped>
.title{
    display: flex;
    align-items: center;
}
.name {
        color: var(--text-kyy_color_text_1, #1A2139);
        font-size: 16px;
        font-weight: 600;
        line-height: 24px; /* 150% */
    }
.info {
    color: var(--text-kyy_color_text_3, #828DA5);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    word-break: break-all;
}
</style>