<template>
    <div class="header select-none flex flex-col!">
      <div class="flex flex-row w-full">
        <div class="left">
            <div style="cursor: pointer;" @click="onClickAvatar">
                <chat-avatar size="medium" :conversation="chatingSession" />
            </div>

            <div class="conversation-info">
                <!-- 单聊会话信息 -->
                <div v-if="chatingSession.conversationType === 1" class="conversation-row1">
                    <div class="center-row select-text">
                        <div class="text-truncate name">{{ getConversationName(chatingSession) }}</div>
                        <div v-if="chatingSession.unregistered" class="tag-info header-unregister">
                            {{t("im.public.logout")}}
                        </div>
                        <div v-if="contactStore.isFollow(chatingSession.targetCardId)" class="header-follow">
                            {{t("im.msg.specialAttention")}}
                        </div>
                        <RelationTag :relation="chatingSession.relation"/>
                    </div>
                </div>

                <!-- 群聊会话信息 -->
                <div v-else-if="chatingSession.conversationType === 3" class="conversation-row1">
                    <div class="center-row select-text">
                        <div class="text-truncate name"> {{ getConversationName(chatingSession) }}</div>
                        <RelationTag :relation="chatingSession.relation"/>
                    </div>
                </div>
                <!-- 助手会话信息 -->
                <div v-else-if="chatingSession.conversationType === 6" class="conversation-row1">
                    <div class="center-row select-text">
                        <div class="text-truncate name"> {{ chatingSession.name }}</div>
                    </div>
                </div>
                <ConversationRelationTag :session="chatingSession" v-if="chatingSession.conversationType !== 6"/>
            </div>
        </div>
        <div class="right">
            <!-- 清零消息调试 -->
            <!-- <div @click="msgStore.loadCleanMessage()">test</div> -->
            <div v-if="!msgStore.isAssistantSession" @click="openRecord">
                <t-tooltip :content="t('im.public.chat_record')" :show-arrow="false" placement="bottom">
                    <!-- <SvgIcon name="im-history" class="chat_header_add" /> -->
                    <i class="i-svg:im-history chat_header_add" />
                </t-tooltip>
            </div>
            <div v-if="showAddMember()"  @click="onClickAddMember">
                <t-tooltip :content="t('im.public.addMember')" :show-arrow="false" placement="bottom">
                    <!-- <SvgIcon name="im-add-friend" class="chat_header_add" /> -->
                    <i class="i-svg:im-add-friend chat_header_add" />
                </t-tooltip>
            </div>
            <div v-if="showSeting()"  @click="openSetting">
                <t-tooltip :content="t('im.tools.setting')" :show-arrow="false" placement="bottom">
                    <!-- <SvgIcon name="im-setting" class="chat_header_add" /> -->
                    <i class="i-svg:im-setting chat_header_add" />
                </t-tooltip>
            </div>
        </div>
      </div>
      <div v-if="productId" class="flex flex-row w-full mt-[15px] mb-[1px]">
        <product v-if="productId" :product-id="productId" />
      </div>
    </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import ChatAvatar from './ChatAvatar.vue';
import RelationTag from '@renderer/components/contacts/relationTag.vue'
import ConversationRelationTag from '@renderer/views/message/components/conversationRelationTag.vue'
import { getConversationName } from '../service/msgUtils';
import { useMessageStore } from '../service/store';
import { useImToolStore } from '../tool/service/tool';
import { getGroupMembers } from '../service/request'
import { useContactsStore } from '@renderer/store/modules/contacts';
import { showDialog } from '@renderer/utils/DialogBV';
import { ImToolChating, ImToolContainer } from '../tool/service/type';
import { useI18n } from 'vue-i18n';
import { useSessionSetting } from '../tool/service/chatSetting';
import LynkerSDK from "@renderer/_jssdk";
import Product from './product.vue';
import { computed } from 'vue';

const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();

const productId = computed(() => {
  return msgStore.chatingSession.productId;
});

const contactStore = useContactsStore();
const msgStore = useMessageStore();
const { chatingSession } = storeToRefs(msgStore);

const onClickAddMember = async () => {
    if (chatingSession.value.conversationType === 1) {
       const hasAuth = await useSessionSetting().onClickAddMember();
       if (hasAuth) {
          const members = Array.from(msgStore.allMembers.get(chatingSession.value.localSessionId).keys());
          showDialog('dialogCreateGroup', { selected: members, myCard: chatingSession.value.myCardId, conversationType: chatingSession.value.conversationType });
       }
    } else {
        const members = await getGroupMembers(chatingSession.value.targetId);
        const groupMemberIds = members?.map(item => item.openid);
        console.error('已经选中的', groupMemberIds);

        showDialog('dialogCreateGroup', { selected: groupMemberIds, groupType: chatingSession.value.relation, myCard: chatingSession.value.myCardId, groupId: chatingSession.value.targetId, conversationType: chatingSession.value.conversationType});
    }
}

const showAddMember = () => {
    if(chatingSession.value.conversationType === 1 && !['IDLE_TEMPORARY'].includes(chatingSession.value.relation)) {
        return true;

    } else if(chatingSession.value.conversationType === 3) {
        return ['0', '3', '10', '15', '20', '23']?.includes(chatingSession.value.relation) && chatingSession.value?.inSession;

    } else {
        return false;
    }
}

const showSeting = () => {
  // 稍后处理助手不显示设置
  if (chatingSession.value.targetId === `assistant8app8pending`) {
    return false;
  }
  if(chatingSession.value.conversationType === 1 || msgStore.isAssistantSession) {
      return true;

  } else {
      return chatingSession.value?.inSession;
  }
}

const openSetting = () => {
    useImToolStore().toggleTool(ImToolContainer.Chating, { chatingTool: ImToolChating.Setting})
}

const openRecord = () => {
    useImToolStore().toggleTool(ImToolContainer.Chating, { chatingTool: ImToolChating.Record})
}

const onClickAvatar = () => {
    let info: ConversationMemberToSave = null;
    let myInfo: ConversationMemberToSave = null
    if (chatingSession.value.conversationType === 1) {
        info = msgStore.allMembers.get(chatingSession.value.localSessionId)?.get(chatingSession.value.targetCardId);
        myInfo = msgStore.allMembers.get(chatingSession.value.localSessionId)?.get(chatingSession.value.myCardId);
        console.log('===>info,myInfo', info, myInfo);

        ipcRenderer.invoke("identity-card", { cardId: info.cardId, myId: myInfo.cardId });

    } else {
        openSetting()
    }
}

</script>

<style lang="less" scoped>
@import "../style/variable.less";
:global(.t-tooltip .t-popup__content) {
  margin-left:8px;
}
.flex {
    display: flex;
    flex-direction: row;
    align-items: center;
}
.header{
    background-color: #fff;
}
.header:extend(.flex) {
    justify-content: space-between;
    padding: 7px 16px;
    border-bottom: 1px solid @kyy_gray_3;
}

.left:extend(.flex) {
    width: 0;
    flex: 1;
    overflow: hidden;
    align-items: center;
}

.right:extend(.flex) {
    gap: 4px;

    img {
        height: 20px;
        margin:0px;
    }

    div:extend(.flex) {
        padding: 2px;
        cursor: pointer;
        .chat_header_add[data-v-58476230] {
            margin-right: 0px;
        }
        &:hover {
            background-color: #EAECFF;
            border-radius: 4px;
        }
        &:active {
            background-color: #DBDFFF;
            border-radius: 4px;
            padding-right:-10px;
        }
    }
}

.conversation-info {
    display: block;
    margin-left: 12px;
    overflow: hidden;
    height: 42px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.conversation-row1 {
    display: flex;
    flex-direction: row;
    width: 100%;
    gap: 8px;
    font-size: 14px;
}

.center-row {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    overflow: hidden;
    gap: 8px;
}

.name {
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    color: @kyy_font_1;
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}


.chat_header_add {
  // height: 24px;
  // width: 24px;
  font-size: 24px;
  color: @kyy_font_2;
}

.header-follow {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0 4px;
    height: fit-content;
    border-radius: 4px;
    font-size: 12px;
    background-color: var(--bg-kyy_color_bg_light, #FFF);
    color: var(--text-kyy_color_text_3, #828DA5);
    user-select: none;

    &::before {
        content: '';
        background: url('@renderer/assets/identity/0.icon_icon-star-fill.png');
        background-size: 16px;
        width: 16px;
        height: 16px;
    }
}

.header-unregister {
    color: @kyy_red_4;
    background-color: @kyy_red_10;
    border-color: currentColor;
}

</style>
