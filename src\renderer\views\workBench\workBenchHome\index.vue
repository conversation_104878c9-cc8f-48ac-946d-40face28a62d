<template>
  <div class="work-bench-home-view about" :class="isPromotionalpage?'isblue':''">
    <div class="content-box">
      <div class="tab-boxs">
        <div v-for="(item,index) in tabs" :key="index" :class="{
        'tab-active': index===0,
        'tab-item': true,
        'tab-red': item?.red,
      }" @click="tabClick(item)">
          {{item.name}}
        </div>
      </div>
      <div class="header-boxs">
        <CultureWall @parentSetWorkBenchTabItem="setWorkBenchTabItem" :checkIsAdminData="checkIsAdminData"
          :activationGroupItem="activationGroupItem" :isWorkbench="true" class="cultureWall"></CultureWall>

        <div class="img-boxs">

          <img @click="jumpWeb(0)" :src="imageList[0]?.image_url?imageList[0]?.image_url:img0">
          <img @click="jumpWeb(1)" :src="imageList[1]?.image_url?imageList[1]?.image_url:img1">
        </div>
        <div class="info-box">
          <WorkHomeHeader v-if="!isPromotionalpage" :activationGroupItem="activationGroupItem"
            @parentSetWorkBenchTabItem="setWorkBenchTabItem" @updisPromotionalpage="updisPromotionalpage">
          </WorkHomeHeader>
        </div>
      </div>
      <div class="info-vip-box">
        <ModuleList v-if="!isPromotionalpage" :appList="appList" :activationGroupItem="activationGroupItem"
          @setWorkBenchTabItem="setWorkBenchTabItem" :checkIsAdminData="checkIsAdminData"></ModuleList>
      </div>
    </div>
    <Tricks :offset="{ x: '-32', y: '-40' }" uuid="数智工场-首页" />
  </div>

</template>

<script setup lang="ts" name="workBenchHome">
  import avatar from "@renderer/components/kyy-avatar/index.vue";
  import { ClientSide } from "@renderer/types/enumer";
  import { getApprovalFlag } from "@/api/zhixing/api/todo";

  import { getLang } from "@/utils/auth";
  import CultureWall from "../components/CultureWall.vue";
  import { useRoute, useRouter } from "vue-router";
  import { onMounted, onActivated, ref, watch, nextTick, getCurrentInstance, computed } from "vue";
  import { useI18n } from "vue-i18n";
  import { onMountedOrActivated } from "@/hooks/onMountedOrActivated";
  import Promotionalpage from "../components/Promotionalpage.vue";
  import WorkHomeHeader from "../components/WorkHomeHeader.vue";
  import { getBaseUrl } from "@renderer/utils/apiRequest";
  import ModuleList from "../components/ModuleList.vue";
  import { getOpenid, setPersonSquareId } from "@renderer/utils/auth";
  import {

    getIndividualSquareAxios,
    getSharedSquaresAxios,

  } from "@renderer/api/politics/api/businessApi";
  import { getSquareByopenId } from "@/api/business/manage";

  import { factoryDetail, getRedPoint } from "@renderer/api/workBench/index.ts";
  import img0 from "@/assets/workbench/Image.svg";
  import img1 from "@/assets/workbench/Image1.svg";
  import to from "await-to-js";
  import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
  import { filterImg, openAppListFn } from "../utils.ts";
  import { checkIsAdmin, AppAllApp, teamcompanysTotal } from "@renderer/api/workBench";
  import {
    getAppsState,
  } from "@/api/fengcai/manage";
  import LynkerSDK from '@renderer/_jssdk';

  const { ipcRenderer, shell } = LynkerSDK;
  const props = defineProps({
    activationGroupItem: {
      type: Object,
      default: () => { },
    },
    tabList: {
      type: Array,
      default: () => [],
    },
  });
  const tabs = ref([{
    name: '首页',
    uuid: 'home',

  }, {
    name: '活动',
    uuid: 'activities',
  }, {
    uuid: 'about',
    name: '关于我们',
  }, {
    name: '公告',
    uuid: 'notice'
  }])
  const { proxy } = getCurrentInstance();
  const router = useRouter();
  const imageList = ref([])
  const langs = computed(() => proxy.$i18n.locale);
  const emits = defineEmits(["setWorkBenchTabItem", 'parentSetWorkBenchTabItem', "fistTab"]);
  const appList = ref([]);
  const checkIsAdminData = ref(null);

  const route = useRoute();
  const { t } = useI18n();
  const isPromotionalpage = ref(false);
  const getApp = async () => {
    try {
      const teamId = window.localStorage.getItem("workBenchTeamid");

      // Fetch image details
      const imageRes = await factoryDetail(teamId);
      imageList.value = imageRes.data.data.info;

      // Fetch team company total data
      const res1 = await teamcompanysTotal(teamId);

      // Fetch app data
      const res = await AppAllApp(teamId);
      if (res?.data?.data) {
        // Add required tabs if they don't exist
        const requiredUuids = ['society-article', 'platform-view', 'square'];
        requiredUuids.forEach(uuid => {
          if (!tabs.value.some(e => e.uuid === uuid)) {
            const element = res.data.data.find(e => e.uuid === uuid);
            if (element && element.uuid !== 'square') {
              tabs.value.push(element);
            }
          }
        });
        // Map res1.data.data for quick lookup
        const elementMap = new Map(res1.data.data.map(item => [item.uuid, item]));

        // Set red property based on count
        for (let index = 0; index < tabs.value.length; index++) {
          const element = tabs.value[index];
          const element1 = elementMap.get(element.uuid);
          if (element1) {
            element.red = element1.count > 0;
          }
        }

        // Check red point for 'about' tab
        checkRedPointForAboutTab(teamId);
      }
      getAppsState(null, window.localStorage.getItem("workBenchTeamid")).then((hasapp) => {
        if (hasapp.data.data.square && !tabs.value.some(e => e.uuid === 'square')) {
          tabs.value.push({
            name: '拾光相册',
            uuid: 'square'
          });
        }

      })

    } catch (error) {
      console.error(error, 'tabsssserrorerror');
    }
  };

  const checkRedPointForAboutTab = async (teamId: string) => {
    try {
      const res = await getRedPoint(teamId);
      console.log(res.data.data, "是否显示红点------------------------------");
      const index = tabs.value.findIndex(e => e.uuid === 'about');
      if (index !== -1) {
        const { progress, introduce, honor } = res.data.data;
        tabs.value[index].red = !(!progress.workshop && !introduce.workshop && !honor.workshop)
        console.log(tabs.value, 'tabs.valuetabs.value');

      }
    } catch (error) {
      console.error(error, 'checkRedPointForAboutTab error');
    }
  };
  const jumpWeb = (index) => {
    if (!imageList.value[index]?.image_url && !(checkIsAdminData?.value?.isAdmin || checkIsAdminData?.value?.super || checkIsAdminData?.value?.superAdmin)) {
      return MessagePlugin.error('请联系管理员上传宣传图')
    }
    if (!imageList.value[index]?.image_url && (checkIsAdminData?.value?.isAdmin || checkIsAdminData?.value?.super || checkIsAdminData?.value?.superAdmin)) {
      router.push("/workBenchIndex/workBenchEnterprise?Advertisement=1");

      setWorkBenchTabItem({
        activeIcon: "workshop",
        icon: "workshop",
        path: "/workBenchIndex/workBenchEnterprise",
        path_uuid: "workBench",
        name: "workBenchEnterprise",
        title: t("banch.glht"),
        type: 17
      })

      return
    }
    if (imageList.value[index].skip_type === 1 && imageList.value[index].skip_param) {
      const h5Getway = "h5";
      const h5Path = "safeCheck";
      const h5Link = `${getBaseUrl(h5Getway)}/${h5Path}?link=${encodeURIComponent(imageList.value[index].skip_param)}`;
      try {
        shell.openExternal(h5Link);
      } catch (error) { }
    }
    if (imageList.value[index].skip_type === 0 && imageList.value[index].image_url) {
      ipcRenderer.invoke("view-img", JSON.stringify({
        url: imageList.value[index].image_url,
      }));
    }

  }



  const setWorkBenchTabItem = (val) => {
    emits("setWorkBenchTabItem", val);
  };


  const onOpenPersonSquare = async (goSquare?) => {
    console.log('onOpenPersonSquare')

    return new Promise(async (resolve, reject) => {
      const params = {};
      const [err, res] = await to(getIndividualSquareAxios(params));
      if (err) {
        // MessagePlugin.error(err?.message);
        console.log(err?.message)
        reject()
        return;
      }
      const { data } = res;
      // 启用成功后，继续跳广场
      const squareId = goSquare || data?.info?.square?.squareId
      console.log(goSquare, squareId)
      if (squareId) {
        // onGoSquarePage(squareId)
        // localStorage.setItem('personSquareId', squareId)
        setPersonSquareId(squareId)
        resolve(squareId);
      } else {
        // MessagePlugin.error('缺少广场ID')
        reject();
      }
    })
  };
  const isOpenSelfSqaure = () => {
    return new Promise(async (resolve, reject) => {
      // const params = {
      // team_id: props.activationGroupItem.teamId || localStorage.getItem("workBenchTeamid"), // 组织 ID（获取组织广场号时使用）
      // open_id: getOpenid(), // 获取个人广场号时使用
      // };
      const [err1, res1] = await to(getSharedSquaresAxios({ team_id: props.activationGroupItem.teamId || localStorage.getItem("workBenchTeamid") }));
      const [err, res] = await to(getSharedSquaresAxios({ open_id: getOpenid() }));
      if (err || err1) {
        console.log(err?.message, '进入errrrrrrrrr');
        reject();
        return;
      }
      console.log(res);
      const { data } = res;
      const data1 = res1.data;
      console.log(data, 'asdasdasdasdasd');
      console.log(data1, 'asdasdasdasdasddata1data1');
      console.log(props.activationGroupItem, 'asdasdasdasdasd props.activationGroupItem');

      if (!data?.selfOpened) {
        const confirmDia = DialogPlugin({
          header: "提示",
          theme: "info",
          body: "您当前未启用广场应用，确定启用？",
          closeBtn: null,
          confirmBtn: "确定",
          className: "delmode",
          onConfirm: async () => {
            // 删除字段操作
            confirmDia.hide();
            try {
              await onOpenPersonSquare();
              resolve();
            } catch (error) {
              reject();
            }
          },
          onClose: () => {
            confirmDia.hide();
            reject();
          },
        });
      } else {
        setPersonSquareId(data?.square?.squareId)
        window.localStorage.setItem('albumTeamId', props.activationGroupItem.teamId || localStorage.getItem("workBenchTeamid"))
        emits("setWorkBenchTabItem", {
          path: '/workBenchIndex/work_bench_album',
          path_uuid: "bench_activityList",
          title: '拾光相册',
          activeIcon: "album",
          icon: "album",
          name: "bench_ablumDetail",
          type: ClientSide.ALBUM,
          query: {
            squareId: data1?.square?.squareId
          }
        });
        router.push({
          path: '/workBenchIndex/work_bench_album',
          query: {
            squareId: data1?.square?.squareId
          }
        })
        console.log(data1, 'data111');
        console.log(data, '22data111');
        resolve();
      }
    })
  }

  const tabClick = (item) => {
    const { teamId } = props.activationGroupItem;
    const actions = {
      'society-article': () => router.push(openAppListFn(item, props.activationGroupItem)),
      'platform-view': () => router.push(openAppListFn(item, props.activationGroupItem)),
      'square': () => {
        // router.push({
        //   path: '/workBenchIndex/work_bench_album',
        //   query: { team_id: teamId, platform: 'digital-platform' }
        // });
        isOpenSelfSqaure()

      },
      'activities': () => {
        router.push({
          path: '/workBenchIndex/activity_active_comp',
          query: { team_id: teamId, platform: 'digital-platform' }
        });
        emits("setWorkBenchTabItem", {
          path: '/workBenchIndex/activity_active_comp',
          path_uuid: "bench_activityList",
          title: '活动',
          activeIcon: "workshop",
          icon: "workshop",
          name: "bench_activityList",
          type: ClientSide.ACTIVITY,
        });
      },
      'policy': () => {
        router.push("/workBenchIndex/policy-express");
        emits("setWorkBenchTabItem", {
          title: t('policy.policy_express'),
          path: "/workBenchIndex/policy-express",
          path_uuid: "policy",
          name: "PolicyExpress",
          type: 30,
        });
      },
      'notice': () => {
        router.push("/workBenchIndex/notice");
        emits("setWorkBenchTabItem", {
          path: "/workBenchIndex/notice",
          path_uuid: "notice",
          title: "公告",
          iswork: true,
          name: "notice",
          type: 24,
        });
      },
      'about': () => {
        router.push({
          path: "/workBenchIndex/aboutour",
          query: { platform: "workBench", fromType: 2 }
        });
        emits("setWorkBenchTabItem", {
          activeIcon: "workshop",
          icon: "workshop",
          path: "/workBenchIndex/aboutour",
          path_uuid: "workBench",
          name: "bench_aboutour",
          title: t("member.squarek.l"),
          type: 17,
        });
      }
    };

    const action = actions[item.uuid];
    if (action) {
      action();
    } else {
      console.warn(`Unknown uuid: ${item.uuid}`);
    }
  };
  const getCheckAdmin = async () => {
    const res = await checkIsAdmin({ ...props.activationGroupItem.user_ids }, props.activationGroupItem.teamId);
    checkIsAdminData.value = res?.data?.data;
  };

  const updisPromotionalpage = (flag) => {
    if (flag) {
      window.localStorage.removeItem("isPromotionalpage");
      isPromotionalpage.value = true;
      return;
    }
    isPromotionalpage.value = false;
    window.localStorage.setItem("isPromotionalpage", true);
  };
  watch(
    () => props.activationGroupItem.teamId,
    (newValue) => {
      console.log(route.query, 'route.query我看看');

      getCheckAdmin();
      if (!route.query?.istab && route.query?.toAdmin && !route.query?.platform) {
        pushRouter();
      }
    },
    { deep: true });
  const pushRouter = () => {
    console.log(route.query, "route.queryroute.queryroute.query1111111");
       let appName1 = filterImg({
        uuid: route.query.uuid,
        name: route.query.name,
      }).name;

    if (route.query?.toLkAd) {
       if (route.query.jumpPath === '/workBenchIndex/ringkolAdvertisement') {
        let activeIndex = 3,title =  t('ad.gggl')
        if(route.query.from === 'square'){
          activeIndex = 0
          title = '另可广告'
        }
        router.push({
          path: `/workBenchIndex/ringkolAdvertisement`,
          query: {
            activeIndex
          },
        });
        ipcRenderer.invoke("set-work-bench-tab-item", {
          path: `/workBenchIndex/ringkolAdvertisement`,
          name: "ringkolAdvertisement",
          path_uuid: "ad-lk",
          title,
          type: 32,
          addNew: true,
          query: {
            activeIndex
          },
        });
        return
      }
      router.push({
        path: `/workBenchIndex/rkAdDetails`,
        query: {
          id: route.query.adId,
          teamId: route.query?.teamId,
        },
      });
      ipcRenderer.invoke("set-work-bench-tab-item", {
        path: `/workBenchIndex/rkAdDetails`,
        name: "rkAdDetails",
        path_uuid: "rkAdDetails",
        title: t('ad.ggxq'),
        type: 32,
        addNew: true,
        query: {
          id: route.query.adId,
          teamId: route.query?.teamId,
        },
      });
      return
    }
    if (route.query?.singleTab) {
      router.push({
        path: `/workBenchIndex/AdDetails`,
        query: {
          id: route.query.id.toString(),
          teamId: route.query.teamId || currentTeamId.value,
          flag: "edit",
        },
      });
      ipcRenderer.invoke("set-work-bench-tab-item", {
        path: `/workBenchIndex/AdDetails`,
        name: "bench_ad_details",
        path_uuid: route.query.uuid,
        title: t('ad.ggxq'),
        type: route.query.type - 0,
        netCover: true,
        addNew: true,
        query: {
          id: route.query.id.toString(),
          teamId: route.query.teamId || currentTeamId.value,
          flag: "edit",
        },
      });
      return
    }
    console.log(route.query, props, 'route.query我看看');
    if (route.query.jumpPath && route.query.uuid && !props.activationGroupItem.noJump && !route.query?.singleTab) {
      let appName = filterImg({
        uuid: route.query.uuid,
        name: route.query.name,
      }).name;
      emits("fistTab");
      setTimeout(() => {
        router.push(
        openAppListFn(
          {
            uuid: route.query.uuid,
            name: appName,
          },
          props.activationGroupItem,
          route.query,
        ),
      );
      }, 1000);
    }

  };
  onMountedOrActivated(() => {
    getApp()

    try {
      const isPromotionalpageValue = window.localStorage.getItem("isPromotionalpage");
      isPromotionalpage.value = isPromotionalpageValue === null ? false : !JSON.parse(isPromotionalpageValue);
    } catch (error) {
      isPromotionalpage.value = false;
    }
    if (props.activationGroupItem.teamId) {
      getCheckAdmin();
    }
    emits("setWorkBenchTabItem", {
      activeIcon: "workshop",
      icon: "workshop",
      path: "/workBenchIndex/workBenchHome",
      path_uuid: "workBench",
      title: t("banch.banch"),
      iswork: true,
      name: "workBenchHome",
      type: 17,
    });
  });
</script>

<style lang="less" scoped>
  .info-box {
    width: 280px;
    height: 308px;
    border-radius: 8px;
    /* background: #EFF3FF; */
    background: url('../../../assets/img/workbg.png') no-repeat;
    background-size: 100% 100%;
    margin-left: 12px;
  }

  .avatar-box {
    display: flex;
    align-items: center;
  }

  .info-boxs-text {
    margin-left: 16px;
  }


  .img-boxs {
    display: flex;
    width: 224px;
    flex-wrap: wrap;
    gap: 12px;
    margin-left: 12px;

    img {
      width: 224px;
      height: 148px;
      border-radius: 8px;
    }
  }

  .cultureWall {
    width: 616px;
    padding: 0 !important;
  }

  .content-box {
    width: 1168px;
  }

  .header-boxs {
    height: 332px;
    width: 100%;
    border-radius: 16px;
    background: #FFF;
    display: flex;
    align-items: center;
    padding: 12px;
  }

  .tab-boxs {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #1A2139;
    font-size: 16px;
    font-weight: 600;
    width: 100%;
    padding: 16px 0 16px 16px;
  }

  .tab-item {
    padding: 4px 12px;
    cursor: pointer;
  }



  .tab-active {
    border-radius: 16px;
    background: #1D63E9;
    color: #fff;
  }


  .isblue {
    background: #0F1595 !important;
  }

  .about {
    width: 100%;
    height: 100%;
    background: url("@/assets/bench/workbenchbg.svg");
    display: flex;
    flex-direction: column;
    align-items: center;

    .boxContent {
      width: 1184px;
    }
  }

  .work-bench-home-view {
    background: url("@/assets/member/svg/bg_vip.svg");
    background-repeat: no-repeat;
    background-size: cover;
  }

  .info-vip-box {
    margin-top: 24px;
  }




  .lin-text {
    color: var(--text-kyy_color_text_2, #516082);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    position: relative;
    padding-left: 8px;
  }

  .tab-red {
    position: relative;
  }

  .tab-red::after {
    content: '';
    position: absolute;
    top: 2px;
    width: 8px;
    height: 8px;
    right: 6px;
    border-radius: 50%;
    border: 1px solid #FFF;
    background: #FF4AA1;
  }

  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    background-color: #f5f5f5;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    background-color: #e3e6eb;
  }
</style>
