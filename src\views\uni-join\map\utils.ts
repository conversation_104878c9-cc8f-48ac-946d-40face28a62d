// export const BAIDU_AK = __APP_ENV__.VITE_MAP_KEY;
export const BAIDU_AK = 'CfbnYiaUGtwLLNNDFxNVJTQWOPtMO68u';
export const BAIDU_API_URL = `https://api.map.baidu.com/api?type=webgl&v=1.0&ak=${BAIDU_AK}&callback=_initBMap_`;

// 地图配置
export const mapOptions = {
  ak: BAIDU_AK,
  plugins: [],
};

export const markerIcon = {
  imageUrl: 'data:image/png;base64,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',
  size: { width: 28, height: 59 },
  anchor: { x: 0, y: 0 },
};

// 将百度地图返回的数据转换为兼容老数据格式
export const covertData = (data) => {
  const { addressComponents, content, surroundingPois, address, point } = data;
  const poi = surroundingPois[0];
  const detail = content?.address_detail;
  return {
    name: poi?.title || address,
    location: point || poi.point,
    address: address || poi.address || poi.title,
    addressComponent: {
      ...addressComponents,
      citycode: String(detail?.city_code),
      township: detail?.town,
    },
    surroundingPois,
  };
};

// 获取跳转地图的链接
export const getMarkerUrl = ({ lat, lng, name, title }) => `https://api.map.baidu.com/marker?location=${lat},${lng}&title=${title || '定位地址'}&content=${name}&output=html`;

// 跳转到浏览器打开地图
export const openExternalMap = (query) => {
  window.open(getMarkerUrl(query));
};

// 定位失败的默认地址
export const getDefaultLocation = () => {
  return { point: { lng: 113.543005, lat: 22.265842 }, name: '珠海市政府', code: 140 };
};

/**
 * 文本高亮
 * @param text 原始文本
 * @param search 高亮文本
 * @param style 高亮样式
 * @example
 * <pre><div class="line-1" v-html="highlight(item.name, keyword)" /></pre>
 */
export const highlight = (text: string, search: string, style = 'color:#4D5EFF') => {
  if (!text) return '';
  if (!search) return text;

  const escapedSearch = search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  return text.replace(/\s/g, '&nbsp;').replace(new RegExp(`(${escapedSearch})`, 'gi'), `<span style="${style}">$1</span>`);
};