import type { register, identifyCode, loginMa, resetPw, jwt, joinTeam, sms, loginMaV2 } from './loginModel';
import { iam_srvRequest, client_orgRequest, ringkolRequest } from '../requestApi';

export function registerAccount(data: register) {
  return iam_srvRequest({
    method: 'post',
    url: '/v1/accounts',
    data,
  });
}

export function bindAccount(data: any) {
  return iam_srvRequest({
    method: 'put',
    url: '/v1/accounts',
    data,
  });
}


export function getIdentifyCode(data: identifyCode) {
  return iam_srvRequest({
    method: 'post',
    url: '/v1/proofs',
    data,
  });
}

export function loginAccount(data: loginMa) {
  return iam_srvRequest({
    method: 'post',
    url: '/v1/passport/guest',
    data,
  });
}

export function loginAccountV2(data: loginMaV2) {
  return ringkolRequest({
    method: "post",
    url: "/iam/v2/account/loginAccount",
    data
  });
}

export function getJwt(data: jwt) {
  return iam_srvRequest({
    method: 'post',
    url: '/v1/passport',
    data,
  });
}

export function resetPassword(data: resetPw) {
  return iam_srvRequest({
    method: 'put',
    url: '/v1/password',
    data,
  });
}

export function regionsSearch(data: { code: string }) {
  return iam_srvRequest({
    method: 'post',
    url: '/v1/regions/search',
    data,
  })
}

export function getProfile() {
  return iam_srvRequest({
    method: 'get',
    url: '/v1/profiles/me',
  });
}

export function getAreaCodes(data: any) {
  return iam_srvRequest({
    method: 'post',
    url: '/v1/area_codes/search',
    data,
  });
}

export function getQrCode() {
  return iam_srvRequest({
    method: 'get',
    url: '/v1/accounts/qrcode',
  });
}

export function joinOrg(data: joinTeam) {
  return client_orgRequest({
    method: 'post',
    url: '/teams/join',
    data,
  });
}

export function getSms(data: sms) {
  return client_orgRequest({
    method: 'post',
    url: '/validator/sms/create',
    data,
  });
}

export function getLinkDetail(params: {link:string}) {
  return client_orgRequest({
    method: 'get',
    url: '/staffs-apply/getLinkDetail',
    params,
  });
}

export function getSmsDetail(id:string) {
  return client_orgRequest({
    method: 'get',
    url: `/staffs-apply/${id}`,
  });
}

export function smsAgree(data:{id:number}) {
  return client_orgRequest({
    method: 'post',
    url: '/staffs-apply/sms-agree',
    data,
  });
}

export function smsRefuse(data:{id:number}) {
  return client_orgRequest({
    method: 'post',
    url: '/staffs-apply/sms-reject',
    data,
  });
}

export function getUuidDetail(params: {app: string, uuid: string}) {
  return client_orgRequest({
    method: 'get',
    url: `/common/sms-detail`,
    params
  });
}

export function uuidAgree(params: {app: string, uuid: string}) {
  return client_orgRequest({
    method: 'post',
    url: '/common/sms-agree',
    params,
  });
}

export function uuidRefuse(params: {app: string, uuid: string}) {
  return client_orgRequest({
    method: 'post',
    url: '/common/sms-reject',
    params,
  });
}

export function checkAccount(data:{acc:string}) {
  return iam_srvRequest({
    method: 'post',
    url: '/v1/accounts/check',
    data,
  });
}

export function accountExist(data: {acc:string}) {
  return iam_srvRequest({
    method: 'post',
    url: '/v1/accounts/check',
    data,
  });
}

export function verifyCode(data:{account:string,code:string}) {
  return iam_srvRequest({
    method: "put",
    url: "/v1/verify_code/check",
    data
  });
}

export function getDownLoadInfo(region:string, type:string) {
  return client_orgRequest({
    method: 'get',
    url: `/version/recently/${region}/${type}`
  })
}

export function smCheckUrl(timeout = 15000, data:{url:string, open_id:string}) {
  return iam_srvRequest({
    method: 'post',
    url: `/v1/shumei/detection_url`,
    data,
    timeout
  })
}
