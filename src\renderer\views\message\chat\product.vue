<template>
  <div
    ref="productContainer"
    class="flex flex-row items-center justify-between  gap-[8px] w-full"
  >
    <div class="w-[44px] h-[44px] flex items-center justify-center">
      <t-image
        class="rounded-8"
        :src="product?.images?.[0]"
        :style="{ width: '44px', height: '44px' }"
        fit="cover"
        shape="round"
        error=""
        loading=""
        @click="handleClick"
      />
    </div>
    <div class="flex flex-col flex-grow-2">
      <div class="text-[#F55042] text-[14px] font-bold leading-[22px] font-600">
        {{ displayPrice }}
      </div>
      <div class="flex flex-row justify-between">
        <div class="text-[#516082] text-[12px] leading-[18px] font-400">
          <span>
            含运费{{ displayExpressFee }}
          </span>
        </div>
        <div class="text-[#516082] text-[12px] leading-[18px] font-500">
          请使用手机打开该会话进行访问
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch, readonly } from 'vue';
import lodash from 'lodash';
import { getProductInfo } from '@renderer/api/im/chat';
import { MessagePlugin } from 'tdesign-vue-next';
import jssdk from '@lynker-desktop/web';

// 类型定义
interface Product {
  price?: number;
  express_fee?: number;
  region?: string;
  images?: string[];
  self_pickup?: number;
}

interface Props {
  productId: string;
}

const props = defineProps<Props>();

// 响应式数据
const product = ref<Product | null>(null);
const isLoading = ref(false);
const error = ref<string | null>(null);
const productContainer = ref<HTMLElement>();

// 价格格式化函数（缓存版本）
const formatPrice = (() => {
  const cache = new Map<number, string>();

  return (price: number, region: string = 'CN'): string => {
    const cacheKey = price * 1000 + (region === 'MO' ? 1 : 0);

    if (cache.has(cacheKey)) {
      return cache.get(cacheKey)!;
    }

    const formatted = price.toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
    const result = region === 'MO' ? `${formatted}MOP` : `${formatted}元`;

    // 限制缓存大小，避免内存泄漏
    if (cache.size > 100) {
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }

    cache.set(cacheKey, result);
    return result;
  };
})();

// 计算属性（优化版本）
const displayPrice = computed(() => {
  const price = product.value?.price;
  if (!price || price <= 0) {
    return '0.00';
  }
  return formatPrice(price / 100, product.value?.region);
});

const displayExpressFee = computed(() => {
  const fee = product.value?.express_fee;
  if (!fee || fee <= 0) {
    return '0.00';
  }
  return formatPrice(fee / 100, product.value?.region);
});

// 获取商品信息的防抖版本
const debouncedGetProductInfo = lodash.debounce(async () => {
  if (isLoading.value || !props.productId) {
    return;
  }

  try {
    isLoading.value = true;
    error.value = null;

    const res = await getProductInfo(props.productId);
    product.value = res.data.data;
  } catch (err) {
    console.error('获取商品信息失败:', err);
    error.value = '获取商品信息失败';
    MessagePlugin.error('获取商品信息失败');
  } finally {
    isLoading.value = false;
  }
}, 300);

const handleClick = () => {
  console.log('====>handleClick', props.productId);
  jssdk.previewImage(JSON.parse(JSON.stringify({
    images: product.value?.images || [],
    index: 0,
    url: product.value?.images?.[0] || '',
  })));
};

// 监听 productId 变化
watch(
  () => props.productId,
  (newId, oldId) => {
    if (newId !== oldId) {
      product.value = null; // 重置商品信息
      error.value = null;
      // 如果当前可见，立即获取新商品信息
      debouncedGetProductInfo();
    }
  },
  { immediate: true }
);

const timer = ref<NodeJS.Timeout | null>(null);

// 生命周期
onMounted(() => {
  timer.value = setInterval(() => {
    debouncedGetProductInfo();
  }, 1000 * 6);
});

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value);
  }
});

// 暴露方法供父组件调用（如需要手动刷新）
defineExpose({
  refresh: debouncedGetProductInfo,
  isLoading: readonly(isLoading),
  error: readonly(error)
});
</script>

<style scoped>

</style>

