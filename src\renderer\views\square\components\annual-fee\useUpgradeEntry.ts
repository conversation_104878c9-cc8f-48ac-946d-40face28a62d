// useUpgradeEntry.js
import { computed, onMounted, ref } from 'vue';
import to from 'await-to-js';
import { authorityCheck } from '@renderer/api/contacts/api/organize';
import { MessagePlugin } from 'tdesign-vue-next';
import { getTeamAnnualFeeInfo } from '@/api/square/square';
import { teamAnnualFeeResponse } from '@/api/square/models/square';
import { getRemainingTime } from '@/views/square/utils/format';
import { useSquareStore } from '../../store/square';

/**
 * 获取年费信息及显示升级/续费/购买入口
 *
 * @param isPersonal 以个人身份查看主页
 * @param teamId
 * @param emit
 */
export default function useUpgradeEntry(isPersonal?: boolean, teamId?: string, emit?: Function) {
  // Mac(应用市场)的包
  const isMas = __APP_ENV__.VITE_APP_MAS;
  const store = useSquareStore();
  const feeInfo = ref<teamAnnualFeeResponse>();
  const showUpgradeBtn = ref(!isMas);

  // 是否过期
  const isExpired = computed(() => new Date(feeInfo.value?.annualFeeExpiredAt).getTime() < new Date().getTime());
  // 剩余天数
  const adventDays = computed(() => getRemainingTime(new Date(feeInfo.value?.annualFeeExpiredAt).getTime()));
  const isTrial = computed(() => feeInfo.value?.annualFeeDetail?.trial);
  const teamIdToUse = computed(() => (isPersonal ? teamId : store.squareInfo?.organizationProfile?.teamId));

  // 是否显示升级入口
  const showEntry = computed(() => {
    if (!feeInfo.value) return false;
    if (!isPersonal && store.isTrial) return;
    return true;
  });

  // 获取年费信息
  const fetchFeeInfo = async () => {
    if (!isPersonal && store.isPersonal) return;

    const [err, res] = await to(getTeamAnnualFeeInfo({ team_id: teamIdToUse.value, annual_fee_detail: 'true' }));
    if (err) return;

    const data = res.data;
    feeInfo.value = res.data;

    if (!isPersonal) {
      store.annualFeeInfo = res.data;
      store.isTrial = isTrial.value;
      store.isExpired = isTrial.value && isExpired.value;
    }

    emit?.('success', data);
  };

  // 校验组织权限
  const fetchOrgApplicationPermission = async () => {
    if (!teamIdToUse.value) return;

    const [err, res] = await to(authorityCheck({ items: ['application'] }, teamIdToUse.value));
    if (err) return;

    showUpgradeBtn.value = res.data.data.application === 1 && !isMas;
  };

  const renewalVisible = ref(false);
  // 年费信息更新
  const upgradeLoaded = (data) => {
    feeInfo.value = data;
    isPersonal && emit('change', data);
  };

  // 检查广场号是否过期
  const checkIsExpired = async () => {
    if (isPersonal) await store.getSquareInfo();
    await fetchFeeInfo();

    if (isExpired.value) {
      const msg = MessagePlugin.warning({
        duration: 3000,
        content: (h) => h('div', [
          h('span', '广场号已过期，请先续费'),
          h('a', {
            style: { marginLeft: '10px' },
            onClick: () => {
              MessagePlugin.close(msg);
              renewalVisible.value = true;
            },
          }, '去续费>>'),
        ]),
      });

      return;
    }

    renewalVisible.value = true;
  };

  onMounted(() => {
    fetchFeeInfo();
    !isPersonal && fetchOrgApplicationPermission();
  });

  return {
    feeInfo,
    showUpgradeBtn,
    isExpired,
    adventDays,
    isTrial,
    showEntry,
    renewalVisible,
    upgradeLoaded,
    checkIsExpired,
    fetchFeeInfo,
  };
}
