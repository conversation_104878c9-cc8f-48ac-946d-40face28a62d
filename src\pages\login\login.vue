<template>
  <iframe v-if="iframeFlag" ref="iframeRef" src="http://*************:5173/account/login" class="iframe"
    :style="{ width: '100%', height: '100%', border: 'none' }"></iframe>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
// 接收 iframe 消息的方法
const handleIframeMessage = (event) => {
  // 安全检查：验证消息来源
  // 替换为您的 iframe 实际域名
  const allowedOrigins = ['https://your-iframe-domain.com', 'http://localhost:3000','http://*************:3002','http://*************:5173']

  if (!allowedOrigins.includes(event.origin)) {
    console.warn('收到来自未授权域名的消息:', event.origin)
    return
  }

  try {
    // 解析消息数据
    const messageData = typeof event.data === 'string' ? JSON.parse(event.data) : event.data

    console.log('收到 iframe 消息:', messageData)

    // 根据消息类型处理不同的业务逻辑
    switch (messageData.type) {
      case 'login_success':
        handleLoginSuccess(messageData.data)
        break
      case 'login_error':
        handleLoginError(messageData.data)
        break
      case 'resize':
        handleIframeResize(messageData.data)
        break
      case 'close':
        handleIframeClose()
        break
      default:
        console.log('未知消息类型:', messageData.type)
    }
  } catch (error) {
    console.error('解析 iframe 消息失败:', error)
  }
}


// 处理登录错误
const handleLoginError = (data) => {
  console.error('登录失败:', data)
  // 处理登录失败的逻辑
  // 例如：显示错误信息
}



















  import { ref, onMounted, onUnmounted } from 'vue'
  const iframeRef = ref < HTMLIFrameElement | null > (null);
  const iframeFlag = ref(true);
  const handleIframeClose = () => {
  // 关闭或隐藏 iframe 的逻辑
}


const handleLoginSuccess = () => {
  console.log('登录成功',data);

}
  onMounted(() => {
    window.addEventListener('message', handleIframeMessage);

  });
  onUnmounted(() => {
    iframeFlag.value=false
    window.removeEventListener('message', handleIframeMessage)
  })
</script>

<style lang="less" scoped>

</style>
