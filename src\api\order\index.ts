import { client_orgRequest, ringkolRequest } from '../requestApi';

export function invoiceCreate(data: any) {
  return client_orgRequest({
    method: 'post',
    url: `/new-partner/invoice/create`,
    data
  });
}
export function payCreate(data: any) {
  return client_orgRequest({
    method: 'post',
    url: `/new-partner/pay/create`,
    data
  });
}
export function payStatus(sn: any, order_sn: string) {
  return client_orgRequest({
    method: 'post',
    url: `/new-partner/pay/status/${sn}`,
    data: {
      order_sn: order_sn
    }
  });
}

export function teamListByUni(data: any) {
  return client_orgRequest({
    method: 'get',
    url: `/teams/list-by-uni`,
    params: data
  });
}

const mapPath = Object.freeze({
  abroad: '/place_abroad/v1/search',
  suggestion: '/place/v2/suggestion',
});

export const baiduMapApi = (
  params: any,
  type: keyof typeof mapPath = 'suggestion'
): Promise<any> => ringkolRequest.post(`/global/v1/baidu/map`, {
  path: mapPath[type],
  query: { ...params, output: 'json' },
});