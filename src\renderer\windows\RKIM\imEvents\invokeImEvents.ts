/*
 * @Author: ZHANGXIAO
 * @Date: 2024-05-23 11:11:29
 * @LastEditors: ZHANGXIAO
 * @LastEditTime: 2024-07-23 19:36:53
 * @FilePath: \lynker-desktop\src\renderer\windows\RKIM\imEvents\invokeImEvents.ts
 * @Description: 中转ipc调用openIM事件
 */
import { getBaseUrl } from '@renderer/utils/apiRequest';
import { imReviewApiV2 } from "@renderer/api/im/apiV2";
import { logHandler } from "@renderer/log";
import { configInfo } from '@renderer/views/setting/util';

import {
 getOpenImToken,
 getOpenid,
 setAppVersion,
 setMsgWinRload,
 getMsgWinRload
} from '@renderer/utils/auth';
import lodash from 'lodash';
import { showDialog } from '@renderer/utils/DialogBV';
import {
  WsResponse,
  MessageItem
} from "@rk/im-sdk/dist/types/entity";
import {
  Platform
} from "@rk/im-sdk";
import {
  SendMsgParams, SearchLocalParams, GetAdvancedHistoryMsgParams
} from "@rk/im-sdk/dist/types/params";
import { i18nt } from "@/i18n";
import { replyBridgeRes } from '../utils';
import { IMSDK } from './imSetup';
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer, shell } = LynkerSDK;

// openIm登录，当前登录平台号
const getPlatform = (): Platform => {
  if (process.platform === "darwin") {
    return 4;
  }
  if (process.platform === "win32") {
    return 3;
  }
  return 7;
};
let sdkVersion = '';
export const messageSetup = () => {
  // 中转调用融云 im 消息的方法
  ipcRenderer.on('im.real.invoke', async (_, args: IMBridgeInvokeData) => {
    console.log('====>args.action', args.action);
    switch (args.action) {
      case 'imLogin': imLogin(args); break;

      case 'disconnect': imDisconnect(args); break;

      case 'sendMessage': imSendMessage(args); break;
      case 'recallMessage': imRecallMessage(args); break;
      case 'deleteMessage': deleteMessage(args); break;
      case 'deleteMessageFromLocalStorage': deleteMessageFromLocalStorage(args); break;
      case 'deleteConversationAllMessages': deleteConversationAllMessages(args); break;
      case 'sendReadReceiptMessage': markMessagesAsRead(args); break;
      case 'insertMessage': insertMessage(args); break;
      case 'updateMessage': updateMessage(args); break;
      case 'findMsgList': findMsgList(args); break;
      case 'searchLocalMessages': searchLocalMessages(args); break;
      // case 'sendReadReceiptRequest': imSendReadReceiptRequest(args); break;
      // case 'sendReadReceiptResponseV2': imSendReadReceiptResponseV2(args); break;
      case 'getRemoteHistoryMessages': imGetRemoteHistoryMessages(args); break;
      case 'getConversationIDBySessionType': getConversationIDBySessionType(args); break;
      case 'getOneConversationDetail': getOneConversationDetail(args); break;
      case 'getOpenIMConversationList': getOpenIMConversationList(args); break;
      case 'resetUnreadMsgCount': resetUnreadMsgCount(args); break;
      case 'getLoginStatus': getLoginStatus(args); break;
      case 'handleSyncedAllSeqsConversation': handleSyncedAllSeqsConversation(args); break;
      case 'getCurrentServerTime': getCurrentServerTime(args); break;
      case 'networkStatusChanged': networkStatusChanged(args); break;
      case 'getSdkVersion': getSdkVersion(args); break;
      case 'imWindowActive': getImWindowActive(args); break;

      default: {
        console.error('未处理的消息调用', args);
      }
    }
  });
  ipcRenderer.on('im.logout', async () => {
    // 直接调用退出吧,不用等后端logout返回, 后端自己会处理登出情况
    imDisconnect();
    // 刷新窗口
    ipcRenderer.send('im.logout.finish');
  });
};

// openim登录
const tryLogin = async (IMToken, IMUserID, args) => {
  // getPlatform() ?? 5
  if (IMToken && IMUserID) {
    console.log('====>3tryLogin', IMToken, IMUserID, args);
    try {
      // const envName = `./${LynkerSDK.config.env || ''} `
      const start = Date.now()
      const envName = `./`;
      const Env = LynkerSDK.config.env;
     const res = await IMSDK.login({
        userID: IMUserID,
        token: IMToken,
        env: Env,
        appVersion: `${configInfo.version} - ${configInfo.buildNumber}`,
        platformID: 3,
        dataDir: envName,
        apiAddr: getBaseUrl('API_URL'),
        wsAddr: getBaseUrl('WS_URL'),
        isLogStandardOutput: true,
        logLevel: 5,
        // logCloudSync: ['PROD', 'PRE'].includes(Env) ? LogCloudSyncConfig : LogCloudSyncConfigQa
      });
      // logLevel: Env === 'prod' ? 3 : 5,

      retryLogin = 0;
      // 获取sdk版本号
      getSdkVersion();
      console.log('====>4用时imLoginDone3-4', `[${Date.now() - start}]ms`, Date.now());
      setMsgWinRload(0);
      return { code: 0 };
    } catch (error) {
      console.error('====>tryLoginError', error);
      if ((error as WsResponse)?.errCode !== 10102) { // 重复登录
        logHandler({ name: 'IMSDK-login初始化失败, dialogLogout', info: `error:${JSON.stringify(error)};params:${JSON.stringify({ IMToken, IMUserID })};args:${JSON.stringify(args)}`, desc: `tryLogin Error Stack: ${error.stack}` });
       // 失败打回登陆页面
       if ((error as WsResponse)?.errCode === 10005 && getMsgWinRload() != 1) {
        setMsgWinRload(1);
        ipcRenderer.invoke('messageWindow-reload');
       } else {
        showDialog('dialogLogout', { tip: i18nt('im.public.init_error') });
       }

      }
      return error;
    }
  }
};
/**
 * 调用IM登录。
 * 先检测登录状态。catch异常，没返回loginStatus,尝试初始化登录。5次后打回登录。
 * loginStatus不为登录成功和登录中，重新调用登录。
 * @param args
 * @returns
 */
let retryLogin = 0;
let timerDelay = null;
export const imLogin = lodash.throttle(async (args?: IMBridgeInvokeData) => {
  const loginStart = Date.now()
  console.log('====>1用时loginStartTime', loginStart);
  if (timerDelay) {
    clearTimeout(timerDelay);
    timerDelay = null;
  }
  const { action, bridgeId } = args;
  // 登录初始化完成后才走检测登录状态
  if (args?.data.from === 'initLogin') {
    sessionStorage.setItem('imLogin', 'initLogin');
  } else if (sessionStorage.getItem('imLogin') !== 'initLogin') {
    action && bridgeId && replyBridgeRes(action, bridgeId, { code: -1 });
    return;
  }
  try {
    // 检测登录状态
    if (args?.data.from !== 'initLogin') {
      const loginStatus = await IMSDK.getLoginStatus();
      console.log('====>2用时imLogin', loginStatus, loginStatus.data, args,  Date.now());
      if (!loginStatus?.data) {
        logHandler({ name: 'IMSDK-imLogin-Status, dialogLogout', info: `params:${JSON.stringify(args)}；retryLogin:${retryLogin}; loginStatus:${JSON.stringify(loginStatus)}`, desc: `Error Stack: ${error.stack}` });
      } else if ([2, 3].includes(loginStatus.data)) {
        action && bridgeId && replyBridgeRes(action, bridgeId, { code: 0 });
        return { code: 0 };
      }
    }
    const IMToken = getOpenImToken();
    const IMUserID = getOpenid();
    const res = await tryLogin(IMToken, IMUserID, args);
    retryLogin += 1;

    console.log('====>5用时imLoginReply1-5', `[${Date.now() - loginStart}]ms`, Date.now());
    action && bridgeId && replyBridgeRes(action, bridgeId, res);
    logHandler({ name: 'IMSDK-imLogin-res', info: `params:${JSON.stringify(args)}；retryLogin:${retryLogin};`, desc: `` });
    return res;
  } catch (error) {
    console.error('====>imLogin2', error, args, retryLogin);
    logHandler({ name: 'IMSDK-调用imLogin失败, dialogLogout', info: `retryLogin:${retryLogin},error:${JSON.stringify(error)};params:${JSON.stringify(args)}`, desc: `Error Stack: ${error.stack}` });
    // 打回登录
    if (retryLogin > 3) {
      retryLogin = 0;
      action && bridgeId && replyBridgeRes(action, bridgeId, { code: -1 });
      if (error?.[0] === 'w' && error?.[1] === 'a' && getMsgWinRload() != 1) {
        // {"0":"w","1":"a","2":"s","3":"m","4":" ","5":"e","6":"x","7":"i","8":"s","9":"t","10":" ","11":"a","12":"l","13":"r","14":"e","15":"a","16":"d","17":"y","18":",","19":" ","20":"f","21":"a","22":"i","23":"l","24":" ","25":"t","26":"o","27":" ","28":"r","29":"u","30":"n","operationID":"36d6fe84-16ea-43e8-9217-47f65797793d","event":"Getloginstatus"}
        setMsgWinRload(1);
        ipcRenderer.invoke('messageWindow-reload');
      } else {
        showDialog('dialogLogout', { tip: i18nt('im.public.init_error') });
      }

    } else {
      const IMToken = getOpenImToken();
      const IMUserID = getOpenid();
      const res = await tryLogin(IMToken, IMUserID, args);
      action && bridgeId && replyBridgeRes(action, bridgeId, res);
      if (res.code !== 0) {
        timerDelay = setTimeout(() => imLogin(args), 5000);
      }
    }
  }
}, 600);

// 断开连接，退出登录
export const imDisconnect = lodash.throttle(async (args?: IMBridgeInvokeData) => {
  try {
    const res = await IMSDK.logout();
    console.log('====>logout', args, res);
    if (args) {
      replyBridgeRes(args.action, args.bridgeId, res);
    }
  } catch (error) {
    replyBridgeRes(args.action, args.bridgeId, {code: -1});
  }
}, 300);

// 发送消息
// 需要走审核的先插入本地再走审核
// 审核失败更新本地数据状态，返回数据更新ui
// 重发不需要插入本地,失败需要更新发送时间
//
const imSendMessage = async (args: IMBridgeInvokeData) => {
  console.log('====>imSendMessage', args.data);
  const { conversation, options } = args.data;
  const netWork1 = navigator.onLine;
  const message = JSON.parse(args.data.message)
  const extra = message.contentExtra as MessageToSave['contentExtra'];
  const msgType = extra?.contentType;
  console.log('====>imSendMessage2', message, msgType, message.conversationType);
  /**
  * 是否是单聊 单聊true
  */
  const pair: boolean = [1, 6].includes(message.conversationType);
  let insert = null
  let sendData = null
  // 单聊发送消息均走审核（检查关系）, 文件助手不走审核
  // 群聊仅发送图片和文字消息走审核（不需要检查关系）
  // 清零消息仅同步自己，不需要发给对方，不走审核
  // 音视频结束消息不走审核
  if (!['clear_unread', 'meeting_end'].includes(msgType) && message.conversationType !== 6 && ( pair || ['image', 'text', 'richText','emoji_coco_image'].includes(msgType))) {
    const obj = { code: 1, urls: [], text: '', from: extra?.senderId ?? '', to: extra?.receiverId ?? '', pair };
    if (msgType === 'image') {
      obj.urls = [extra.data.imgUrl];
      obj.code = 0;
    } else if (['text', 'richText', 'emoji_coco_image'].includes(msgType)) {
      if(!message.isResend) { // 不是重发需要插入消息
        message.status = -1
        try {
          insert = await insertMessageToLocalStorage(message, extra, options, pair, 'sendTextMessage')
          console.log('===>insert', insert);
          
        } catch (error) {
          replyBridgeRes(args.action, args.bridgeId, { code: 30003, msg: "网络异常，请稍后重试", data: message });
          logHandler({ name: '消息发送失败-insertMessageToLocalStorage', info: JSON.stringify(error), desc:'insertMessageToLocalStorage失败' });
          return false;
        }
      }
      obj.text = extra.data?.text;
    }
    sendData = !insert?.sendData ? await getMessageData(message, extra, options, pair) : insert.sendData
    // 统一走自己的审核，不走融云的审核
    try {
      // 错误在 catch 中处理
      const res = await imReviewApiV2(obj);

      //  // 单聊免打扰通知发送静默消息
      //  if(res?.data?.not_disturb && pair){
      //   options.disableNotification = true
      //  }
      if (res?.data && !res.data.contact) { // 不在谁可联系我的许可范围内/
        sendData.message.status = 3;
        updateMessage({data:sendData})
        replyBridgeRes(args.action, args.bridgeId, { code: 405, msg: "不在对方联系范围内，消息发送失败", data: sendData.message });
        return false;
      }
    } catch (error) {
      const res = error?.response;
      console.error('====>message_review', error, res);
      // 更新insert数据状态
      sendData.message.status = 3;
      updateMessage({data:sendData})

      if (res) {
        if (res.status === 418) {
          replyBridgeRes(args.action, args.bridgeId, { code: 21501, msg: "你输入的信息包含敏感内容，请修改后重试", data: sendData.message });

        } else if (res.status === 400 && res?.data?.reason === 'COMMON_BLACK_LIST') {
          replyBridgeRes(args.action, args.bridgeId, { code: 405, msg: "已被对方加入黑名单，消息发送失败", data: sendData.message });

        } else {
          replyBridgeRes(args.action, args.bridgeId, { code: 30003, msg: "消息发送失败", data: sendData.message });
        }
      } else {
        replyBridgeRes(args.action, args.bridgeId, { code: 30003, msg: "消息发送失败", data: sendData.message });
      }
      const info = `network:${netWork1}; error:${JSON.stringify(error)};sendMessage-conversation:${JSON.stringify(conversation)};`;
      const desc = `invokelmEvent>imReviewApiV2;action:${args.action}`;
      logHandler({ name: 'im-审核', info, desc });
      return false;
    }
  }
  try {
    if(!sendData) {
      sendData = await getMessageData(message, extra, options, pair)
    }
    // 创建自定义消息结构
    const res = await sendTextMsg(sendData);
    const { data: successMessage } = res;
    replyBridgeRes(args.action, args.bridgeId, { data: successMessage as MessageItem, code: successMessage.status || res.errCode, msg: {} });

  } catch (error) {
    console.log('===>sendData', sendData);

    sendData.message.status = 3;
    updateMessage({data:sendData})
    const netWork = navigator.onLine;
    console.error('====>imSendMessageError', error);
    replyBridgeRes(args.action, args.bridgeId, { code: 10001, msg: "网络异常，消息发送失败", data: sendData.message });
    // 已经退出登录
    if (error.errCode === 10101) {
     imLogin({ action: "", data: { from: 'sendTextMessageE', errCode: error.errCode } });
   }
    const info = `netWork1:${netWork1}, network2:${netWork}, error:${JSON.stringify(error)};sendMessage-conversation:${JSON.stringify(conversation)};`;
    const desc = `invokelmEvent>imSendMessage();action:${args.action}`;
    logHandler({ name: 'im-消息发送失败', info, desc });
  }
};
/**
 * 发送自定义文本消息
 * @param message
 * @param extra
 * @param options
 * @param pair
 * @returns
 */
export const sendTextMessage = async (message: MessageToSave, extra: MessageToSave['contentExtra'], options, pair) => {
  const sendData = await getMessageData(message, extra, options, pair);
  sendTextMsg(sendData)
}

/**
 * 消息自定义文本发送
 * @param sendData
 * @returns
 */
const sendTextMsg = async (sendData) => {
  let res = null;
  console.log('=====>sendMessageParam', sendData);
  // 消息自定义文本发送
  try {
    res = await IMSDK.sendMessage(sendData);
    console.log('====>sendMessageres', res);
    logHandler({ name: 'im-发送消息', info: `res:${JSON.stringify(res)};`, desc: `sendID:${sendData?.sendID}; ${new Date()}; 发送自定义文本消息` });
    return res;
  } catch (error) {
    console.error('=====>sendTextMessageE', error);
    logHandler({ name: 'im-发送消息失败sendText', info: `error:${JSON.stringify(error)};params:${JSON.stringify(sendData)}`, desc: `；sendTextMessageERROR` });
    return error;
  }

};

/** *
 * 本地插入消息
 */
export const insertMessage = async (args: IMBridgeInvokeData) => {
  const { conversation, options } = args.data;
  const message = JSON.parse(args.data.message)

  try {
    const extra = message.contentExtra as MessageToSave['contentExtra'];
    const pair: boolean = [1, 6].includes(message.conversationType);
    const { res } = await insertMessageToLocalStorage(message, extra, options, pair, 'insertMessage')
    replyBridgeRes(args.action, args.bridgeId, { data: res.data as MessageItem, code: 0, msg: {} });
  } catch (error) {
    console.error('=====>insertMessageErr', error);
    replyBridgeRes(args.action, args.bridgeId, { data: {}, code: -1, msg: {} });
    logHandler({ name: 'im-本地插入消息失败', info: `error:${JSON.stringify(error)};params:${message}`, desc: `insertMessageErr` });

  }
};

const insertMessageToLocalStorage = async(message, extra, options, pair, from) => {
  const sendData = await getMessageData(message, extra, options, pair);
  const insertData = { ...sendData, sendStatus: message.status };
  console.log('====>insertData1', insertData,  from, sendData);
  let res = null
  if (pair) {
    res = await IMSDK.insertSingleMessageToLocalStorage(insertData);
  } else {
    res = await IMSDK.insertGroupMessageToLocalStorage(insertData);
  }
  console.log('====>insertData2', res);
  return { res, sendData}
}

/**
 * 更新本地消息
 * @param args
 */
export const updateMessage = async (args) => {
  console.log('===>updateMessage', args);

  const dataMsg = typeof args.data.message === 'string' ? JSON.parse(args.data.message) : args.data.message
  const { recvID, sendID, ...message } = dataMsg;
  if(message.textElem && !message.textElem.ex){
    message.textElem.ex = message.ex;
  }
  const params = {
    message,
    clientMsgID: message.clientMsgID,
    recvID,
    sendID,
    sessionType: message.sessionType,
  };
  try {
    const res = await IMSDK.updateMessageToLocalStorage(params);
    args.action && replyBridgeRes(args.action, args.bridgeId, { data: {}, code: 0 });
    console.log('=====>updateMessageToLocalStorage', res, params);
  } catch (error) {
    console.error('=====>updateMessageE', error, message, params);
    args.action && replyBridgeRes(args.action, args.bridgeId, { data: {}, code: -1 });
  }
};
/**
 * 构建消息结构体
 * @param message
 * @param extra
 * @param options
 * @param pair boolean 是否是单聊 单聊true
 * @returns
 */
export const getMessageData = async (message: MessageToSave, extra: MessageToSave['contentExtra'], options, pair) => {
  try {
    const { targetId, openImId, conversationID } = message;
    const sendID = openImId || message.sendID;
    const contentText = extra.data?.text || extra.data?.fileName || '';
    let msgData: MessageItem;
    const CustomMsgImContent: CustomMessage = {
      extra: JSON.stringify({
        senderId:extra.senderId,
        receiverId:extra.receiverId,
        data:extra.data,
        source:extra.source,
        contentType: extra.contentType,
      })
    };
    const CustomMsgImContentString = JSON.stringify(CustomMsgImContent);
    if (message.messageType === 114) {
      // 引用消息
      msgData = await setQuoteMessage(message, contentText);
    } else {
      // 文本消息
      const { data } = await IMSDK.createTextMessage(contentText);
      msgData = data;
    }
    // 判断link 搜索 type
    let type = message.contentExtra?.contentType;
    if (type === 'text') {
      const urlRegex = /^https?:\/\//;
      type = urlRegex.test(contentText) ? 'link' : type;
    }
    msgData.ex = CustomMsgImContentString;
    msgData.subContentType = type;
    msgData.senderNickname = extra.senderNickname;
    msgData.senderFaceUrl = extra.senderFaceUrl;
    msgData.recvID = targetId,
    msgData.sendID = sendID
    msgData.sessionType = message.conversationType === 6 ? 6 : pair ? 1 : 3
    // 有clientMsgID，sdk数据库已经有这条消息了，发送更新这条消息，
    // 因为发送接口不会更新ex里面的数据
    // 而且只有失败状态才能发送
    if (message.clientMsgID) {
      msgData.clientMsgID = message.clientMsgID;
      msgData.createTime = message.createTime;
    }
    const sendData: SendMsgParams = {
      recvID: pair ? targetId : '',
      sendID,
      message: msgData,
      groupID: !pair ? targetId : '',
      offlinePushInfo: options?.offlinePushInfo,
    };
    console.log('=====>getMessageData', msgData, sendData);
    return sendData;
  } catch (error) {
    logHandler({ name: 'im-发送消息失败getMessageData', info: `error:${JSON.stringify(error)};params:${JSON.stringify(message)}`, desc: `getMessageDataERR` });
    throw error;
  }
};

/**
 * 发送引用消息结构体
 * @param message
 * @returns
 */
export const setQuoteMessage = async (message: MessageToSave, text) => {
  const { conversationID } = message;
  const msg = await IMSDK.findMessageList([
    {
      conversationID,
      clientMsgIDList: [message.content.referMsgUid],
    },
  ]);
  const quoteMsg = msg.data?.findResultItems[0].messageList[0];

  const { data } = await IMSDK.createQuoteMessage({
    text,
    message: JSON.stringify(quoteMsg)
  });
  return data;
};

/**
 * 查询本地消息
 */
const findMsgList = async (args: IMBridgeInvokeData) => {
  const { conversationID, messageUIds } = args.data.message;
  try {
    const msg = await IMSDK.findMessageList([
      {
        conversationID,
        clientMsgIDList: messageUIds
      },
    ]);
    const result = msg.data?.findResultItems;
    replyBridgeRes(args.action, args.bridgeId, { code: 0, result });
  } catch (error) {
    replyBridgeRes(args.action, args.bridgeId, { code: -1 });
  }

};
/**
 * 搜索本地消息
 */
const searchLocalMessages = async (args: IMBridgeInvokeData) => {
  const searchParams = args.data.searchParams as SearchLocalParams;
  try {
    const result = await IMSDK.searchLocalMessages(searchParams);
    console.log('=====>search', result, searchParams);
    replyBridgeRes(args.action, args.bridgeId, { code: 0, result });
  } catch (error) {
    console.error('=====>searchER', error, searchParams);
    replyBridgeRes(args.action, args.bridgeId, { code: -1 });
    logHandler({ name: 'im-搜索本地消息出错', info: `error:${JSON.stringify(error)};params:${searchParams}`, desc: `searchLocalMessagesE` });
  }

};
// 获取历史会话
const imGetRemoteHistoryMessages = async (args: IMBridgeInvokeData) => {
  const { options } = args.data;
  const time = Date.now();
  try {
    const res = await IMSDK.getAdvancedHistoryMessageList({
    ...options,
    } as GetAdvancedHistoryMsgParams);
    replyBridgeRes(args.action, args.bridgeId, { conversationID: options.conversationID, ...res.data });
    console.log('=====>getAdvancedHistoryMessageList', [Date.now() - time], options, res);
  } catch (error) {
    logHandler({ name: 'im-获取历史会话错误', info: `error:${JSON.stringify(error)};params:${JSON.stringify(args.data)}`, desc: `` });
    replyBridgeRes(args.action, args.bridgeId, { errCode: error.errCode });
    console.error('====>getRemoteHistoryMessagesErr', error, args);
    // 已经退出登录
    // if (error.errCode === 10101) {
      imLogin({ action: "", data: { from: 'getAdvancedHistoryMessageList', errCode: error.errCode } });
    // }
  }
};
const markMessagesAsRead = async (args: IMBridgeInvokeData) => {
  const { data } = args;
  try {
    console.log('=====>markMessagesAsReadByMsgID', data);
    const res = await IMSDK.markMessagesAsReadByMsgID(data);
    replyBridgeRes(args.action, args.bridgeId, { code: 0 });
  } catch (error) {
    console.error('=====>markMessagesAsReadByMsgIDE', data, error);
    replyBridgeRes(args.action, args.bridgeId, { code: -1, ...error });
    logHandler({ name: 'im-标记消息已读出错', info: `error:${JSON.stringify(error)};params:${data}`, desc: `markMessagesAsReadByMsgIDE` });

    // feedbackToast({ error });
  }
};
const resetUnreadMsgCount = async (args: IMBridgeInvokeData) => {
  const { data } = args;
  try {
    // const res = await IMSDK.markMessagesAsReadByMsgID(data);
    // 后端说改成这个
    const res = await IMSDK.resetUnreadMsgCount(data);
    console.log('=====>resetUnreadMsgCount', res, args);
    replyBridgeRes(args.action, args.bridgeId, { code: 0 });
  } catch (error) {
    replyBridgeRes(args.action, args.bridgeId, { code: -1 });
    console.error('=====>resetUnreadMsgCountE', data, error);
    // feedbackToast({ error });
  }
};

/**
 *  设置会话活跃，离线消息全量推送。用在选择当前会话，和离开当前会话
 * @param args {data:{conversationID, oldConversationID?}}
 */
const handleSyncedAllSeqsConversation = async (args: IMBridgeInvokeData) => {
  const { data } = args;
  try {
    if (data.oldConversationID) {
      const re = await IMSDK.handleSyncedAllSeqsConversation({ conversationID: data.oldConversationID, handleFlag: 0 });
      console.log('=====>handleSyncedAllSeqsConversationO', re, { conversationID: data.oldConversationID, handleFlag: 0 });
    }
   const res = await IMSDK.handleSyncedAllSeqsConversation({ conversationID: data.conversationID ?? "", handleFlag: 1 });
    replyBridgeRes(args.action, args.bridgeId, { code: 0 });
    console.log('=====>handleSyncedAllSeqsConversation', res, { conversationID: data.conversationID ?? "", handleFlag: 1 });

  } catch (error) {
    replyBridgeRes(args.action, args.bridgeId, { code: -1 });
    console.error('=====>handleSyncedAllSeqsConversationE', data, error);
  }
};

// 获取指定会话信息
const getOneConversation = async (conversation) => {
  try {
    const { data } = await IMSDK.getOneConversation({
      sourceID: conversation.targetId,
      sessionType: conversation.conversationType,
      ownerUserID: conversation.myOpenImId,
    });
    return data;
  } catch (error) {
    // 调用失败
    console.error('=====>getOneConversationERR', error, conversation);
    // 已经退出登录
    // if (error.errCode === 10101) {
      imLogin({ action: "", data: { from: 'getOneConversationERR', errCode: error.errCode } });
    // }
    logHandler({ name: 'im-获取会话错误', info: `error:${JSON.stringify(error)};params:${JSON.stringify(conversation)}`, desc: `getOneConversationERR` });

  }
};
// 获取指定会话ID
const getConversationIDBySessionType = async (args) => {
  const { conversation } = args.data;
  try {
    const res = await IMSDK.getConversationIDBySessionType({
      sourceID: conversation.targetId,
      sessionType: conversation.conversationType,
      ownerUserID: conversation.myOpenImId,
    });
    replyBridgeRes(args.action, args.bridgeId, { code: 0, data: res.data });

    console.log('=====>getConversationIDBySessionType', {
      sourceID: conversation.targetId,
      sessionType: conversation.conversationType,
      ownerUserID: conversation.myOpenImId,
    }, res);
    return res;
  } catch (error) {
    // 调用失败
    replyBridgeRes(args.action, args.bridgeId, { code: -1 });
    console.error('=====>getConversationIDBySessionType', {
      sourceID: conversation.targetId,
      sessionType: conversation.conversationType,
      ownerUserID: conversation.myOpenImId,
    }, error);
    const info = `error:${JSON.stringify(error)};conversation:${JSON.stringify(conversation)}`;
    const desc = ``;
    logHandler({ name: 'im-获取指定会话ID出错', info, desc });
  }
};
// 获取指定会话
const getOneConversationDetail = async (args) => {
  const { conversation } = args.data;
  const data = await getOneConversation(conversation);
  console.log('=====>getOneConversation', conversation, data);
  if (data) {
    replyBridgeRes(args.action, args.bridgeId, { code: 0, data });
  } else {
    replyBridgeRes(args.action, args.bridgeId, { code: -1, data: {} });

  }
};

// 获取会话列表
const getOpenIMConversationList = async (args) => {
  try {
    const { data } = await IMSDK.getAllConversationList();

    replyBridgeRes(args.action, args.bridgeId, { code: 0, data });
    // console.log(`====>getAllConversationList`, args, data);

    return data || [];
  } catch (error) {
    // 调用失败
    replyBridgeRes(args.action, args.bridgeId, { code: -1 });
    if ([100, 10005].includes(error.errCode) && getMsgWinRload() != 1) {
      setMsgWinRload(1);
      ipcRenderer.invoke('messageWindow-reload');
    }
    console.error('=====>getAllConversationList', args, error);
    const info = `error:${JSON.stringify(error)};args:${JSON.stringify(args)}`;
    const desc = ``;
    logHandler({ name: 'im-获取会话列表出错', info, desc });
    return [];
  }
};
// 撤回消息
const imRecallMessage = async (args: IMBridgeInvokeData) => {
  try {
    /**
     * conversationID: string;
       clientMsgID: string;
     */
    const res = await IMSDK.revokeMessage(args.data);
    console.log('=====>imRecallMessage', args.data, res);
    replyBridgeRes(args.action, args.bridgeId, { code: 0, ...res });
  } catch (error) {
    // 调用失败
    console.error('=====>撤回消息', error);
    replyBridgeRes(args.action, args.bridgeId, { code: error.errCode });
    const info = `error:${JSON.stringify(error)};revokeMessage:${JSON.stringify(args.data)}`;
    const desc = `;action:${args.action}`;
    logHandler({ name: 'im-撤回消息失败', info, desc });
  }
};
// 删除消息
const deleteMessage = async (args: IMBridgeInvokeData) => {
  try {
    /**
    * conversationID: string;
      clientMsgID: string;
    */
    const res = await IMSDK.deleteMessage(args.data);
    console.log('=====>deleteMessage', res);
    replyBridgeRes(args.action, args.bridgeId, { code: 0, ...res });

    const info = `data:${JSON.stringify(args.data)}`;
    const desc = `res:${JSON.stringify(res)};action:${args.action}`;
    logHandler({ name: 'im-删除消息', info, desc });
  } catch (error) {
    // 调用失败
    console.error('=====>deleteMessageERR', error);
    replyBridgeRes(args.action, args.bridgeId, { code: error.errCode });
    const info = `error:${JSON.stringify(error)};deleteMessage:${JSON.stringify(args.data)}`;
    const desc = `;action:${args.action}`;
    logHandler({ name: 'im-删除消息失败', info, desc });
  }
};
// 删除本地消息
const deleteMessageFromLocalStorage = async (args: IMBridgeInvokeData) => {
  try {
    /**
    * conversationID: string;
      clientMsgID: string;
    */
    const res = await IMSDK.deleteMessageFromLocalStorage(args.data);
    console.log('=====>deleteMessageFromLocalStorage',args.data, res);
    replyBridgeRes(args.action, args.bridgeId, { code: 0, ...res });

    const info = `data:${JSON.stringify(args.data)}`;
    const desc = `res:${JSON.stringify(res)};action:${args.action}`;
    logHandler({ name: 'im-删除本地消息', info, desc });
  } catch (error) {
    // 调用失败
    console.error('=====>deleteMessageFromLocalStorageERR', error);
    replyBridgeRes(args.action, args.bridgeId, { code: error.errCode });
    const info = `error:${JSON.stringify(error)};deleteMessage:${JSON.stringify(args.data)}`;
    const desc = `;action:${args.action}`;
    logHandler({ name: 'im-删除消息本地失败', info, desc });
  }
};

// 获取登录状态
const getLoginStatus = async (args) => {
 const res = await IMSDK.getLoginStatus();
 replyBridgeRes(args.action, args.bridgeId, { code: 0, ...res });
};

const getCurrentServerTime = async (args) => {
  try {
    const res = await IMSDK.getCurrentServerTime();
    console.log('===>Date.now()', res.data, Date.now());
    const diff = res.data - Date.now();
    replyBridgeRes(args.action, args.bridgeId, { code: 0, data:res.data, diff });
  } catch (error) {
    console.error('====>getCurrentServerTimeError', error);
    replyBridgeRes(args.action, args.bridgeId, { code: -1 });
  }
};

// 清除会话历史
const deleteConversationAllMessages = async (args) => {
  try {
    console.log('====>deleteConversationAndDeleteAllMsg', args);
    const res = await IMSDK.deleteConversationAndDeleteAllMsg(args.data.conversationID, args.data.bindUserID);
    console.log('====>deleteConversationAndDeleteAllMsgres', res);
    replyBridgeRes(args.action, args.bridgeId, { code: 0, ...res });
  } catch (error) {
    console.error('====>deleteConversationAndDeleteAllMsgError', error, args);
    replyBridgeRes(args.action, args.bridgeId, { code: -1 });
  }
};

const networkStatusChanged = (args?) => {
  try {
   IMSDK.networkStatusChanged();
   replyBridgeRes(args.action, args.bridgeId, { code: 0 });
  } catch (error) {
    replyBridgeRes(args.action, args.bridgeId, { code: -1 });
    console.error('====>networkStatusChangedError', error);
  }
};

const getSdkVersion = async (args?) => {
  try {
    const version = await IMSDK.getSdkVersion();
    sdkVersion = version?.data;
    const appVersion = `${configInfo.version} - ${configInfo.buildNumber}`;
    setAppVersion({ sdkVersion: version?.data, appVersion });
    args?.action && replyBridgeRes(args.action, args.bridgeId, { code: 0 });
    console.log('====>getSdkVersion', version?.data);
  } catch (error) {
    replyBridgeRes(args.action, args.bridgeId, { code: -1 });
    console.error('====>getSdkVersionError', error);
  }
};
const getImWindowActive = async (args?) => {
  args?.action && replyBridgeRes(args.action, args.bridgeId, { code: 0, active: true });
};