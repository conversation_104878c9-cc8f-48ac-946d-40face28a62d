<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import to from 'await-to-js';
import { useI18n } from 'vue-i18n';
import vueQr from 'vue-qr/src/packages/vue-qr.vue';
import LynkerSDK from '@renderer/_jssdk';
import logoSrc from '@renderer/assets/account/logo_img_logo.svg';
import { MessagePlugin } from 'tdesign-vue-next';
import { cmToPx, convertBase64ToDataUrl } from '@/views/square/utils';
import { useSquareStore } from '@/views/square/store/square';
import { QRCodeURL } from '../../../../api/square/post';

const { t } = useI18n();
const store = useSquareStore();
const visible = ref(false);

const beginDownload = ref(false);
const isDownloading = ref(false); // 防止重复下载的状态
const hasProcessed = ref(false); // 防止重复处理 onSuccess 的标志

const width = ref(64);
const margin = computed(() => Math.round(width.value * 0.03));
const logoMargin = computed(() => Math.round(width.value * 0.01));
const qrCodeUrl = ref('');
const columns = ref([
  { colKey: 'width', title: t('square.qrcode.width'), align: 'center' },
  { colKey: 'advice', title: t('square.qrcode.adviceDistance'), align: 'center' },
  { colKey: 'operate', title: t('square.operate'), align: 'center' },
]);
const tableData = ref([
  { width: '8cm', advice: '0.5m' },
  { width: '12cm', advice: '0.8m' },
  { width: '15cm', advice: '1m' },
  { width: '30cm', advice: '1.5m' },
  { width: '50cm', advice: '2.5m' },
]);

const download = async (row) => {
  // 防止重复点击
  if (beginDownload.value || isDownloading.value) return;

  beginDownload.value = true;
  isDownloading.value = true; // 设置下载状态
  hasProcessed.value = false; // 重置处理标志

  width.value = cmToPx(parseInt(row.width, 10));
  await nextTick();
  const [err, res] = await to(QRCodeURL({ qr_token: store.squareInfo.promoteQrToken }));
  if (err) {
    beginDownload.value = false;
    isDownloading.value = false;
    return;
  }
  qrCodeUrl.value = res.data.url;
};

const open = () => {
  visible.value = true;
};

const onSuccess = async (base64) => {
  if (!isDownloading.value || hasProcessed.value) return;
  hasProcessed.value = true; // 标记已处理

  try {
    const url = await convertBase64ToDataUrl(base64, width.value, width.value);
    const result = await LynkerSDK.downloadFile({
      url,
      title: 'qrcode.png',
    });

    result && MessagePlugin.success('下载成功');
  } catch (error) {
    console.error('下载文件失败:', error);
  } finally {
    beginDownload.value = false;
    isDownloading.value = false; // 重置下载状态
  }
};

defineExpose({
  open,
});
</script>

<template>
  <t-dialog
    v-model:visible="visible"
    width="600"
    attach="body"
    :header="$t('square.qrcode.qrcodeDownload')"
    :footer="null"
    prevent-scroll-through
    class="square-d-qrcode-download"
  >
    <template #closeBtn>
      <iconpark-icon name="iconerror" class="text-24 text-#1A2139 cursor-pointer" @click="visible = false" />
    </template>

    <t-table row-key="id" :data="tableData" :columns="columns">
      <template #operate="{ row }">
        <t-link theme="primary" hover="color" @click="download(row)">
          <t-icon name="download" /> {{ $t('square.action.download') }}
        </t-link>
      </template>
    </t-table>

    <p class="mt-16 text-center">{{ $t('square.qrcode.qrcodeSizeAdvice') }}</p>

    <vue-qr
      v-if="beginDownload"
      :key="`qr-${width}-${qrCodeUrl}`"
      class="hidden"
      :margin="margin"
      :size="width"
      color-dark="#000000"
      :logo-src="logoSrc"
      :logo-margin="logoMargin"
      :text="qrCodeUrl"
      :callback="onSuccess"
    />
  </t-dialog>
</template>

<style lang="less">
.square-d-qrcode-download {
  .t-dialog {
    padding: 24px;
    .t-dialog__body {
      padding-top: 24px;
      padding-bottom: 0;
    }
  }
}
</style>

<style lang="less" scoped>
.hidden {
  display: none;
  opacity: 0;
  z-index: -1;
  pointer-events: none;
  position: absolute;
}
</style>
