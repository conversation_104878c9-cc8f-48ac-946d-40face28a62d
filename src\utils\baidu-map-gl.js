/**
 * Skipped minification because the original files appears to be already minified.
 * Original file: /npm/vue3-baidu-map-gl@2.6.5/dist/index.prod.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("vue")):"function"==typeof define&&define.amd?define(["exports","vue"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).Vue3baiduMapGl={},e.Vue)}(this,(function(e,t){"use strict";const o={all:n=n||new Map,on:function(e,t){var o=n.get(e);o?o.push(t):n.set(e,[t])},off:function(e,t){var o=n.get(e);o&&(t?o.splice(o.indexOf(t)>>>0,1):n.set(e,[]))},emit:function(e,t){var o=n.get(e);o&&o.slice().map((function(e){e(t)})),(o=n.get("*"))&&o.slice().map((function(o){o(e,t)}))}};var n;function a(){return o}const i=()=>{},l={};function r({key:e,src:t,addCalToWindow:o=!1,exportGetter:n=i}){let a;try{a=n()}catch(e){}if(l[e]||a){if(void 0!==a)return Promise.resolve({[e]:a})}else l[e]=new Promise(((a,i)=>{const l=document.createElement("script"),r=()=>{a({[e]:n()}),window.document.body.removeChild(l)};o?window[e]=r:l.onload=function(){this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState||(l.onload=null,r())},l.src=t,l.type="text/javascript",l.defer=!0,l.onerror=i,document.body.appendChild(l)}));return l[e]}const s="https://unpkg.com/mapvgl/dist/mapvgl.min.js",c="https://unpkg.com/mapvgl/dist/mapvgl.threelayers.min.js",u="https://unpkg.com/mapv/build/mapv.min.js",d="https://unpkg.com/mapv-three/dist/mapvthree.umd.js",p="https://mapopen.bj.bcebos.com/github/BMapGLLib/TrackAnimation/src/TrackAnimation.min.js",f="https://mapopen.bj.bcebos.com/github/BMapGLLib/DrawingManager/src/DrawingManager.min.js",m="https://mapopen.bj.bcebos.com/github/BMapGLLib/DistanceTool/src/DistanceTool.min.js",g="https://mapopen.bj.bcebos.com/github/BMapGLLib/GeoUtils/src/GeoUtils.min.js",y="https://mapopen.bj.bcebos.com/github/BMapGLLib/AreaRestriction/src/AreaRestriction.min.js",v="https://mapopen.bj.bcebos.com/github/BMapGLLib/InfoBox/src/InfoBox.min.js",h="https://bj.bcebos.com/v1/mapopen/github/BMapGLLib/RichMarker/src/RichMarker.min.js",b="https://bj.bcebos.com/v1/mapopen/github/BMapGLLib/Lushu/src/Lushu.min.js",w={TrackAnimation:e=>r({src:e||p,key:"trackAnimation",exportGetter:()=>window.BMapGLLib.TrackAnimation}),DrawingManager:e=>r({src:e||f,key:"DrawingManager",exportGetter:()=>window.BMapGLLib.DrawingManager}),DistanceTool:e=>r({src:e||m,key:"DistanceTool",exportGetter:()=>window.BMapGLLib.DistanceTool}),GeoUtils:e=>r({src:e||g,key:"GeoUtils",exportGetter:()=>window.BMapGLLib.GeoUtils}),RichMarker:e=>r({src:e||h,key:"RichMarker",exportGetter:()=>window.BMapGLLib.RichMarker}),AreaRestriction:e=>r({src:e||y,key:"AreaRestriction",exportGetter:()=>window.BMapGLLib.AreaRestriction}),InfoBox:e=>r({src:e||v,key:"InfoBox",exportGetter:()=>window.BMapGLLib.InfoBox}),LuShu:e=>r({src:e||b,key:"LuShu",exportGetter:()=>window.BMapGLLib.LuShu}),Mapvgl:e=>r({src:e||s,key:"Mapvgl",exportGetter:()=>window.mapvgl}),MapvglThreeLayers:e=>r({src:e||c,key:"MapvglThreeLayers",exportGetter:()=>window.mapvglThreeLayers}),Mapv:e=>r({src:e||u,key:"Mapv",exportGetter:()=>window.mapv}),MapvThree:e=>r({src:e||d,key:"MapvThree",exportGetter:()=>window.mapvthree})};function C(e,t,o){for(const n of Object.keys(e))if(/^on/.test(n)&&e[n]){const e=n.replace(/^on/,"").toLocaleLowerCase();o.addEventListener(e,(o=>{var n,a;o.preventDefault=o.preventDefault||(null===(n=o.domEvent)||void 0===n?void 0:n.preventDefault.bind(o.domEvent)),o.stopPropagation=o.stopPropagation||(null===(a=o.domEvent)||void 0===a?void 0:a.stopPropagation.bind(o.domEvent)),t(e,o)}))}}new Set;function M(e,t){console.warn(`[Vue3 BaiduMap GL/${e}]: ${t}`)}function B(e,t){console.error(`[Vue3 BaiduMap GL/${e}]: ${t}`)}const k="undefined"!=typeof window,O=e=>void 0!==e;function L(e){return"[object String]"===Object.prototype.toString.call(e)}function S(e){return"[object Array]"===Object.prototype.toString.call(e)}var G;function I(e){return e.map((({lng:e,lat:t})=>new BMapGL.Point(e,t)))}function P({lng:e,lat:t}){return new BMapGL.Point(e,t)}e.DistrictType=void 0,(G=e.DistrictType||(e.DistrictType={}))[G.PROVINCE=0]="PROVINCE",G[G.CITY=1]="CITY",G[G.AREA=2]="AREA";let _=0;function T(e,t,o){return n=>e()?t(n):o(n)}function A(e){return(t,o)=>{(t===o||t!==o&&JSON.stringify(t)!==JSON.stringify(o))&&e(t)}}const x={1:"PLAYING",2:"INITIAL",3:"STOPPING"};const D={0:"BMAP_STATUS_SUCCESS",8:"ERR_POSITION_TIMEOUT",2:"ERR_POSITION_UNAVAILABLE",6:"ERR_PERMISSION_DENIED"};function R(e,t,o){return new Promise((n=>{e.getPoint(t,(e=>n(e||null)),o)}))}function z(e,t,o){return new Promise((n=>{e.getLocation(new BMapGL.Point(t.lng,t.lat),(e=>{n(e||null)}),o)}))}var E,N;e.CoordinatesFromType=void 0,(E=e.CoordinatesFromType||(e.CoordinatesFromType={}))[E.COORDINATES_WGS84=1]="COORDINATES_WGS84",E[E.COORDINATES_WGS84_MC=2]="COORDINATES_WGS84_MC",E[E.COORDINATES_GCJ02=3]="COORDINATES_GCJ02",E[E.COORDINATES_GCJ02_MC=4]="COORDINATES_GCJ02_MC",E[E.COORDINATES_BD09=5]="COORDINATES_BD09",E[E.COORDINATES_BD09_MC=6]="COORDINATES_BD09_MC",E[E.COORDINATES_MAPBAR=7]="COORDINATES_MAPBAR",E[E.COORDINATES_51=8]="COORDINATES_51",e.CoordinatesToType=void 0,(N=e.CoordinatesToType||(e.CoordinatesToType={}))[N.COORDINATES_GCJ02=3]="COORDINATES_GCJ02",N[N.COORDINATES_BD09=5]="COORDINATES_BD09",N[N.COORDINATES_BD09_MC=6]="COORDINATES_BD09_MC";let j=null;function W(){if(null!==j)return j;const e="//mapopen.bj.bcebos.com/cms/react-bmap/markers_new2x_fbb9e99.png";j={simple_red:new BMapGL.Icon(e,new BMapGL.Size(21,33),{imageOffset:new BMapGL.Size(227,189),imageSize:new BMapGL.Size(300,300)}),simple_blue:new BMapGL.Icon(e,new BMapGL.Size(21,33),{imageOffset:new BMapGL.Size(227,225),imageSize:new BMapGL.Size(300,300)}),loc_red:new BMapGL.Icon(e,new BMapGL.Size(23,35),{imageOffset:new BMapGL.Size(200,189),imageSize:new BMapGL.Size(300,300)}),loc_blue:new BMapGL.Icon(e,new BMapGL.Size(23,35),{imageOffset:new BMapGL.Size(200,225),imageSize:new BMapGL.Size(300,300)}),start:new BMapGL.Icon(e,new BMapGL.Size(25,40),{imageOffset:new BMapGL.Size(200,139),imageSize:new BMapGL.Size(300,300)}),end:new BMapGL.Icon(e,new BMapGL.Size(25,40),{imageOffset:new BMapGL.Size(225,139),imageSize:new BMapGL.Size(300,300)}),location:new BMapGL.Icon(e,new BMapGL.Size(14,20),{imageOffset:new BMapGL.Size(124,233),imageSize:new BMapGL.Size(300,300)})};for(let t=1;t<=10;t++)j["red"+t]=new BMapGL.Icon(e,new BMapGL.Size(21,33),{imageOffset:new BMapGL.Size(21*(t-1),0),imageSize:new BMapGL.Size(300,300)});for(let t=1;t<=10;t++)j["blue"+t]=new BMapGL.Icon(e,new BMapGL.Size(21,33),{imageOffset:new BMapGL.Size(21*(t-1),66),imageSize:new BMapGL.Size(300,300)});return j}function $(){const e=""+_++;return t.provide("parentComponentId",e),e}const Z=["id"];var F=t.defineComponent(Object.assign({name:"BMap"},{__name:"index",props:{ak:{},apiUrl:{},width:{default:"100%"},height:{default:"550px"},center:{default:()=>({lat:39.915185,lng:116.403901})},mapType:{default:"BMAP_NORMAL_MAP"},zoom:{default:14},heading:{default:0},tilt:{default:0},minZoom:{default:0},maxZoom:{default:21},noAnimation:{type:Boolean,default:!1},mapStyleId:{},mapStyleJson:{},showControls:{type:Boolean,default:!1},plugins:{},pluginsSourceLink:{},displayOptions:{},restrictCenter:{type:Boolean,default:!0},enableTraffic:{type:Boolean,default:!1},enableDragging:{type:Boolean,default:!0},enableInertialDragging:{type:Boolean,default:!0},enableScrollWheelZoom:{type:Boolean,default:!1},enableContinuousZoom:{type:Boolean,default:!0},enableResizeOnCenter:{type:Boolean,default:!0},enableDoubleClickZoom:{type:Boolean,default:!1},enableKeyboard:{type:Boolean,default:!0},enablePinchToZoom:{type:Boolean,default:!0},enableAutoResize:{type:Boolean,default:!0},enableIconClick:{type:Boolean},loadingBgColor:{default:"#f1f1f1"},loadingTextColor:{default:"#999"},backgroundColor:{},onClick:{},onDblclick:{},onRightclick:{},onRightdblclick:{},onMaptypechange:{},onMousemove:{},onMouseover:{},onMouseout:{},onMovestart:{},onMoving:{},onMoveend:{},onZoomstart:{},onZoomend:{},onAddoverlay:{},onAddcontrol:{},onRemovecontrol:{},onRemoveoverlay:{},onClearoverlays:{},onDragstart:{},onDragging:{},onDragend:{},onAddtilelayer:{},onRemovetilelayer:{},onLoad:{},onResize:{},onHotspotclick:{},onHotspotover:{},onHotspotout:{},onTilesloaded:{},onTouchstart:{},onTouchmove:{},onTouchend:{},onLongpress:{}},emits:["initd","unload","pluginReady","click","dblclick","rightclick","rightdblclick","maptypechange","mousemove","mouseover","mouseout","movestart","moving","moveend","zoomstart","zoomend","addoverlay","addcontrol","removecontrol","removeoverlay","clearoverlays","dragstart","dragging","dragend","addtilelayer","removetilelayer","load","resize","hotspotclick","hotspotover","hotspotout","tilesloaded","touchstart","touchmove","touchend","longpress"],setup(e,{expose:o,emit:n}){const i=e,l=t.ref();let s=null,c=t.ref(!1);const u=t.getCurrentInstance(),d=$(),{emit:p}=a(),f=t.computed((()=>L(i.width)?i.width:`${i.width}px`)),m=t.computed((()=>L(i.height)?i.height:`${i.height}px`)),g=k&&!!u;function y(){i.mapStyleId?s.setMapStyleV2({styleId:i.mapStyleId}):i.mapStyleJson&&s.setMapStyleV2({styleJson:i.mapStyleJson})}function v(e){e?s.setTrafficOn():s.setTrafficOff()}function h(e){var t,o;"string"==typeof e?s.centerAndZoom(e,i.zoom):s.centerAndZoom((t=e.lng,o=e.lat,new BMapGL.Point(t,o)),i.zoom)}function b(e){s.setDisplayOptions(e||{})}function M(e){s.setZoom(e,{noAnimation:i.noAnimation})}function O(e){void 0!==window[e]&&s.setMapType(window[e])}function S(e){s.setHeading(e)}function G(e){s.setTilt(e)}function I(e){e?s.enableDragging():s.disableDragging()}function P(e){e?s.enableInertialDragging():s.disableInertialDragging()}function _(e){e?s.enableScrollWheelZoom():s.disableScrollWheelZoom()}function T(e){e?s.enableContinuousZoom():s.disableContinuousZoom()}function x(e){e?s.enableResizeOnCenter():s.disableResizeOnCenter()}function D(e){e?s.enableDoubleClickZoom():s.disableDoubleClickZoom()}function R(e){e?s.enableKeyboard():s.disableKeyboard()}function z(e){e?s.enablePinchToZoom():s.disablePinchToZoom()}function E(e){e?s.enableAutoResize():s.disableAutoResize()}return t.onMounted((function(){if(!g)return;const{proxy:e}=u,o=i.ak||e&&e.$baiduMapAk,a=i.apiUrl||e&&e.$baiduMapApiUrl,f=i.plugins&&e.$baiduMapPlugins?Object.assign(e.$baiduMapPlugins,i.plugins):i.plugins||e.$baiduMapPlugins,m=i.pluginsSourceLink&&e.$baiduMapPluginsSourceLink?Object.assign(e.$baiduMapPluginsSourceLink,i.pluginsSourceLink):i.pluginsSourceLink||e.$baiduMapPluginsSourceLink||{},k=a?"_initBMap_":`_initBMap_${o}`;r({src:a?`${a.replace(/&$/,"")}&callback=${k}`:`//api.map.baidu.com/api?type=webgl&v=1.0&ak=${o}&callback=${k}`,addCalToWindow:!0,key:k,exportGetter:()=>window.BMapGL}).then((()=>{const{restrictCenter:e,enableIconClick:o,backgroundColor:a,minZoom:r,maxZoom:g,mapType:k,enableAutoResize:L,showControls:N,center:j,displayOptions:W}=i;if(!l.value)return;s=new BMapGL.Map(l.value,{backgroundColor:a,enableIconClick:o,restrictCenter:e,minZoom:r,maxZoom:g,mapType:window[k],enableAutoResize:L,showControls:N,displayOptions:W}),h(j),function(){const{enableDragging:e,enableInertialDragging:t,enableScrollWheelZoom:o,enableContinuousZoom:n,enableResizeOnCenter:a,enableDoubleClickZoom:l,enableKeyboard:r,enablePinchToZoom:s,enableAutoResize:c,enableTraffic:u,mapType:d,zoom:p,tilt:f,heading:m}=i;M(p),G(f),v(u),S(m),O(d),R(r),I(e),E(c),z(s),T(n),x(a),D(l),_(o),P(t)}(),y(),t.watch((()=>i.zoom),M),t.watch((()=>i.tilt),G),t.watch((()=>i.heading),S),t.watch((()=>i.center),A(h),{deep:!0}),t.watch((()=>i.mapStyleId),y),t.watch((()=>i.mapStyleJson),A(y),{deep:!0}),t.watch((()=>i.displayOptions),A(b),{deep:!0}),t.watch((()=>i.mapType),O),t.watch((()=>i.enableTraffic),v),t.watch((()=>i.enableDragging),I),t.watch((()=>i.enableInertialDragging),P),t.watch((()=>i.enableScrollWheelZoom),_),t.watch((()=>i.enableContinuousZoom),T),t.watch((()=>i.enableResizeOnCenter),x),t.watch((()=>i.enableDoubleClickZoom),D),t.watch((()=>i.enableKeyboard),R),t.watch((()=>i.enablePinchToZoom),z),t.watch((()=>i.enableAutoResize),E),C(i,n,s),f&&function(e,t={}){const o=[...new Set(e)].reduce(((e,o)=>{let n;return"string"==typeof o&&(n=w[o])?e.push(n(t[o])):"function"==typeof o&&e.push(o()),e}),[]);return Promise.all(o).then((e=>{const t={};return e.forEach((e=>{Object.assign(t,e)})),t}))}(f,m).then((e=>{n("pluginReady",s,e)})).catch((e=>{B("BMap","plugins error: "+e)}));const $={map:s,instance:u,BMapGL:window.BMapGL};p(d,$),n("initd",$),c.value=!0})).catch((e=>B("BMap",e.message)))})),t.onUnmounted((()=>{if(s)try{s.destroy()}catch(e){B("BMapGL SDK",e.message)}})),o({getMapInstance:()=>s,getBaseMapOptions:()=>i,resetCenter:()=>h(i.center),setDragging:I}),t.provide("getMapInstance",(()=>s)),t.provide("baseMapSetCenterAndZoom",(e=>h(e))),t.provide("baseMapSetDragging",(e=>I(e))),t.provide("getBaseMapOptions",(()=>i)),(e,o)=>t.unref(g)?(t.openBlock(),t.createElementBlock(t.Fragment,{key:0},[t.createElementVNode("div",{id:t.unref(d),class:"baidu-map-container",ref_key:"mapContainer",ref:l,style:t.normalizeStyle([{width:f.value,height:m.value,background:i.loadingBgColor},{position:"relative",overflow:"hidden"}])},[t.renderSlot(e.$slots,"loading",{},(()=>[t.createElementVNode("div",{style:t.normalizeStyle([{color:i.loadingTextColor},{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)"}])},t.toDisplayString(t.unref(c)?"":"map loading..."),5)]))],12,Z),t.renderSlot(e.$slots,"default")],64)):t.createCommentVNode("",!0)}}));function U(e){const{on:o,off:n,emit:l}=a(),r=t.getCurrentInstance(),s=$(),{emit:c}=r||{emit:i},u=t.inject("getMapInstance",void 0),d=t.inject("parentComponentId",void 0)||"",p=r&&r.type.name||"";if(void 0===u)return{ready:i};const f=u();let m;const g=t=>{m=e(t.map)};return t.onMounted((()=>{f?g({map:f}):o(d,g)})),t.onBeforeUnmount((()=>{try{m&&m()}catch(e){B(p,e.message)}c("unload"),n(d,g)})),{ready:(e,t)=>{const o={map:e,instance:t,BMapGL:window.BMapGL};c("initd",o),l(s,o)}}}const H={style:{display:"none"}};var V=t.defineComponent(Object.assign({name:"BControl",inheritAttrs:!1},{__name:"index",props:{anchor:{default:"BMAP_ANCHOR_TOP_LEFT"},offset:{default:()=>({x:83,y:18})},visible:{type:Boolean,default:!0}},emits:["initd","unload"],setup(e){const o=e,n=t.ref(),{ready:a}=U((e=>{if(!n.value)return;const{offset:i,anchor:l,visible:r}=o,s=new BMapGL.Control;return s.defaultAnchor=window[l],s.defaultOffset=new BMapGL.Size(i.x,i.y),s.initialize=e=>e.getContainer().appendChild(n.value),r&&e.addControl(s),a(e,s),t.watch((()=>o.visible),(t=>{e[t?"addControl":"removeControl"](s)})),()=>e.removeControl(s)}));return(e,o)=>(t.openBlock(),t.createElementBlock("div",H,[t.createElementVNode("div",t.mergeProps({ref_key:"controlContainer",ref:n},e.$attrs),[t.renderSlot(e.$slots,"default")],16)]))}})),J=t.defineComponent(Object.assign({name:"BScale"},{__name:"index",props:{anchor:{default:"BMAP_ANCHOR_BOTTOM_LEFT"},offset:{default:()=>({x:83,y:18})},unit:{default:"BMAP_UNIT_METRIC"},visible:{type:Boolean,default:!0}},emits:["initd","unload"],setup(e){const o=e;let n;const{ready:a}=U((e=>{const{visible:l,offset:r,anchor:s}=o;return n=new BMapGL.ScaleControl({offset:new BMapGL.Size(r.x,r.y),anchor:window[s]}),l&&e.addControl(n),i(),a(e,n),t.watch((()=>o.visible),(t=>{e[t?"addControl":"removeControl"](n)})),()=>e.removeControl(n)}));function i(){n.setUnit(window[o.unit])}return t.watch((()=>o.unit),i),(e,t)=>null}})),q=t.defineComponent(Object.assign({name:"BZoom"},{__name:"index",props:{anchor:{default:"BMAP_ANCHOR_BOTTOM_RIGHT"},offset:{default:()=>({x:83,y:18})},visible:{type:Boolean,default:!0}},emits:["initd","unload"],setup(e){const o=e;let n;const{ready:a}=U((e=>{const{visible:i,offset:l,anchor:r}=o;return n=new BMapGL.ZoomControl({offset:new BMapGL.Size(l.x,l.y),anchor:window[r]}),i&&e.addControl(n),a(e,n),t.watch((()=>o.visible),(t=>{e[t?"addControl":"removeControl"](n)})),()=>e.removeControl(n)}));return(e,t)=>null}})),K=t.defineComponent(Object.assign({name:"BCityList"},{__name:"index",props:{anchor:{default:"BMAP_ANCHOR_TOP_LEFT"},offset:{default:()=>({x:18,y:18})},expand:{type:Boolean,default:!1},visible:{type:Boolean,default:!0}},emits:["initd","unload"],setup(e){const o=e;let n;const{ready:a}=U((e=>{const{visible:i,expand:l,offset:r,anchor:s}=o;return n=new BMapGL.CityListControl({expand:l,offset:new BMapGL.Size(r.x,r.y),anchor:window[s]}),i&&e.addControl(n),a(e,n),t.watch((()=>o.visible),(t=>{e[t?"addControl":"removeControl"](n)})),()=>e.removeControl(n)}));return(e,t)=>null}})),Y=t.defineComponent(Object.assign({name:"BLocation"},{__name:"index",props:{anchor:{default:"BMAP_ANCHOR_BOTTOM_RIGHT"},offset:{default:()=>({x:18,y:18})},visible:{type:Boolean,default:!0},onLocationError:{},onLocationSuccess:{}},emits:["initd","unload","locationSuccess","locationError"],setup(e,{emit:o}){const n=e;let a;const{ready:i}=U((e=>{const{visible:l,offset:r,anchor:s}=n;return a=new BMapGL.LocationControl({offset:new BMapGL.Size(r.x,r.y),anchor:window[s]}),l&&e.addControl(a),i(e,a),C(n,o,a),t.watch((()=>n.visible),(t=>{e[t?"addControl":"removeControl"](a)})),()=>e.removeControl(a)}));return(e,t)=>null}}));const Q={},X={style:{display:"none"}};var ee=t.defineComponent(Object.assign({name:"BCopyright",inheritAttrs:!1},{__name:"index",props:{anchor:{default:"BMAP_ANCHOR_BOTTOM_RIGHT"},offset:{default:()=>({x:83,y:18})},visible:{type:Boolean,default:!0}},emits:["initd","unload"],setup(e){var o;const n=e,a=t.ref();let i;const l=null===(o=t.getCurrentInstance())||void 0===o?void 0:o.uid,{ready:r}=U((e=>{const{anchor:o,offset:s,visible:c}=n;if(!a.value)return;let u=e.getBounds();return(i=Q[o])||(i=new BMapGL.CopyrightControl({offset:new BMapGL.Size(s.x,s.y),anchor:window[o]}),Q[o]=i,e.addControl(i)),c&&i.addCopyright({id:l,content:a.value.innerHTML,bounds:u}),r(e,i),t.watch((()=>n.visible),(e=>{e?a.value&&i.addCopyright({id:l,content:a.value.innerHTML,bounds:u}):l&&i.removeCopyright(l)})),()=>{var t,n;const a=Q[o],i=null===(t=null==a?void 0:a.getCopyrightCollection)||void 0===t?void 0:t.bind(a);i&&(null===(n=i())||void 0===n?void 0:n.length)>1?a.removeCopyright(l):(e.removeControl(a),Reflect.deleteProperty(Q,o))}}));return t.onUpdated((()=>{if(!i)return;let e=i.getCopyright(l);e&&a.value&&e.content!==a.value.innerHTML&&i.addCopyright({id:l,content:a.value.innerHTML,bounds:e.bounds})})),(e,o)=>(t.openBlock(),t.createElementBlock("div",X,[t.createElementVNode("div",t.mergeProps({ref_key:"copyrightContainer",ref:a},e.$attrs),[t.renderSlot(e.$slots,"default")],16)]))}})),te=t.defineComponent(Object.assign({name:"BNavigation3d"},{__name:"index",props:{anchor:{default:"BMAP_ANCHOR_BOTTOM_RIGHT"},offset:{default:()=>({x:83,y:18})},visible:{type:Boolean,default:!0}},emits:["initd","unload"],setup(e){const o=e;let n;const{ready:a}=U((e=>{const{visible:i,offset:l,anchor:r}=o;return n=new BMapGL.NavigationControl3D({offset:new BMapGL.Size(l.x,l.y),anchor:window[r]}),i&&e.addControl(n),a(e,n),t.watch((()=>o.visible),(t=>{e[t?"addControl":"removeControl"](n)})),()=>e.removeControl(n)}));return(e,t)=>null}})),oe=t.defineComponent(Object.assign({name:"BPanoramaControl"},{__name:"index",props:{anchor:{default:"BMAP_ANCHOR_TOP_RIGHT"},offset:{default:()=>({x:10,y:10})},visible:{type:Boolean,default:!0}},emits:["initd","unload"],setup(e){const o=e;let n;const{ready:a}=U((e=>{const{visible:i,offset:l,anchor:r}=o;return n=new BMapGL.PanoramaControl,n.setOffset(new BMapGL.Size(l.x,l.y)),n.setAnchor(window[r]),i&&e.addControl(n),a(e,n),t.watch((()=>o.visible),(t=>{e[t?"addControl":"removeControl"](n)})),()=>e.removeControl(n)}));return(e,t)=>null}})),ne=t.defineComponent(Object.assign({name:"BPanoramaCoverageLayer"},{__name:"index",emits:["initd","unload"],setup(e){let t;const{ready:o}=U((e=>(t=new BMapGL.PanoramaCoverageLayer,e.addTileLayer(t),o(e,t),()=>e.removeTileLayer(t))));return(e,t)=>null}})),ae=t.defineComponent(Object.assign({name:"BMarker"},{__name:"index",props:{position:{},offset:{default:()=>({x:0,y:0})},icon:{},zIndex:{},enableMassClear:{type:Boolean,default:!0},enableDragging:{type:Boolean,default:!1},enableClicking:{type:Boolean,default:!0},raiseOnDrag:{type:Boolean,default:!1},draggingCursor:{default:"pointer"},rotation:{default:0},title:{default:""},visible:{type:Boolean,default:!0},onClick:{},onDblclick:{},onMousedown:{},onMouseup:{},onMouseout:{},onMouseover:{},onRemove:{},onInfowindowclose:{},onInfowindowopen:{},onDragstart:{},onDragging:{},onDragend:{},onRightclick:{}},emits:["initd","unload","click","dblclick","mousedown","mouseup","mouseout","mouseover","remove","infowindowclose","infowindowopen","dragstart","dragging","dragend","rightclick"],setup(e,{emit:o}){const n=e;let a;const{ready:i}=U((e=>{const p=()=>{if(!n.position)return!1;const{position:t,offset:r,enableMassClear:s,enableDragging:c,enableClicking:u,raiseOnDrag:p,draggingCursor:f,rotation:m,title:g,icon:y,zIndex:v,visible:h}=n,b={offset:new BMapGL.Size(r.x,r.y),enableMassClear:s,enableDragging:c,enableClicking:u,raiseOnDrag:p,draggingCursor:f,title:g};y&&(b.icon=l()),a=new BMapGL.Marker(new BMapGL.Point(t.lng,t.lat),b),d(m),O(v)&&function(e){a.setZIndex(e)}(v),h&&e.addOverlay(a),C(n,o,a),i(e,a)};return p(),t.watch((()=>n.position),A((e=>{var t;a?(t=e)&&t.lat&&t.lng&&a.setPosition(new BMapGL.Point(t.lng,t.lat)):p()})),{deep:!0}),t.watch((()=>n.icon),A(r),{deep:!0}),t.watch((()=>n.offset),A(u),{deep:!0}),t.watch((()=>n.enableDragging),s),t.watch((()=>n.enableMassClear),c),t.watch((()=>n.rotation),d),t.watch((()=>n.visible),(t=>{e[t?"addOverlay":"removeOverlay"](a)})),()=>{a&&e.removeOverlay(a)}}));function l(){const e=W(),{icon:t}=n;if(L(t)&&e[t])return e[t];{const{anchor:e,imageOffset:o,imageSize:n,imageUrl:a,printImageUrl:i,size:l}=t,r={size:new BMapGL.Size(l.width,l.height)};return n&&(r.imageSize=new BMapGL.Size(n.width,n.height)),e&&(r.anchor=new BMapGL.Size(e.x,e.y)),o&&(r.imageOffset=new BMapGL.Size(o.x,o.y)),i&&(r.printImageUrl=i),new BMapGL.Icon(a,new BMapGL.Size(l.width,l.height),r)}}function r(){a.setIcon(l())}function s(e){e?a.enableDragging():a.disableDragging()}function c(e){e?a.enableMassClear():a.disableMassClear()}function u(e){e&&a.setOffset(new BMapGL.Size(e.x,e.y))}function d(e){void 0!==e&&a.setRotation(e)}return t.provide("getOverlayInstance",(()=>a)),(e,o)=>t.renderSlot(e.$slots,"default")}})),ie=t.defineComponent(Object.assign({name:"BMarker3d"},{__name:"index",props:{position:{},height:{},size:{default:50},shape:{default:"BMAP_SHAPE_CIRCLE"},fillColor:{default:"#f00"},fillOpacity:{default:.8},icon:{},enableMassClear:{type:Boolean,default:!0},visible:{type:Boolean,default:!0},onClick:{},onDblclick:{},onMousedown:{},onMouseup:{},onMouseout:{},onMouseover:{},onRemove:{},onRightclick:{}},emits:["initd","unload","click","dblclick","mousedown","mouseup","mouseout","mouseover","remove","rightclick"],setup(e,{emit:o}){const n=e;let a;const{ready:i}=U((e=>{const d=()=>{if(!n.position)return!1;if(!n.height)return!1;const{position:t,shape:r,fillColor:s,fillOpacity:c,size:d,icon:p,height:f,enableMassClear:m,visible:g}=n,y={size:d,fillColor:s,fillOpacity:c,shape:window[r]};p&&(y.icon=l()),a=new BMapGL.Marker3D(new BMapGL.Point(t.lng,t.lat),f,y),g&&e.addOverlay(a),u(m),C(n,o,a),i(e,a)};return d(),t.watch((()=>n.position),A((e=>{a?function(e){try{a.setPosition(new BMapGL.Point(e.lng,e.lat))}catch(e){console.error(e)}}(e):d()})),{deep:!0}),t.watch((()=>n.height),(e=>{var t;a?(t=e,a.setHeight(t)):d()})),t.watch((()=>n.enableMassClear),u),t.watch((()=>n.icon),A(c),{deep:!0}),t.watch((()=>n.enableMassClear),u),t.watch((()=>n.fillOpacity),s),t.watch((()=>n.fillColor),r),t.watch((()=>n.visible),(t=>{e[t?"addOverlay":"removeOverlay"](a)})),()=>{e.removeOverlay(a)}}));function l(){const{icon:e}=n,{anchor:t,imageOffset:o,imageSize:a,imageUrl:i,printImageUrl:l}=e,r={imageSize:new BMapGL.Size(a.width,a.height)};return t&&(r.anchor=new BMapGL.Size(t.x,t.y)),o&&(r.imageOffset=new BMapGL.Size(o.x,o.y)),l&&(r.printImageUrl=l),new BMapGL.Icon(i,new BMapGL.Size(a.width,a.height),r)}function r(e){a.setFillColor(e)}function s(e){a.setFillOpacity(e)}function c(){a.setIcon(l())}function u(e){e?a.enableMassClear():a.disableMassClear()}return t.provide("getOverlayInstance",(()=>a)),(e,o)=>t.renderSlot(e.$slots,"default")}})),le=t.defineComponent(Object.assign({name:"BLabel"},{__name:"index",props:{content:{},position:{},offset:{default:()=>({x:0,y:0})},zIndex:{},style:{},enableMassClear:{type:Boolean,default:!0},visible:{type:Boolean,default:!0},onClick:{},onDblclick:{},onMousedown:{},onMouseup:{},onMouseout:{},onMouseover:{},onRemove:{},onRightclick:{}},emits:["initd","unload","click","dblclick","mousedown","mouseup","mouseout","mouseover","remove","rightclick"],setup(e,{emit:o}){const n=e;let a;const i=()=>!!a,{ready:l}=U((e=>{const f=()=>{if(!n.content)return!1;if(!n.position)return!1;const{content:i,position:l,offset:s,enableMassClear:u,style:f,visible:m,zIndex:g}=n,y={position:new BMapGL.Point(l.lng,l.lat),offset:new BMapGL.Size(s.x,s.y),enableMassClear:u};a=new BMapGL.Label(i,y),f&&a.setStyle(f),m&&e.addOverlay(a),O(g)&&r(g),C(n,o,a),t.watch((()=>n.offset),A(d),{deep:!0}),t.watch((()=>n.style),A(c),{deep:!0}),t.watch((()=>n.enableMassClear),p),t.watch((()=>n.zIndex),r),t.watch((()=>n.visible),(t=>{e[t?"addOverlay":"removeOverlay"](a)}))};return f(),l(e,a),t.watch((()=>n.position),A(T(i,s,f)),{deep:!0}),t.watch((()=>n.content),T(i,u,f)),()=>{a&&e.removeOverlay(a)}}));function r(e){O(e)&&a.setZIndex(e)}function s(e){a.setPosition(new BMapGL.Point(e.lng,e.lat))}function c(e){O(e)&&a.setStyle(e)}function u(e){a.setContent(e)}function d(e){a.setOffset(new BMapGL.Size(e.x,e.y))}function p(e){e?a.enableMassClear():a.disableMassClear()}return t.provide("getOverlayInstance",(()=>a)),(e,o)=>t.renderSlot(e.$slots,"default")}})),re=t.defineComponent(Object.assign({name:"BPolyline"},{__name:"index",props:{path:{},strokeColor:{default:"#000"},strokeWeight:{default:2},strokeOpacity:{default:1},strokeStyle:{default:"solid"},enableMassClear:{type:Boolean,default:!0},enableEditing:{type:Boolean,default:!1},enableClicking:{type:Boolean,default:!0},geodesic:{type:Boolean,default:!1},clip:{type:Boolean,default:!0},linkRight:{type:Boolean,default:!0},visible:{type:Boolean,default:!0},onClick:{},onDblclick:{},onMousedown:{},onMouseup:{},onMouseout:{},onMouseover:{},onRemove:{},onLineupdate:{}},emits:["initd","unload","click","dblclick","mousedown","mouseup","mouseout","mouseover","remove","lineupdate"],setup(e,{emit:o}){const n=e;let a;const{ready:i}=U((e=>{const p=()=>{a&&e.removeOverlay(a)},f=()=>{if(!n.path||!n.path.length)return!1;const{path:p,strokeColor:f,strokeWeight:m,strokeOpacity:g,strokeStyle:y,enableMassClear:v,enableEditing:h,enableClicking:b,geodesic:w,clip:M,linkRight:B,visible:k}=n,O=I(p);a=new BMapGL.Polyline(O,{strokeColor:f,strokeWeight:m,strokeOpacity:g,strokeStyle:y,enableMassClear:v,enableEditing:h,enableClicking:b,geodesic:w,linkRight:B,clip:M}),k&&e.addOverlay(a),C(n,o,a),i(e,a),t.watch((()=>n.strokeColor),l),t.watch((()=>n.strokeOpacity),r),t.watch((()=>n.strokeWeight),s),t.watch((()=>n.strokeStyle),c),t.watch((()=>n.enableMassClear),u),t.watch((()=>n.enableEditing),d)};return f(),t.watch((()=>n.path),A((e=>{var t;a?e.length?(t=e,a&&a.setPath(I(t))):p():f()})),{deep:!0}),t.watch((()=>n.visible),(t=>{e[t?"addOverlay":"removeOverlay"](a)})),p}));function l(e){a&&a.setStrokeColor(e)}function r(e){a&&a.setStrokeOpacity(e)}function s(e){a&&a.setStrokeWeight(e)}function c(e){a&&a.setStrokeStyle(e)}function u(e){a&&(e?a.enableMassClear():a.disableMassClear())}function d(e){a&&(e?a.enableEditing():a.disableEditing())}return t.provide("getOverlayInstance",(()=>a)),(e,o)=>t.renderSlot(e.$slots,"default")}})),se=t.defineComponent(Object.assign({name:"BBezierCurve"},{__name:"index",props:{path:{},controlPoints:{},strokeColor:{default:"#000"},strokeWeight:{default:2},strokeOpacity:{default:1},strokeStyle:{default:"solid"},enableMassClear:{type:Boolean,default:!0},visible:{type:Boolean,default:!0},onClick:{},onDblclick:{},onMousedown:{},onMouseup:{},onMouseout:{},onMouseover:{},onRemove:{},onLineupdate:{}},emits:["initd","unload","click","dblclick","mousedown","mouseup","mouseout","mouseover","remove","lineupdate"],setup(e,{emit:o}){const n=e;let a;const{ready:i}=U((e=>{const d=()=>{if(!n.path||!n.path||!n.path.length)return!1;if(!n.controlPoints||!n.controlPoints||!n.controlPoints.length)return!1;const{path:t,controlPoints:l,strokeColor:r,strokeWeight:s,strokeOpacity:c,strokeStyle:u,enableMassClear:d,visible:p}=n,f=I(t),m=l.map((e=>I(e)));try{a=new BMapGL.BezierCurve(f,m,{strokeColor:r,strokeWeight:s,strokeOpacity:c,strokeStyle:u,enableMassClear:d})}catch(e){}p&&e.addOverlay(a),i(e,a),C(n,o,a)};return d(),t.watch((()=>n.path),A((e=>{var t;a?(t=e,a.setPath(I(t))):d()})),{deep:!0}),t.watch((()=>n.controlPoints),A((e=>{var t;a?(t=e,a.setControlPoints(t.map((e=>I(e))))):d()})),{deep:!0}),t.watch((()=>n.strokeColor),l),t.watch((()=>n.strokeOpacity),r),t.watch((()=>n.strokeWeight),s),t.watch((()=>n.strokeStyle),c),t.watch((()=>n.enableMassClear),u),t.watch((()=>n.visible),(t=>{e[t?"addOverlay":"removeOverlay"](a)})),()=>{e.removeOverlay(a)}}));function l(e){a.setStrokeColor(e)}function r(e){a.setStrokeOpacity(e)}function s(e){a.setStrokeWeight(e)}function c(e){a.setStrokeStyle(e)}function u(e){e?a.enableMassClear():a.disableMassClear()}return t.provide("getOverlayInstance",(()=>a)),(e,o)=>t.renderSlot(e.$slots,"default")}})),ce=t.defineComponent(Object.assign({name:"BPolygon"},{__name:"index",props:{path:{},isBoundary:{type:Boolean},strokeColor:{default:"#000"},autoCenter:{type:Boolean,default:!0},strokeWeight:{default:2},strokeOpacity:{default:1},strokeStyle:{default:"solid"},fillColor:{default:"#fff"},fillOpacity:{default:.3},enableMassClear:{type:Boolean},enableEditing:{type:Boolean},enableClicking:{type:Boolean},geodesic:{type:Boolean,default:!1},clip:{type:Boolean,default:!0},visible:{type:Boolean,default:!0},onClick:{},onDblclick:{},onMousedown:{},onMouseup:{},onMouseout:{},onMouseover:{},onRemove:{},onLineupdate:{}},emits:["initd","unload","click","dblclick","mousedown","mouseup","mouseout","mouseover","remove","lineupdate"],setup(e,{emit:o}){const n=e,a=t.inject("baseMapSetCenterAndZoom");let i;const{ready:l}=U((e=>{const a=()=>{if(!n.path)return!1;if(!n.path.length)return;const{path:a,strokeColor:y,strokeWeight:v,strokeOpacity:h,strokeStyle:b,fillOpacity:w,fillColor:M,enableMassClear:B,enableEditing:k,enableClicking:O,geodesic:L,clip:S,isBoundary:G,visible:P}=n,_=G?a:I(a);_&&(i=new BMapGL.Polygon(_,{strokeColor:y,strokeWeight:v,strokeOpacity:h,strokeStyle:b,fillOpacity:w,fillColor:M,enableMassClear:B,enableEditing:k,enableClicking:O,geodesic:L,clip:S}),P&&e.addOverlay(i),P&&r(),C(n,o,i),l(e,i),t.watch((()=>n.strokeColor),s),t.watch((()=>n.strokeOpacity),u),t.watch((()=>n.fillColor),c),t.watch((()=>n.fillOpacity),d),t.watch((()=>n.strokeWeight),p),t.watch((()=>n.strokeStyle),f),t.watch((()=>n.enableMassClear),m),t.watch((()=>n.enableEditing),g),t.watch((()=>n.visible),(t=>{e[t?"addOverlay":"removeOverlay"](i),t&&r()})))};return a(),t.watch((()=>n.path),A((e=>{i?function(e){const{isBoundary:t}=n;i.setPath(t?e:I(e)),r()}(e):a()})),{deep:!0}),()=>{i&&e.removeOverlay(i)}}));function r(){t.nextTick((()=>{if(n.autoCenter)try{const e=i.getBounds();if(e){const t=e.getCenter();t&&a(t)}}catch(e){console.warn("BPolygon","auto set center error",e)}}))}function s(e){i&&i.setStrokeColor(e)}function c(e){i&&i.setFillColor(e)}function u(e){i&&i.setStrokeOpacity(e)}function d(e){i&&i.setFillOpacity(e)}function p(e){i&&i.setStrokeWeight(e)}function f(e){i&&i.setStrokeStyle(e)}function m(e){i&&(e?i.enableMassClear():i.disableMassClear())}function g(e){i&&(e?i.enableEditing():i.disableEditing())}return t.provide("getOverlayInstance",(()=>i)),(e,o)=>t.renderSlot(e.$slots,"default")}}));const ue={style:{display:"none"}};var de=t.defineComponent(Object.assign({name:"BInfoWindow"},{__name:"index",props:{modelValue:{type:Boolean,default:void 0},show:{type:Boolean,default:!1},title:{default:""},position:{},width:{default:0},height:{default:0},maxWidth:{default:220},offset:{default:()=>({x:0,y:0})},enableAutoPan:{type:Boolean,default:!0},enableCloseOnClick:{type:Boolean,default:!0},onClose:{},onOpen:{},onMaximize:{},onRestore:{},onClickclose:{}},emits:["initd","unload","close","open","maximize","restore","clickclose","update:show"],setup(e,{emit:o}){const n=e,a=t.computed({get:()=>n.show,set:e=>o("update:show",e)}),i=t.ref();let l,r;t.watchEffect((()=>{n.modelValue,0}));const{ready:s}=U((e=>{r=e;const h=()=>{const{title:r,width:u,height:p,enableAutoPan:f,maxWidth:m,offset:g,enableCloseOnClick:y}=n,v={width:u,height:p,title:r,maxWidth:m,enableAutoPan:f,enableCloseOnClick:y,offset:new BMapGL.Size(g.x,g.y)};l=new BMapGL.InfoWindow(i.value||"",v),l.addEventListener("close",(()=>{n.show&&(a.value=!1)})),l.addEventListener("open",(()=>{n.show||(a.value=!0)})),e.addOverlay(l),d(),function(){const e=window.MutationObserver;if(!e)return;new e((()=>{l.redraw()})).observe(i.value,{attributes:!0,childList:!0,characterData:!0,subtree:!0})}(),C(n,o,l),s(e,l),n.show&&t.nextTick((()=>{c(),t.nextTick((()=>{"_visible"in l?!l._visible&&(a.value=!1):!l.isOpen()&&(a.value=!1)}))}))};return i.value?h():t.nextTick((()=>h())),t.watch((()=>n.position),A((e=>function(e){c(),l.setPosition(new BMapGL.Point(e.lng,e.lat)),a.value||u()}(e))),{deep:!0}),t.watch((()=>n.title),p),t.watch((()=>n.width),m),t.watch((()=>n.height),f),t.watch((()=>n.maxWidth),g),t.watch((()=>n.enableAutoPan),y),t.watch((()=>n.enableCloseOnClick),v),t.watch((()=>n.show),A((()=>{n.show?c():u()}))),()=>{l&&(l.hide(),e.removeOverlay(l))}}));function c(){const{position:e}=n;e&&l&&(r.openInfoWindow(l,new BMapGL.Point(e.lng,e.lat)),a.value=!0)}function u(){l&&(l.hide(),a.value=!1)}function d(){var e;l.redraw(),Array.prototype.forEach.call((null===(e=i.value)||void 0===e?void 0:e.querySelectorAll("img"))||[],(e=>{e.onload=()=>{l.redraw()}}))}function p(e){l.setTitle(e)}function f(e){l.setHeight(e)}function m(e){l.setWidth(e)}function g(e){l.setMaxWidth(e)}function y(e){e?l.enableAutoPan():l.disableAutoPan()}function v(e){e?l.enableCloseOnClick():l.disableCloseOnClick()}return t.onUpdated((()=>{var e;l&&l.isOpen()&&(e=i.value||"",l.setContent(e),d())})),t.provide("getOverlayInstance",(()=>l)),(e,o)=>(t.openBlock(),t.createElementBlock("div",ue,[t.createElementVNode("div",t.mergeProps({ref_key:"infoWindowContainer",ref:i},e.$attrs),[t.renderSlot(e.$slots,"default")],16)]))}})),pe=t.defineComponent(Object.assign({name:"BCircle"},{__name:"index",props:{center:{},radius:{},strokeColor:{default:"#000"},strokeOpacity:{default:1},fillColor:{default:"#fff"},fillOpacity:{default:.3},strokeWeight:{default:2},strokeStyle:{default:"solid"},enableMassClear:{type:Boolean,default:!0},enableEditing:{type:Boolean,default:!1},enableClicking:{type:Boolean,default:!0},geodesic:{type:Boolean,default:!1},clip:{type:Boolean,default:!0},visible:{type:Boolean,default:!0},onClick:{},onDblclick:{},onMousedown:{},onMouseup:{},onMouseout:{},onMouseover:{},onRemove:{},onLineupdate:{}},emits:["initd","unload","click","dblclick","mousedown","mouseup","mouseout","mouseover","remove","lineupdate"],setup(e,{emit:o}){const n=e;let a;const{ready:i}=U((e=>((()=>{const{center:t,radius:l,strokeColor:r,strokeOpacity:s,fillColor:c,fillOpacity:u,strokeWeight:d,strokeStyle:p,enableMassClear:f,enableEditing:m,enableClicking:g,geodesic:y,clip:v,visible:h}=n,b=new BMapGL.Point(t.lng,t.lat);a=new BMapGL.Circle(b,l,{strokeColor:r,strokeWeight:d,strokeOpacity:s,strokeStyle:p,enableMassClear:f,enableEditing:m,enableClicking:g,geodesic:y,clip:v,fillOpacity:u,fillColor:c}),h&&e.addOverlay(a),C(n,o,a),i(e,a)})(),t.watch((()=>n.center),A(r),{deep:!0}),t.watch((()=>n.radius),l),t.watch((()=>n.strokeColor),s),t.watch((()=>n.strokeOpacity),u),t.watch((()=>n.fillColor),c),t.watch((()=>n.fillOpacity),d),t.watch((()=>n.strokeWeight),p),t.watch((()=>n.strokeStyle),f),t.watch((()=>n.enableMassClear),m),t.watch((()=>n.enableEditing),g),t.watch((()=>n.visible),(t=>{e[t?"addOverlay":"removeOverlay"](a)})),()=>{e.removeOverlay(a)})));function l(e){a.setRadius(e)}function r(e){a&&a.setCenter(new BMapGL.Point(e.lng,e.lat))}function s(e){a.setStrokeColor(e)}function c(e){a.setFillColor(e)}function u(e){a.setStrokeOpacity(e)}function d(e){a.setFillOpacity(e)}function p(e){a.setStrokeWeight(e)}function f(e){a.setStrokeStyle(e)}function m(e){e?a.enableMassClear():a.disableMassClear()}function g(e){e?a.enableEditing():a.disableEditing()}return t.provide("getOverlayInstance",(()=>a)),(e,o)=>t.renderSlot(e.$slots,"default")}})),fe=t.defineComponent(Object.assign({name:"BPrism"},{__name:"index",props:{path:{},altitude:{},isBoundary:{type:Boolean},topFillColor:{default:"#fff"},topFillOpacity:{default:.5},sideFillColor:{default:"#fff"},sideFillOpacity:{default:.8},autoCenter:{type:Boolean,default:!0},enableMassClear:{type:Boolean,default:!0},visible:{type:Boolean,default:!0},onClick:{},onDblclick:{},onMousedown:{},onMouseup:{},onMouseout:{},onMouseover:{},onRemove:{},onLineupdate:{}},emits:["initd","unload","click","dblclick","mousedown","mouseup","mouseout","mouseover","remove","lineupdate"],setup(e,{emit:o}){const n=e,a=t.inject("baseMapSetCenterAndZoom");let i;const{ready:l}=U((e=>{const f=()=>{if(!n.path||!n.path||!n.path.length)return!1;const{path:f,altitude:m,isBoundary:g,topFillColor:y,topFillOpacity:v,sideFillColor:h,sideFillOpacity:b,enableMassClear:w,visible:B}=n,k=g?f:I(f);i=new BMapGL.Prism(k,m,{topFillColor:y,topFillOpacity:v,sideFillColor:h,sideFillOpacity:b,enableMassClear:w}),B&&e.addOverlay(i),C(n,o,i),l(e,i),n.autoCenter&&t.nextTick((()=>{var e;try{const t=null===(e=i.getBounds())||void 0===e?void 0:e.getCenter();a(t)}catch(e){M("BPrism","auto set center error")}})),t.watch((()=>n.enableMassClear),r),t.watch((()=>n.topFillColor),s),t.watch((()=>n.topFillOpacity),c),t.watch((()=>n.sideFillColor),u),t.watch((()=>n.sideFillOpacity),d),t.watch((()=>n.altitude),p)};return f(),t.watch((()=>n.path),A((e=>{i?function(e){const{isBoundary:t}=n;i.setPath(t?e:I(e))}(e):f()})),{deep:!0}),t.watch((()=>n.visible),(t=>{e[t?"addOverlay":"removeOverlay"](i)})),()=>{i&&e.removeOverlay(i)}}));function r(e){e?i.enableMassClear():i.disableMassClear()}function s(e){i&&i.setTopFillColor(e)}function c(e){i&&i.setTopFillOpacity(e)}function u(e){i&&i.setSideFillColor(e)}function d(e){i&&i.setSideFillOpacity(e)}function p(e){i&&i.setAltitude(e)}return t.provide("getOverlayInstance",(()=>i)),(e,o)=>t.renderSlot(e.$slots,"default")}})),me=t.defineComponent(Object.assign({name:"BGroundOverlay"},{__name:"index",props:{type:{},url:{},startPoint:{},endPoint:{},autoCenter:{type:Boolean,default:!1},opacity:{default:1},visible:{type:Boolean,default:!0},onClick:{},onDblclick:{},onMousemove:{},onMouseover:{},onMouseout:{}},emits:["initd","unload","click","dblclick","mousemove","mouseover","mouseout"],setup(e,{emit:o}){const n=e;let a;const{ready:i}=U((e=>{const l=()=>{a&&e.removeOverlay(a)},r=()=>{l();let{startPoint:o,endPoint:i,opacity:r,type:s,autoCenter:c,visible:u}=n;const d=function(){let e=n.url;if("function"==typeof e&&(e=e(),!e))return!1;return e}();if(!d)return;if(!o)return!1;if(!i)return!1;const p=(f=o,m=i,new BMapGL.Bounds(new BMapGL.Point(f.lng,m.lat),new BMapGL.Point(m.lng,f.lat)));var f,m;const g={opacity:r,type:s,url:d.value||d};a=new BMapGL.GroundOverlay(p,g),u&&e.addOverlay(a),c&&t.nextTick((()=>{try{const t=p.getCenter();e.panTo(t)}catch(e){M("GroundOverlay","auto set center error")}}))};return r(),C(n,o,a),i(e,a),t.watch((()=>n),A(r),{deep:!0}),t.watch((()=>n.visible),(t=>{e[t?"addOverlay":"removeOverlay"](a)})),l}));return t.provide("getOverlayInstance",(()=>a)),(e,o)=>t.renderSlot(e.$slots,"default")}})),ge=t.defineComponent(Object.assign({name:"BContextMenu",inheritAttrs:!1},{__name:"index",props:{width:{default:100},visible:{type:Boolean,default:!0},menuItems:{default:()=>[]},onOpen:{},onClose:{}},emits:["initd","unload","open","close"],setup(e,{emit:o}){const n=e,a=t.inject("getOverlayInstance",(()=>null));let i;const{ready:l}=U((e=>{const r=a()||e,s=()=>{i&&r.removeContextMenu(i)},c=()=>{const{width:t,menuItems:a,visible:l}=n;i=new BMapGL.ContextMenu;for(const o of a){if(L(o)&&"-"===o){i.addSeparator();continue}const n=new BMapGL.MenuItem(o.text,(function(t,n){o.callback.call(null,{point:t,pixel:n,map:e,BMapGL:BMapGL,target:r})}),{width:t,id:String(Math.random())});o.disabled?n.disable():n.enable(),i.addItem(n)}l&&r.addContextMenu(i),C(n,o,i)};return t.watch((()=>n.visible),(e=>{i&&r[e?"addContextMenu":"removeContextMenu"](i)})),t.watch((()=>n.menuItems),A((()=>{s(),c()})),{deep:!0}),c(),l(e,i),s}));return(e,t)=>null}})),ye=t.defineComponent(Object.assign({name:"BDistrictLayer"},{__name:"index",props:{visible:{type:Boolean,default:!0},name:{},kind:{default:0},fillColor:{default:"#fdfd27"},fillOpacity:{default:1},strokeColor:{default:"#231cf8"},strokeWeight:{default:1},strokeOpacity:{default:1},viewport:{type:Boolean,default:!1},onClick:{},onDblclick:{},onRightclick:{},onRightdblclick:{},onMousemove:{},onMouseover:{},onMouseout:{}},emits:["initd","unload","mouseover","mouseout","click"],setup(e,{emit:o}){const n=e;let a;const{ready:i}=U((e=>{if(!n.name)return B("BDistrictLayer","DistrictLayer props.name is required");const{visible:l,name:r,kind:s,fillColor:c,fillOpacity:u,strokeColor:d,strokeOpacity:p,strokeWeight:f,viewport:m}=n;a=new BMapGL.DistrictLayer({name:`(${r})`,kind:s,fillColor:c,fillOpacity:u,strokeColor:d,strokeOpacity:p,strokeWeight:f,viewport:m}),l&&e.addDistrictLayer(a),C(n,o,a),t.watch((()=>n.visible),(t=>{a&&e[t?"addDistrictLayer":"removeDistrictLayer"](a)}));const g=setTimeout((()=>{i(e,a)}),400);return()=>{clearTimeout(g),e.removeDistrictLayer(a)}}));return(e,t)=>null}})),ve=t.defineComponent(Object.assign({name:"BMapMask"},{__name:"index",props:{path:{},showRegion:{default:"inside"},isBuildingMask:{type:Boolean,default:!1},isMapMask:{type:Boolean,default:!1},isPoiMask:{type:Boolean,default:!1},visible:{type:Boolean,default:!0},onClick:{},onDblclick:{},onMousedown:{},onMouseup:{},onMouseout:{},onMouseover:{},onRightclick:{}},emits:["initd","unload","click","dblclick","mousedown","mouseup","mouseout","mouseover","rightclick"],setup(e,{emit:o}){const n=e;let a;const{ready:i}=U((e=>{const l=()=>{a&&e.removeOverlay(a)};return t.watchPostEffect((()=>{if(l(),!n.path||!n.path||!n.path.length)return!1;const{path:t,showRegion:r,isBuildingMask:s,isMapMask:c,isPoiMask:u,visible:d}=n,p=I(t);a=new BMapGL.MapMask(p,{showRegion:r,isBuildingMask:s,isMapMask:c,isPoiMask:u}),d&&e.addOverlay(a),C(n,o,a),i(e,a)})),l}));return t.provide("getOverlayInstance",(()=>a)),(e,o)=>t.renderSlot(e.$slots,"default")}})),he=t.defineComponent(Object.assign({name:"BAutoComplete"},{__name:"index",props:{location:{},types:{},onSearchComplete:{},onHighlight:{},onConfirm:{}},emits:["initd","unload","searchComplete","highlight","confirm"],setup(e,{emit:o}){const n=e,a=t.ref();let i;const{ready:l}=U((e=>{a.value||M("BAutoComplete","render error");const{location:r,types:s}=n;let c=e;"object"==typeof r&&r.lat&&r.lng&&(c=P(r)),i=new BMapGL.Autocomplete({location:c,onSearchComplete:e=>o("searchComplete",e),input:a.value,types:s}),C(n,o,i),l(e,i),t.watch((()=>n.location),(t=>{let o=e;"object"==typeof t&&t.lat&&t.lng&&(o=P(t)),i.setLocation(o)})),t.watch((()=>n.types),(e=>{e&&i.setTypes(e)}))}));return(e,o)=>(t.openBlock(),t.createElementBlock("input",{class:"b-auto-complete-input",type:"text",ref_key:"autoCompleteInput",ref:a,placeholder:"请输入搜索关键词"},null,512))}})),be=[F,V,J,q,te,ae,ie,ee,Y,le,re,ce,ve,se,pe,K,fe,de,ge,oe,ne,me,ye,he];const we={install:(e,t)=>{const{ak:o,apiUrl:n,plugins:a,pluginsSourceLink:i}=t||{},l=e.config.globalProperties;for(const t of be){const o=t.name;e.component(o,t)}o&&(l.$baiduMapAk=o),n&&(l.$baiduMapApiUrl=n),a&&(l.$baiduMapPlugins=a),i&&(l.$baiduMapPluginsSourceLink=i)},version:"2.6.5"},Ce=we.install,Me=we.version;e.BAutoComplete=he,e.BBezierCurve=se,e.BCircle=pe,e.BCityList=K,e.BContextMenu=ge,e.BControl=V,e.BCopyright=ee,e.BDistrictLayer=ye,e.BGroundOverlay=me,e.BInfoWindow=de,e.BLabel=le,e.BLocation=Y,e.BMap=F,e.BMapMask=ve,e.BMarker=ae,e.BMarker3d=ie,e.BNavigation3d=te,e.BPanoramaControl=oe,e.BPanoramaCoverageLayer=ne,e.BPolygon=ce,e.BPolyline=re,e.BPrism=fe,e.BScale=J,e.BZoom=q,e.default=we,e.getScriptAsync=r,e.install=Ce,e.useAddressGeocoder=function(e){const o=t.ref(),n=t.ref(!0),a=t.ref(!0);let i;return{get:(t,l)=>{if(i||(i=new BMapGL.Geocoder),!t)return B("useAddressGeocoder","missing required params: address");if(!l)return B("useAddressGeocoder","missing required  params: city");const r=S(t);n.value=!0,(r?Promise.all(t.map((e=>R(i,e,l)))):R(i,t,l)).then((t=>{if(t)if(r){let e=0;o.value=t.map((t=>(e+=+!t,t))),a.value=e===t.length}else o.value=t,a.value=!1;else o.value=t,a.value=!0;e&&e(o)})).catch((e=>{B("useAddressGeocoder",e.message),a.value=!0,o.value=null})).finally((()=>{n.value=!1}))},point:o,isLoading:n,isEmpty:a}},e.useAreaBoundary=function(e){const o=t.ref(!1),n=t.ref([]);let a;return t.onUnmounted((()=>a=null)),{isLoading:o,boundaries:n,get:t=>{a||(a=new BMapGL.Boundary),o.value=!0,a.get(t,(t=>{o.value=!1,n.value=t.boundaries,e&&e(n)}))}}},e.useBrowserLocation=function(e,o){e=e||{};const n=t.ref({}),a=t.ref(!0),i=t.ref(!1),l=t.ref();return{get:()=>{e.SDKLocation=e.enableSDKLocation,new Promise(((t,o)=>{a.value=!0;const n=new BMapGL.Geolocation;n.getCurrentPosition((function(e){const a=n.getStatus();if(l.value=D[a],a===window.BMAP_STATUS_SUCCESS){const{address:o,accuracy:n,point:a}=e;t({accuracy:n,point:a,address:o})}else o()}),e)})).then((e=>{i.value=!1,n.value=e,o&&o(n)})).catch((()=>{i.value=!0})).finally((()=>{a.value=!1}))},isLoading:a,isError:i,status:l,location:n}},e.useDefaultMarkerIcons=W,e.useInstanceId=$,e.useIpLocation=function(e){const o=t.ref({}),n=t.ref(!0);return{location:o,isLoading:n,get:()=>{n.value=!0,(new BMapGL.LocalCity).get((t=>{n.value=!1,o.value={code:t.code,point:t.center,name:t.name},e&&e(o)}))}}},e.usePoint=function(){const e=t.ref(null);return{point:e,set:({lng:t,lat:o})=>{e.value=new BMapGL.Point(t,o)}}},e.usePointConvertor=function(){const e=t.ref(),o=t.ref(!0),n=t.ref(!1),a=t.ref();let i;return{convert:(t,l,r)=>{if(!t)return B("usePointConvertor","missing required params: points");if(!l)return B("usePointConvertor","missing required params: from");if(!r)return B("usePointConvertor","missing required params: to");if(!t.length)return;i||(i=new BMapGL.Convertor),o.value=!0;const s=t.map((e=>new BMapGL.Point(e.lng,e.lat)));(function(e,t,o,n){return new Promise(((a,i)=>{e.translate(t,o,n,(e=>{0===e.status?a(e):i(e.status)}))}))})(i,s,l,r).then((t=>{e.value=t.points.map((e=>({lng:e.lng,lat:e.lat}))),a.value=t.status,n.value=!1})).catch((e=>{e&&(a.value=e),n.value=!0})).finally((()=>{o.value=!1}))},result:e,isLoading:o,isError:n,status:a}},e.usePointGeocoder=function(e,o){e=e||{};const n=t.ref(),a=t.ref(!0),i=t.ref(!0);let l;return{get:t=>{if(!t)return B("usePointGeocoder","missing required params: point");l||(l=new BMapGL.Geocoder);const r=S(t);a.value=!0,(r?Promise.all(t.map((t=>z(l,t,e)))):z(l,t,e)).then((e=>{if(e)if(r){let t=0;n.value=e.map((e=>(t+=+!e,e))),i.value=t===e.length}else n.value=e,i.value=!1;else n.value=e,i.value=!0;o&&o(n)})).catch((()=>{i.value=!0,n.value=void 0})).finally((()=>{a.value=!1}))},result:n,isLoading:a,isEmpty:i}},e.useTrackAnimation=function(e,o){let n,a,i,l;const r=t.ref("INITIAL"),s=o||{};let c;t.watch((()=>e.value),(e=>{i=e}));const u=()=>{var e,t,o,r;n||(l=i.getMapInstance(),n=new BMapGLLib.TrackAnimation(l,a,s),e=n,t="_status",o=n._status,r=d,Object.defineProperty(e,t,{get:()=>o,set(e){o=e,r&&r(o)}}))},d=()=>{n&&(r.value=x[n._status])};return t.onUnmounted((()=>{n&&"INITIAL"!==r.value&&n.cancel(),l&&(l.removeOverlay(a),a=null)})),{setPath:e=>{const t=e.map((e=>new BMapGL.Point(e.lng,e.lat)));a=new BMapGL.Polyline(t),u()},start:()=>{const e=performance.now(),t=c||0,o=s.delay||0;n&&e-t>o&&"INITIAL"===r.value&&n.start(),c=performance.now()},stop:()=>{n&&n.pause()},cancel:()=>{n&&n.cancel()},proceed:()=>{n&&n.continue()},status:r}},e.useViewAnimation=function(e,o){(o=o||{}).disableDragging=void 0===o.disableDragging;const n=t.ref("INITIAL"),a={animationcancel:[()=>n.value="INITIAL"],animationend:[()=>n.value="INITIAL"],animationstart:[()=>n.value="PLAYING"]};let i,l,r=!1;t.watch((()=>e.value),(e=>{i=e}));const s=()=>{const{enableDragging:e}=i.getBaseMapOptions();i.setDragging(e)};let c={addEventListener(e,t){a[e]||(a[e]=[]),a[e].push(t)},removeEventListener(e,t){const o=a[e];if(o)if(t)for(let e=o.length;e>=0;e--)o[e]===t&&o.splice(e,1);else a[e]=[]}};return t.onUnmounted((()=>{try{c&&"INITIAL"==n.value&&(l=i.getMapInstance(),c._cancel(l),s())}catch(e){return!1}})),{viewAnimation:c,start:()=>{r&&"PLAYING"!==n.value&&(l=i.getMapInstance(),l.startViewAnimation(c),i.setDragging(!o.disableDragging))},cancel:()=>{r&&"INITIAL"!==n.value&&(c._cancel(l),s())},stop:()=>{r&&"PLAYING"===n.value&&(c._pause(),n.value="STOPPING")},proceed:()=>{r&&"STOPPING"===n.value&&(c._continue(),n.value="PLAYING")},status:n,setKeyFrames:e=>{const{loop:t,duration:n,delay:i}=o;for(const t of e)if(t.center){const{lng:e,lat:o}=t.center;t.center=new BMapGL.Point(e,o)}c=new BMapGL.ViewAnimation(e,{duration:n,delay:i,interation:t});for(const e of Object.keys(a)){const t=a[e];t&&t.length&&t.forEach((t=>{c.addEventListener(e,t)}))}r=!0}}},e.version=Me,Object.defineProperty(e,"__esModule",{value:!0})}));
