<template>
  <div class="view-img-box container-view video-view-container" tabindex="0" @click="ensureFocus" @click.self="visibleInfoFlag = false" @mouseup.stop="">
    <title-bar style="background-color: #f5f8fe"  is-title="图片预览" @click.self="visibleInfoFlag = false">
      <template #content>
        <div class="title-bar-content">
          <iconpark-icon
            v-if="previewIndex !== 0"
            :class="{ 'not-click': previewIndex === 0 }"
            class="bar-icon"
            name="iconleft"
            @click="onSwitchLeft(), (visibleInfoFlag = false)"
          ></iconpark-icon>
          <iconpark-icon
            v-if="previewIndex !== videos.length - 1"
            :class="{
              'not-click': previewIndex === videos.length - 1,
            }"
            class="bar-icon iconrights"
            name="iconright"
            @click="onSwitchRight(), (visibleInfoFlag = false)"
          ></iconpark-icon>
          <iconpark-icon
            v-if="isDow"
            v-show="!videos[previewIndex].isDowFile"
            class="bar-icon"
            name="icondownload-b766bhki"
            @click="dowFile(), (visibleInfoFlag = false)"
          ></iconpark-icon>
          <iconpark-icon
            v-else
            v-show="!videos[previewIndex].isDowFile"
            class="bar-icon"
            name="iconfolderopen-b766bhgc"
            @click="dowFile(), (visibleInfoFlag = false)"
          ></iconpark-icon>

          <fileInfo
            v-if="videos[previewIndex] && visibleInfoFlag"
            v-model:visible="visibleInfoFlag"
            class="bar-icon"
            :objs="videos[previewIndex]"
          ></fileInfo>
        </div>
      </template>
    </title-bar>
    <VideoPlayer
      v-if="videoSrc()&&videos[0]?.url"
      :height="500"
      language="zh-CN"
      :options="{
        language: 'zh-CN',
      }"
      :playback-rates="[0.75, 1, 1.25, 1.5, 2]"
      :src="videoSrc()"
      :poster="getVideoThumbnail(videos[previewIndex].url)"
      :volume="0.6"
      :autoplay="true"
      ref="videoPlayerRef"
      controls
      class="w-full videos"
      @mounted="handleMounted"
      @contextmenu="onRightClick($event)"
    />
    <div
      v-if="showContextMenu && (!videos[previewIndex].isDowFile || !videos[previewIndex].isShareFile)"
      class="menu"
      :style="menuStyle"
    >
      <ul>
        <li v-for="(item, index) in menuItems" v-show="showBtn(item)" :key="index" @click="handleItemClick(item)">
          {{ item }}
        </li>
      </ul>
    </div>
    <div
      v-if="videos.length > 1 && previewIndex !== 0"
      class="m-switch-left hover-block"
      :class="{ 'u-switch-disabled': previewIndex === 0 }"
      @click="onSwitchLeft()"
    >
      <iconpark-icon style="font-size: 48px" name="iconlefe"></iconpark-icon>
    </div>
    <div
      v-if="previewIndex !== videos.length - 1"
      class="m-switch-right hover-block"
      :class="{
        'u-switch-disabled': previewIndex === videos.length - 1,
      }"
      @click="onSwitchRight()"
    >
      <iconpark-icon style="font-size: 48px" name="iconright-b762inpn"></iconpark-icon>
    </div>
    <select-member
      v-model:visible="shareVisible"
      :active-card-id="videos[previewIndex]?.myCardId ? [videos[previewIndex].myCardId] : []"
      :show-my-group-menu="true"
      :show-dropdown-menu="false"
      :change-menus="true"
      :attach="'body'"
      @confirm="shareList"
    />
  </div>
</template>

<script setup lang="ts">
import videojs from "video.js";
import { VideoPlayer } from "@videojs-player/vue";
import "video.js/dist/video-js.css";
import { videoCN } from "@/components/preview/components/videoCN.ts";
console.log(videoCN, "videoCNvideoCNvideoCN");
import fileInfo from "@/components/preview/components/fileInfo.vue";

videojs.addLanguage("zh-cn", videoCN);
// import 'video.js/dist/lang/zh-CN.json';
import selectMember from "@renderer/components/rk-business-component/select-member/common-add-members.vue";
import { getVideoThumbnail, MsgShareType, sendApplicationMsg } from "@renderer/utils/share.ts";
import TitleBar from "@renderer/components/common/viewFileBar.vue";
import { ref, computed, watchEffect, onMounted, onUnmounted, nextTick } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";
import LynkerSDK from "@renderer/_jssdk";
const { t } = useI18n();
const previewIndex = ref(0);
const videos = ref([""]);
let isDow = ref(true);
const visibleInfoFlag = ref(false);
const showContextMenu = ref(null);
const videoPlayerRef = ref(null);
const shareVisible = ref(false);
console.log(videoPlayerRef.value, "videoPlayerRefvideoPlayerRef");

const menuItems = ref([t("square.post.forward"), t("order.saveas")]);
const position = ref({
  top: 0,
  left: 0,
});
const showBtn = (item) => {
  if ((item === t("niche.saveas") || item === t("niche.openFiles")) && !videos.value[previewIndex.value].isDowFile) {
    return true;
  }
  if (item === t("square.post.forward") && !videos.value[previewIndex.value].isShareFile) {
    return true;
  }
  return false;
};
const videoSrc = () => (videos.value[previewIndex.value].url ? videos.value[previewIndex.value].url : false);
const getImageInfo = (includeExtension,flag = false) => {
  let videoInfo = videos.value[previewIndex.value].url;
  console.log(videoInfo, "videoInfovideoInfovideoInfo");
  const fileName = videoInfo.split("?")[0].split("/").pop();
  const fileExtension = includeExtension ? fileName.split(".").pop().toLowerCase() : "";

  return flag ? fileName : fileExtension;
};
const handleMounted = (payload) => {
  videoPlayerRef.value = payload;
  console.log(videoPlayerRef.value.addLanguage, "videoPlayerRef.valuevideoPlayerRef.value");
};
const menuStyle = computed(() => ({
  top: `${position.value.top}px`,
  left: `${position.value.left}px`,
}));
const shareList = (data) => {

  if (data) {
    const video = videos.value[previewIndex.value]
    let objs = {
      videoName: video.title,
      videoUrl: video.url,
      videoImageUrl: getVideoThumbnail(video.url),
      width: video.width ||  videoPlayerRef.value.state?.videoWidth,
      height: video.height ||  videoPlayerRef.value.state?.videoHeight,
      size: video.size,
      type: video.type,
      duration: video.duration || videoPlayerRef.value.state?.duration,
    };
    sendApplicationMsg(MsgShareType.video, objs, data);
    MessagePlugin.success({
      content: t("clouddisk.success"),
      duration: 3000,
      zIndex: 99999,
      offset: ["0", "30"],
    });
  }
};
const getImages = () => {
  console.log(videos.value, "videos.valuevideos.value");
  if (Array.isArray(videos.value)) {
    return videos.value;
  }
  return [videos.value];
};
watchEffect(() => {
  videos.value = getImages();
});

// 监听视频预览状态变化，自动获取焦点
watchEffect(() => {
  if (videos.value[0]?.url) {
    console.log('🔍 [VIDEO-FOCUS] 视频预览窗口打开，自动获取焦点');
    nextTick(() => {
      ensureFocus();
    });
  }
});
// 键盘事件处理函数
const handleKeydown = (event) => {
  const e = event || window.event;
  console.log('🔍 [VIDEO-KEYBOARD] 键盘事件触发 - keyCode:', e.keyCode, 'key:', e.key);

  // 检查是否有输入框获得焦点
  const activeElement = document.activeElement;
  if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
    console.log('🔍 [VIDEO-KEYBOARD] 输入框获得焦点，跳过键盘处理');
    return;
  }

  // 左箭头键或上箭头键 - 切换到上一个视频
  if (e.keyCode === 37 || e.keyCode === 38) {
    console.log('🔍 [VIDEO-KEYBOARD] 左/上箭头键按下');
    e.preventDefault();
    e.stopPropagation();
    onSwitchLeft();
  }

  // 右箭头键或下箭头键 - 切换到下一个视频
  if (e.keyCode === 39 || e.keyCode === 40) {
    console.log('🔍 [VIDEO-KEYBOARD] 右/下箭头键按下');
    e.preventDefault();
    e.stopPropagation();
    onSwitchRight();
  }

  // 空格键 - 播放/暂停
  if (e.keyCode === 32) {
    console.log('🔍 [VIDEO-KEYBOARD] 空格键按下');
    e.preventDefault();
    e.stopPropagation();
    if (videoPlayerRef.value?.state?.playing) {
      videoPlayerRef.value.player.pause();
    } else {
      videoPlayerRef.value?.player?.play();
    }
  }
};

// 确保窗口获得焦点的函数
const ensureFocus = () => {
  console.log('🔍 [VIDEO-FOCUS] 尝试获取焦点');

  // 确保窗口获得焦点
  if (window.focus) {
    window.focus();
  }

  // 确保document获得焦点
  if (document.body && document.body.focus) {
    document.body.focus();
  }

  // 设置tabindex使元素可以获得焦点
  const container = document.querySelector('.video-view-container');
  if (container) {
    container.setAttribute('tabindex', '0');
    (container as HTMLElement).focus();
  }
};

// 窗口焦点事件处理
const handleWindowFocus = () => {
  console.log('🔍 [VIDEO-FOCUS] 窗口获得焦点');
};

const handleWindowBlur = () => {
  console.log('🔍 [VIDEO-FOCUS] 窗口失去焦点');
};

// 在组件挂载时添加键盘事件监听器
onMounted(() => {
  console.log('🔍 [VIDEO-MOUNT] 组件挂载，添加事件监听器');

  // 添加键盘事件监听器（使用capture模式确保优先处理）
  document.addEventListener('keydown', handleKeydown, true);
  window.addEventListener('keydown', handleKeydown, true);

  // 添加窗口焦点事件监听
  window.addEventListener('focus', handleWindowFocus);
  window.addEventListener('blur', handleWindowBlur);

  // 确保初始焦点
  nextTick(() => {
    ensureFocus();
  });
});

// 在组件卸载时移除键盘事件监听器
onUnmounted(() => {
  console.log('🔍 [VIDEO-UNMOUNT] 组件卸载，移除事件监听器');

  // 移除所有事件监听器
  document.removeEventListener('keydown', handleKeydown, true);
  window.removeEventListener('keydown', handleKeydown, true);
  window.removeEventListener('focus', handleWindowFocus);
  window.removeEventListener('blur', handleWindowBlur);
});
// const poster = (
//   () => getVideoThumbnail(videos.value[previewIndex.value].url),
// );
const onSwitchLeft = () => {
  if (previewIndex.value > 0) {
    previewIndex.value--;
    getDBurl(previewIndex.value);
  } else {
    MessagePlugin.error({
      content: t("order.fistimg"),
      duration: 3000,
      zIndex: 99999,
      offset: ["0", "30"],
    });
  }
};
const onSwitchRight = () => {
  if (previewIndex.value < videos.value.length - 1) {
    previewIndex.value++;
    getDBurl(previewIndex.value);
  } else {
    MessagePlugin.error({
      content: t("order.lastimg"),
      duration: 3000,
      zIndex: 99999,
      offset: ["0", "30"],
    });
  }
};
const onRightClick = (event, url) => {
  getDBurl(previewIndex.value);

  if (isDow.value) {
    menuItems.value[1] = t("order.saveas");
  } else {
    menuItems.value[1] = t("order.openFiles");
  }
  // event.preventDefault();
  showContextMenu.value = true;
  const windowHeight = window.innerHeight;
  const windowWidth = window.innerWidth;
  const clickX = event.clientX;
  const clickY = event.clientY;
  let left = clickX;
  let top = clickY;
  if (clickY + 152 > windowHeight) {
    top = clickY - 105;
  }
  if (clickX + 152 > windowWidth) {
    left = windowWidth - 155;
  }
  position.value = { top, left };
};
window.addEventListener("click", () => {
  showContextMenu.value = false;
}); // 右鍵

const handleItemClick = (item) => {
  if (item === t("square.post.forward")) {
    shareVisible.value = true;
    console.log("轉發");
  } else {
    dowFile();
  }
  showContextMenu.value = false;
};

const dowFile = async () => {
  visibleInfoFlag.value = false;
  const image = videos.value[previewIndex.value].url;
  const fileName = getImageInfo(image, true);
  const fileType = getImageInfo(image);
  console.log('====>download-info',{title: fileName, url: image, type:fileType});
  let filePath = null;
  const result = await LynkerSDK.ipcRenderer.invoke("get-dow-record-by-url", image);
  if (result) {
    isDow.value = false;
    const { download_path } = result;
    const fileFlag = await LynkerSDK.ipcRenderer.invoke("open-folder", download_path);

    if (!fileFlag) {
      LynkerSDK.ipcRenderer.invoke("del-dow-record", image);
      isDow.value = true;
      MessagePlugin.error({
        content: t("order.notFilePlaseDownload"),
        duration: 3000,
        offset: ["0", "30"],
      });
    }
  } else {
    isDow.value = true;
    filePath = await LynkerSDK.ipcRenderer.invoke("download-file", {
      title: fileName,
      url: image,
    });

    if (filePath) {
      isDow.value = false;
      LynkerSDK.ipcRenderer.invoke("add-dow-record", {
        url: image,
        download_path: filePath,
        file_name: fileName,
        file_type: fileType,
        file_size: 0,
      });
      MessagePlugin.success({
        content: `${fileName || ""}${t("activity.activity.downloadOverTip")}`,
        duration: 3000,
        offset: ["0", "30"],
      });
    } else if(filePath === false){
      // 取消下载
    }else{
      MessagePlugin.error({
        content: `下载失败`,
        duration: 3000,
        offset: ["0", "30"],
      });
    }
  }
};

LynkerSDK.ipcRenderer.on("clear-video", () => {
  videos.value = [""];
  console.log("clearclearclearclear");
});
LynkerSDK.ipcRenderer.send("getArrayData");
LynkerSDK.ipcRenderer.on("arrayData", (event, array) => {
  console.log(array, "arrayarrayarray");
  if (array) {
    videos.value = array;
    videoSrc();
    if (videos.value[0].url) {
      getDBurl(0);
    }
  }
});
const getDBurl = (val) => {
  LynkerSDK.ipcRenderer.invoke("get-dow-record-by-url", videos.value[val].url).then((result) => {
    console.log(result, "resultresult");
    if (result) {
      isDow.value = false;
    } else {
      isDow.value = true;
    }
  });
};
</script>

<style lang="less" scoped>
:deep(.vjs-menu-item:hover) {
  background-color: transparent !important;
}
:deep(.vjs-control-bar) {
  bottom: 48px;
  width: 557px;
  left: 50%;
  transform: translateX(-50%);
  padding-right: 8px;
  border-radius: 4px;
  background: var(--bg-kyy-color-bg-mask, rgba(0, 0, 0, 0.5));
}
:deep(.vjs-picture-in-picture-control) {
  display: none;
}
:deep(.vjs-fullscreen-control) {
  display: none;
}

:deep(.vjs-remaining-time-display) {
  font-size: 14px;
  color: #fff;
}
:deep(.video-js .vjs-big-play-button){
  margin-left: 0;
}
:deep(.vjs-menu-content) {
  padding: 12px 0px 4px;
  border-radius: 4px;
  background: var(--bg-kyy-color-bg-mask, rgba(0, 0, 0, 0.5));
  line-height: 22px;
  color: var(--text-kyy-color-text-white, #fff);
  max-height: 500px !important;
}
:deep(.vjs-selected) {
  background-color: transparent !important;
  color: var(--brand-kyy-color-brand-default, #4d5eff) !important;
}
:deep(.vjs-menu-item) {
  margin-bottom: 8px;
}
:deep(.vjs-menu) {
  width: 80px;
}
.vjs-icon-placeholder:before {
}
.m-switch-left {
  inset-inline-start: 36px;
  position: fixed;
  inset-block-start: 50%;
  z-index: 1081;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin-top: -20px;
  border-radius: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  transition: all 0.3s;
  pointer-events: auto;
}
.m-switch-right {
  inset-inline-end: 36px;
  position: fixed;
  inset-block-start: 50%;
  z-index: 1081;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin-top: -20px;
  border-radius: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  transition: all 0.3s;
  pointer-events: auto;
}
.iconrights {
  margin-right: 16px;
  position: relative;
  &::after {
    content: "";
    width: 1px;
    height: 16px;
    position: absolute;
    top: 4px;
    right: 0;
    background: #eceff5;
  }
}
.bar-icon {
  margin: 0 12px;
  font-size: 24px;
  cursor: pointer;
  line-height: 24px;
  -webkit-app-region: no-drag;
}
.title-bar-content {
  display: flex;
  align-items: center;
  height: 100%;
  position: absolute;
  justify-content: center;
  .iconrights {
    margin-right: 24px;
    position: relative;
    &::after {
      content: "";
      width: 1px;
      height: 16px;
      position: absolute;
      top: 4px;
      right: -11px;
      background: #d5dbe4;
    }
  }
}
.hover-block {
  opacity: 0;
}
.container-view:hover {
  .hover-block {
    opacity: 1;
  }
}
.container-view {
  height: 100%;
}
.not-click {
  color: #d5dbe4;
}
.menu {
  position: absolute;
  background-color: #f1f1f1;
  border: 1px solid #ddd;
  z-index: 1999999;
  width: 152px;
  display: inline-flex;
  padding: var(--kyy_radius_dropdown_m, 8px);
  align-items: center;
  border-radius: var(--kyy_radius_dropdown_m, 8px);
  background: var(--kyy_color_dropdown_bg_default, #fff);
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
}
ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
li {
  cursor: pointer;
  display: flex;
  height: 32px;
  min-width: 136px;
  min-height: 32px;
  max-height: 32px;
  padding: 0px 16px;
  align-items: center;
  gap: 12px;
  align-self: stretch;
  color: var(--kyy_color_dropdown_text_default, #1a2139);
  font-size: 14px;
  font-weight: 400;
  line-height: 32px; /* 157.143% */
}
.videos {
  height: calc(100% - 56px);
  width: 100%;
}
li:hover {
  border-radius: var(--kyy_radius_dropdown_s, 4px);
  background: var(--kyy_color_dropdown_bg_hover, #f3f6fa);
}
.u-switch-disabled {
  display: none;
}
</style>
