import type {
  AxiosError
} from "axios";
import axios, {
  type AxiosInterceptorManager,
  type InternalAxiosRequestConfig,
  type AxiosRequestConfig
} from "axios"; // 引入axios
import _ from 'lodash';
import CryptoJS from "crypto-js";

import { getAccesstoken, setAccesstoken, getOpenid, removeOpenid, getSMDeviceId } from "@/utils/auth";
import { sendWxMsg } from "@/utils/myUtils";
import { useRouter } from 'vue-router';
const ua = navigator.userAgent;

// import { getProjectTeamID } from "@renderer/views/engineer/utils/auth";
const onHandleError = (error, commonErrMsg?: string) => {
  console.log(error)
  const { data }: any = error.response;
  return Promise.reject(new Error(commonErrMsg || data?.message));
};

type ApiModule =
  | "iam-srv"
  | "im-sync"
  | "client-organize"
  | "organize"
  | "square"
  | "know-srv"
  | "business"
  | "order"
  | "square-operation-manage"
  | "portal"
  | "tools"
  | "global"
  | "website"
  | "ringkol"

type ApiConfig = {
  [key in ApiModule]: string;
};

type RequestIntercept = Parameters<
  AxiosInterceptorManager<InternalAxiosRequestConfig>["use"]
>[0];

// 设置返回解析未json
axios.defaults.headers["Content-Type"] = "application/json";
// 跨域允许 cookie
axios.defaults.withCredentials = false;

function getApiConfig(env: string): ApiConfig {
  if (env === "PRE") {
    return {
      "organize": "https://pre.ringkol.com/organize/api",
      "client-organize": "https://pre.ringkol.com/client/api",
      "im-sync": "https://pre.ringkol.com/im/api",
      "iam-srv": "https://pre.ringkol.com/iam/api",
      "square": "https://pre.ringkol.com/square/api",
      "portal": "https://pre.ringkol.com/portal/api",
      "square-operation-manage":
        "https://pre.ringkol.com/square-operation-manage/api",
      "know-srv": "https://pre.ringkol.com/know/api",
      "business": "https://pre.ringkol.com/business/api",
      "order": "https://pre.ringkol.com/order/api",
      "tools": "https://pre.ringkol.com/tools/api",
      "global": "https://pre.ringkol.com/global/api",
      "website": "https://pre.ringkol.com",
      ringkol: 'https://pre.ringkol.com/ringkol/api',
    };
  }
  if (env === "PROD") {
    return {
      "organize": "https://ringkol.com/organize/api",
      "client-organize": "https://ringkol.com/client/api",
      "im-sync": "https://ringkol.com/im/api",
      "iam-srv": "https://ringkol.com/iam/api",
      "square": "https://ringkol.com/square/api",
      "portal": "https://ringkol.com/portal/api",
      "square-operation-manage": "https://ringkol.com/square-operation-manage/api",
      "know-srv": "https://ringkol.com/know/api",
      "business": "https://ringkol.com/business/api",
      "order": "https://ringkol.com/order/api",
      "tools": "https://ringkol.com/tools/api",
      "global": "https://ringkol.com/global/api",
      "website": "https://ringkol.com",
      ringkol: 'https://ringkol.com/ringkol/api',
    };
  }

  if (env === "QA") {
    return {
      "organize": "https://qa.ringkol.com/organize/api",
      "client-organize": "https://qa.ringkol.com/client/api",
      "im-sync": "https://qa.ringkol.com/im/api",
      "iam-srv": "https://qa.ringkol.com/iam/api",
      // "iam-srv": "http://192.168.31.160:8000",
      "square": "https://qa.ringkol.com/square/api",
      "portal": "https://qa.ringkol.com/portal/api",
      "square-operation-manage": "https://qa.ringkol.com/square-operation-manage/api",
      "know-srv": "https://qa.ringkol.com/know/api",
      "business": "https://qa.ringkol.com/business/api",
      "order": "https://qa.ringkol.com/order/api",
      "tools": "https://qa.ringkol.com/tools/api",
      "global": "https://qa.ringkol.com/global/api",
      "website": "https://qa.ringkol.com",
      ringkol: 'https://qa.ringkol.com/ringkol/api',
    };
  }
  // 默认开发环境
  return {
    "organize": "https://dev.ringkol.com/organize/api",
    "client-organize": "https://dev.ringkol.com/client/api",
    "im-sync": "https://dev.ringkol.com/im/api",
    "iam-srv": "https://dev.ringkol.com/iam/api",
    // "iam-srv": "http://192.168.31.160:8000",
    "square": "https://dev.ringkol.com/square/api",
    "portal": "https://dev.ringkol.com/portal/api",
    "square-operation-manage": "https://dev.ringkol.com/square-operation-manage/api",
    "know-srv": "https://dev.ringkol.com/know/api",
    "business": "https://dev.ringkol.com/business/api",
    "order": "https://dev.ringkol.com/order/api",
    "tools": "https://dev.ringkol.com/tools/api",
    "global": "https://dev.ringkol.com/global/api",
    "website": "https://dev.ringkol.com",
    ringkol: 'https://dev.ringkol.com/ringkol/api',
  };
}


/**
 * 创建 axios 实例
 * @param baseUrl 默认 url
 * @param reqInterceptor 公共请求参数注入处理， 默认不需要传。如果模块需要自定义，可以传入自定义的函数
 * @returns
 */
export function createApiInstance(
  baseUrl: string,
  reqInterceptor?: RequestIntercept,
  onRejected?: (error: AxiosError) => any
) {
  // 创建一个axios实例
  const instance = axios.create({
    baseURL: baseUrl,
    timeout: 10000 // 从5秒调整为15秒
  });
  // 添加响应拦截器
  instance.interceptors.response.use((response) => {
    // 2xx 范围内的状态码都会触发该函数。
    // 对响应数据做点什么
    return response.data;
  }, (error) => {
    // 超出 2xx 范围的状态码都会触发该函数。
    // 对响应错误做点什么
    console.log(error)
    const { config } = error;
    switch (error?.response?.status) {
      case 401:
      case 403:
        //   if(window.RingkolApp) {
        //     window.RingkolAppChannel.postMessage(JSON.stringify({"method": "appTokenExpire", "success": 'window.refreshToken'}))
        //     return new Promise((resolve) => {
        //       // 将resolve放进队列，用一个函数形式来保存，等token刷新后直接执行
        //       window.requestQueue.push((token:string) => {
        //         config.headers.Authorization = `Bearer ${token}`;
        //         resolve(instance(config));
        //       });
        //     });
        //   }
        //   break;
        // case 403:
        if (~ua.indexOf('micromessenger') !== -1) {
          sendWxMsg({
            type: 'FROM_H5',
            status: '403',
            path:'/pages/app-home/index'
          })
        }
        if (window.RingkolApp) {
          window.RingkolAppChannel.postMessage(JSON.stringify({ "method": "appRingkolMethodStatus403" }))
        }
        break;
    }
    if (onRejected) {
      return onRejected(error)
    }
    return Promise.reject(error);
  });

  if (reqInterceptor) {
    instance.interceptors.request.use(reqInterceptor);
  }
  instance.interceptors.request.use((config) => {
    // const lang = { "zh-tc": "zh-hant-MO", "zh-cn": "zh-hans-CN" };
    const curLang = window.localStorage.getItem("lang");
    config.headers["Accept-Language"] = curLang;
    // config.headers["Accept-Language"] = curLang ?
    //     lang[curLang as 'zh-tc' | 'zh-cn'] : "zh-hans-CN";
    if (getAccesstoken()) {
      // console.log(getAccesstoken(), 'set token')
      config.headers.Authorization = `Bearer ${getAccesstoken()}`;
    }
    config.headers["Sm-Device-Id"] = getSMDeviceId() || "";
    config.headers["Phone-Type"] = "web";

    // 签名相关
    // step1 获取URLI
    const url = getQueryUrl(config.url as string, config?.params);
    // 生成一个8位数的随机数
    const random = Number(String(Math.random()).slice(2, 10));
    // teamid,没有必须给空字符串
    const teamId = config.headers?.teamId || config.headers?.teamid || "";
    // timestamp
    const timestamp = Date.now();
    // body
    const body = config?.data ? JSON.stringify(config.data) : "";
    // openid 没有必须给空字符串
    const openid = getOpenid() || "";
    // console.log(`${url}${random}${teamId}${timestamp}${body}${openid}`,'22222222')
    // 生成签名
    const hmac = CryptoJS.HmacSHA256(
      `${url}${random}${teamId}${timestamp}${body}${openid}`,
      openid
    );
    const sign = CryptoJS.enc.Base64.stringify(hmac);
    config.headers.Nonce = random;
    config.headers.TeamId = teamId;
    config.headers.Timestamp = timestamp;
    config.headers.Signature = sign;

    return config;
  })

  return instance;
}

/**
 * 返回对查询参数的 url 字符串（查询参数过滤掉值为 null 和 undefined 的）
 * @param url - 请求地址（相对）
 * @param query - 查询参数对象
 * @returns The url 字符串
 */
const getQueryUrl = (url: string, query?: Record<string, any>): string => {
  if (query && Object.keys(query).length > 0) {
    const queryString = Object.entries(query)
      .filter(([_, value]) => value != null)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
    return `${url}?${queryString}`;
  }
  return url;
};

const Env = import.meta.env.VITE_H5_ENV;

// 获取 base url
export const getBaseUrl = (env: ApiModule) => getApiConfig(Env)[env];

export const iam_srvRequest = createApiInstance(getApiConfig(Env)["iam-srv"]);

export const ringkolRequest = createApiInstance(getApiConfig(Env)["ringkol"]);

export const im_syncRequest = createApiInstance(getApiConfig(Env)["im-sync"]);

export const organizeRequest = createApiInstance(getApiConfig(Env)["organize"]);

export const client_orgRequest = createApiInstance(getApiConfig(Env)["client-organize"]);

// lss 入会、激活会员
export const client_orgRequest_member = createApiInstance(
  getApiConfig(Env)["client-organize"],
  (config) => { return config },
  onHandleError
);
// null,
// (error)=> {
//   const { data, status } = error.response;
//   if(status === 401) {

//   }
// }

export const squareRequest = createApiInstance(getApiConfig(Env)["square"]);
export const portalRequest = createApiInstance(getApiConfig(Env)["portal"]);
export const square_manageRequest = createApiInstance(getApiConfig(Env)["square-operation-manage"]);

export const know_srvRequest = createApiInstance(getApiConfig(Env)["know-srv"]);

export const businessRequest = createApiInstance(getApiConfig(Env)["business"]);

export const orderRequest = createApiInstance(getApiConfig(Env)["order"]);

export const toolsRequest = createApiInstance(getApiConfig(Env)["tools"]);

export const globalRequest = createApiInstance(getApiConfig(Env)["global"]);

window.requestQueue = [];

// 挂载方法到window上，供app端调用
window.refreshToken = (token: string) => {
  setAccesstoken(token);
  window.RingkolApp && updateInstanceToken(token);
}

const updateInstanceToken = (token: string) => {
  [iam_srvRequest, im_syncRequest, organizeRequest, client_orgRequest, client_orgRequest_member, squareRequest, portalRequest, square_manageRequest, know_srvRequest, businessRequest, orderRequest, toolsRequest, globalRequest].forEach(instance => {
    instance.interceptors.request.use((config) => {
      const lang = { "zh-tc": "zh-hant-MO", "zh-cn": "zh-hans-CN" };
      const curLang = window.localStorage.getItem("lang");
      config.headers["Accept-Language"] = curLang ?
        lang[curLang as 'zh-tc' | 'zh-cn'] : "zh-hans-CN";
      config.headers.Authorization = `Bearer ${token}`;
      return config;
    })
  });
  window.requestQueue.forEach((cb) => cb(token));
  window.requestQueue = [];
}
