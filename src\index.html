<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>库存管理系统</title>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --warning-color: #e74c3c;
            --background-color: #f8f9fa;
            --card-bg: #ffffff;
            --text-color: #343a40;
            --border-radius: 12px;
            --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            --transition: all 0.3s ease;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding-bottom: 3rem;
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color), #2980b9);
            color: white;
            padding: 1.5rem 0;
            margin-bottom: 1.5rem;
            box-shadow: var(--box-shadow);
        }

        .header-section h1 {
            font-size: 1.8rem;
            font-weight: 600;
        }

        .filter-bar {
            background-color: var(--card-bg);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            box-shadow: var(--box-shadow);
        }

        .filter-row {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
        }

        .filter-item {
            flex: 1;
            min-width: 120px;
        }

        .form-control, .form-select {
            border-radius: 6px;
            font-size: 0.9rem;
            padding: 0.5rem 0.75rem;
        }

        .inventory-item {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
        }

        .inventory-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.75rem;
        }

        .item-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }

        .item-category {
            font-size: 0.8rem;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            margin-left: 0.5rem;
        }

        .category-day {
            background-color: #e3f2fd;
            color: #0d6efd;
        }

        .category-night {
            background-color: #f8f9fa;
            color: #343a40;
        }

        .item-stats {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 0.75rem;
            margin-bottom: 0.75rem;
        }

        .stat-item {
            background-color: #f8f9fa;
            padding: 0.5rem;
            border-radius: 6px;
            text-align: center;
        }

        .stat-label {
            font-size: 0.75rem;
            color: #6c757d;
            margin-bottom: 0.25rem;
        }

        .stat-value {
            font-weight: 600;
            font-size: 0.9rem;
        }

        .warning {
            color: var(--warning-color);
            font-weight: bold;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .item-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 0.75rem;
            border-top: 1px solid #eee;
        }

        .status-label {
            font-size: 0.9rem;
            display: flex;
            align-items: center;
        }

        .status-label i {
            margin-right: 0.25rem;
        }

        .status-edited {
            color: var(--secondary-color);
        }

        .status-not-edited {
            color: #6c757d;
        }

        .item-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            font-size: 0.9rem;
        }

        .btn {
            border-radius: 8px;
            transition: var(--transition);
            font-weight: 500;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-success {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-warning {
            background-color: #f39c12;
            border-color: #f39c12;
        }

        .modal-content {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), #2980b9);
            color: white;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            padding: 1rem 1.25rem;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.3;
        }

        .action-buttons {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .error-message {
            background-color: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .error-message i {
            margin-right: 0.5rem;
            font-size: 1.2rem;
        }

        .fade-in {
            animation: fadeIn 0.5s;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .highlight {
            animation: highlight 2s;
        }

        @keyframes highlight {
            0% { background-color: rgba(46, 204, 113, 0.2); }
            100% { background-color: transparent; }
        }

        @media (max-width: 576px) {
            .header-section h1 {
                font-size: 1.5rem;
            }

            .action-buttons {
                flex-wrap: wrap;
            }

            .action-buttons .btn {
                flex: 1;
                min-width: 120px;
            }

            .item-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="header-section">
        <div class="container">
            <h1 class="text-center mb-0">库存管理系统</h1>
        </div>
    </div>

    <div class="container">
        <!-- 错误消息区域 -->
        <div id="errorContainer" style="display: none;"></div>

        <!-- 操作按钮区 -->
        <div class="action-buttons">
            <button id="addItem" class="btn btn-primary flex-1">
                <i class="fas fa-plus me-2"></i>添加商品
            </button>
            <button id="exportData" class="btn btn-success">
                <i class="fas fa-file-export me-1"></i>导出
            </button>
            <button id="showImportModal" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#importModal">
                <i class="fas fa-file-import me-1"></i>导入
            </button>
        </div>

        <!-- 筛选搜索面板 -->
        <div class="filter-bar">
            <div class="filter-row">
                <div class="filter-item">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="searchName" placeholder="搜索商品名称">
                    </div>
                </div>
                <div class="filter-item">
                    <select class="form-select" id="filterCategory">
                        <option value="all">白晚班物料</option>
                        <option value="白班物料">白班物料</option>
                        <option value="晚班物料">晚班物料</option>
                    </select>
                </div>
            </div>
            <div class="filter-row">
                <div class="filter-item">
                    <select class="form-select" id="filterTodayEdited">
                        <option value="all">统计与未统计</option>
                        <option value="edited">今日已统计</option>
                        <option value="not_edited">今日未统计</option>
                    </select>
                </div>
                <div class="filter-item">
                    <select class="form-select" id="filterWarning">
                        <option value="all">预警和未预警</option>
                        <option value="warning">仅预警商品</option>
                        <option value="normal">仅正常商品</option>
                    </select>
                </div>
                <div class="filter-item">
                    <select class="form-select" id="filterWarningDays">
                        <option value="all">2日和4日</option>
                        <option value="2">2日预警</option>
                        <option value="4">4日预警</option>
                    </select>
                </div>
                <div class="filter-item" style="min-width: auto;">
                    <button class="btn btn-primary w-100" id="resetFilter">
                        <i class="fas fa-undo"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 库存列表 -->
        <div id="inventoryList" class="fade-in">
            <!-- 加载状态 -->
            <div class="empty-state">
                <i class="fas fa-circle-notch fa-spin"></i>
                <h3>加载中...</h3>
                <p>正在从服务器获取数据</p>
            </div>
        </div>
    </div>

    <!-- 添加/编辑商品模态框 -->
    <div class="modal fade" id="itemModal" tabindex="-1" aria-labelledby="itemModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="itemModalLabel">添加商品</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="itemForm">
                        <input type="hidden" id="itemId">
                        <div class="mb-3">
                            <label for="itemName" class="form-label">名称</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-box"></i></span>
                                <input type="text" class="form-control" id="itemName" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="itemCategory" class="form-label">分类</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-layer-group"></i></span>
                                <select class="form-select" id="itemCategory" required>
                                    <option value="白班物料">白班物料</option>
                                    <option value="晚班物料">晚班物料</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="itemQuantity" class="form-label">库存</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-warehouse"></i></span>
                                <input type="number" class="form-control" id="itemQuantity" min="0" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="itemUnit" class="form-label">单位</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-ruler"></i></span>
                                <input type="text" class="form-control" id="itemUnit" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="itemDailySales" class="form-label">日销</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-chart-line"></i></span>
                                <input type="number" class="form-control" id="itemDailySales" min="0" step="0.01" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="itemWarningDays" class="form-label">预警天数</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-exclamation-circle"></i></span>
                                <select class="form-select" id="itemWarningDays" required>
                                    <option value="2">2天预警</option>
                                    <option value="4" selected>4天预警</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveItem">
                        <span id="saveItemText">保存</span>
                        <span id="saveItemLoading" class="loading" style="display: none;"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入数据模态框 -->
    <div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importModalLabel">导入数据</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="jsonInput" class="form-label">请输入JSON数据</label>
                        <textarea class="form-control" id="jsonInput" rows="6" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="importData">
                        <span id="importDataText"><i class="fas fa-file-import me-2"></i>导入</span>
                        <span id="importDataLoading" class="loading" style="display: none;"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.1/js/bootstrap.bundle.min.js"></script>
    <script>
        // 初始化数据
        let inventory = [];
        let currentId = 0;

        // API 基础URL（与Express服务器对接）
        const API_BASE_URL = 'http://*************:3000/api/inventory';

        // 获取当前日期的字符串表示（YYYY-MM-DD）
        function getCurrentDate() {
            const date = new Date();
            return date.toISOString().split('T')[0];
        }

        // 显示错误消息
        function showError(message) {
            const errorContainer = document.getElementById('errorContainer');
            errorContainer.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>${message}</span>
                </div>
            `;
            errorContainer.style.display = 'block';

            // 5秒后自动隐藏错误消息
            setTimeout(() => {
                errorContainer.style.display = 'none';
            }, 5000);
        }

        // 隐藏错误消息
        function hideError() {
            document.getElementById('errorContainer').style.display = 'none';
        }

        // 从服务器加载数据
        async function loadDataFromServer() {
            try {
                hideError();
                const response = await fetch(API_BASE_URL);

                if (!response.ok) {
                    throw new Error(`服务器响应错误: ${response.status}`);
                }

                const data = await response.json();

                if (data.inventory && Array.isArray(data.inventory)) {
                    inventory = data.inventory;
                    currentId = data.currentId || 0;

                    // 检查是否需要重置今日统计状态（新的一天）
                    checkAndResetDailyStatus();

                    renderInventory();
                } else {
                    throw new Error('服务器返回的数据格式不正确');
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                document.getElementById('inventoryList').innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-exclamation-circle"></i>
                        <h3>加载失败</h3>
                        <p>无法连接到服务器，请稍后重试</p>
                        <button class="btn btn-primary mt-2" onclick="loadDataFromServer()">
                            <i class="fas fa-sync-alt me-1"></i>重试
                        </button>
                    </div>
                `;
                showError(`加载数据失败: ${error.message}`);
            }
        }

        // 保存数据到服务器
        async function saveDataToServer() {
            try {
                const response = await fetch(API_BASE_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        inventory,
                        currentId
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `服务器响应错误: ${response.status}`);
                }

                const result = await response.json();
                return result.success;
            } catch (error) {
                console.error('保存数据失败:', error);
                showError(`保存数据失败: ${error.message}`);
                return false;
            }
        }

        // 检查是否是新的一天，需要重置今日统计状态
        function checkAndResetDailyStatus() {
            const today = getCurrentDate();

            // 查找最近编辑日期
            const lastEditedDates = inventory
                .filter(item => item.lastEditedDate)
                .map(item => item.lastEditedDate);

            const mostRecentDate = lastEditedDates.length > 0
                ? new Date(Math.max(...lastEditedDates.map(d => new Date(d)))).toISOString().split('T')[0]
                : null;

            // 如果是新的一天，重置所有商品的今日统计状态
            if (!mostRecentDate || mostRecentDate !== today) {
                inventory.forEach(item => {
                    item.todayEdited = false;
                });
                // 立即保存更改
                saveDataToServer();
            }
        }

        // DOM元素
        const inventoryList = document.getElementById('inventoryList');
        const itemModal = new bootstrap.Modal(document.getElementById('itemModal'));
        const importModal = new bootstrap.Modal(document.getElementById('importModal'));
        const searchName = document.getElementById('searchName');
        const filterCategory = document.getElementById('filterCategory');
        const filterTodayEdited = document.getElementById('filterTodayEdited');
        const filterWarning = document.getElementById('filterWarning');
        const filterWarningDays = document.getElementById('filterWarningDays');
        const resetFilter = document.getElementById('resetFilter');

        // 筛选参数
        let filters = {
            name: '',
            category: 'all',
            todayEdited: 'all',
            warning: 'all',
            warningDays: 'all'
        };

        // 编辑商品函数
        window.editItemById = function(itemId) {
            const item = inventory.find(item => item.id === itemId);

            if (item) {
                document.getElementById('itemModalLabel').textContent = '编辑商品';
                document.getElementById('itemId').value = item.id;
                document.getElementById('itemName').value = item.name;
                document.getElementById('itemCategory').value = item.category;
                document.getElementById('itemQuantity').value = item.quantity;
                document.getElementById('itemUnit').value = item.unit;
                document.getElementById('itemDailySales').value = item.dailySales;
                document.getElementById('itemWarningDays').value = item.warningDays || 4;

                itemModal.show();
            }
        };

        // 渲染库存列表（列表视图）
        function renderInventory() {
            inventoryList.innerHTML = '';

            // 应用筛选
            const filteredInventory = inventory.filter(item => {
                const warningDays = item.warningDays || 4;
                const warningAmount4Day = item.quantity - item.dailySales * 4;
                const warningAmount2Day = item.quantity - item.dailySales * 2;
                const warningAmount = warningDays === 2 ? warningAmount2Day : warningAmount4Day;
                const isWarning = warningAmount < 0;

                // 名称筛选
                const nameMatch = filters.name === '' ||
                    item.name.toLowerCase().includes(filters.name.toLowerCase());

                // 分类筛选
                const categoryMatch = filters.category === 'all' ||
                    item.category === filters.category;

                // 今日统计筛选
                let todayEditedMatch = true;
                if (filters.todayEdited === 'edited') {
                    todayEditedMatch = item.todayEdited === true;
                } else if (filters.todayEdited === 'not_edited') {
                    todayEditedMatch = item.todayEdited !== true;
                }

                // 预警筛选
                let warningMatch = true;
                if (filters.warning === 'warning') {
                    warningMatch = isWarning;
                } else if (filters.warning === 'normal') {
                    warningMatch = !isWarning;
                }

                // 预警天数筛选
                let warningDaysMatch = true;
                if (filters.warningDays !== 'all') {
                    warningDaysMatch = (item.warningDays || 4) === parseInt(filters.warningDays);
                }

                return nameMatch && categoryMatch && todayEditedMatch && warningMatch && warningDaysMatch;
            });

            if (filteredInventory.length === 0) {
                inventoryList.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-box-open"></i>
                        <h3>暂无匹配的数据</h3>
                        <p>尝试调整筛选条件或添加新商品</p>
                    </div>
                `;
                return;
            }

            // 渲染每个库存项
            filteredInventory.forEach(item => {
                const warningDays = item.warningDays || 4;
                const warningAmount4Day = item.quantity - item.dailySales * 4;
                const warningAmount2Day = item.quantity - item.dailySales * 2;

                const isWarning4Day = warningAmount4Day < 0;
                const isWarning2Day = warningAmount2Day < 0;

                // 格式化数字
                const formatNumber = (num) => {
                    return parseFloat(num.toFixed(10)).toString();
                };

                const itemElement = document.createElement('div');
                itemElement.className = 'inventory-item fade-in';
                itemElement.dataset.id = item.id;

                // 为新添加的项目添加高亮动画
                if (item.isNew) {
                    itemElement.classList.add('highlight');
                    item.isNew = false; // 只高亮一次
                }

                itemElement.innerHTML = `
                    <div class="item-header">
                        <div>
                            <h3 class="item-name">
                                ${item.name}
                                <span class="item-category category-${item.category === '白班物料' ? 'day' : 'night'}">
                                    ${item.category}
                                </span>
                            </h3>
                            <div class="item-inventory">库存: ${item.quantity} ${item.unit}</div>
                        </div>
                    </div>

                    <div class="item-stats">
                        <div class="stat-item">
                            <div class="stat-label">日销量</div>
                            <div class="stat-value">${item.dailySales}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">2日剩余</div>
                            <div class="stat-value ${isWarning2Day ? 'warning' : ''}">${formatNumber(warningAmount2Day)}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">4日剩余</div>
                            <div class="stat-value ${isWarning4Day ? 'warning' : ''}">${formatNumber(warningAmount4Day)}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">预警天数</div>
                            <div class="stat-value">${warningDays}天</div>
                        </div>
                    </div>

                    <div class="item-status">
                        <div class="status-label ${item.todayEdited ? 'status-edited' : 'status-not-edited'}">
                            <i class="fas ${item.todayEdited ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                            ${item.todayEdited ? '今日已统计' : '今日未统计'}
                        </div>
                        <div class="item-actions">
                            <button type="button" class="btn btn-warning action-btn"
                                    onclick="editItemById(${item.id})" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                    </div>
                `;

                inventoryList.appendChild(itemElement);
            });
        }

        // 添加商品按钮事件
        document.getElementById('addItem').addEventListener('click', () => {
            document.getElementById('itemModalLabel').textContent = '添加商品';
            document.getElementById('itemForm').reset();
            document.getElementById('itemId').value = '';
            itemModal.show();
        });

        // 保存商品事件
        document.getElementById('saveItem').addEventListener('click', async () => {
            const itemId = document.getElementById('itemId').value;
            const name = document.getElementById('itemName').value.trim();
            const category = document.getElementById('itemCategory').value;
            const quantity = parseFloat(document.getElementById('itemQuantity').value);
            const unit = document.getElementById('itemUnit').value.trim();
            const dailySales = parseFloat(document.getElementById('itemDailySales').value);
            const warningDays = parseInt(document.getElementById('itemWarningDays').value);

            if (!name || isNaN(quantity) || !unit || isNaN(dailySales) || !warningDays) {
                alert('请填写所有字段');
                return;
            }

            // 显示加载状态
            const saveBtn = document.getElementById('saveItem');
            const saveText = document.getElementById('saveItemText');
            const saveLoading = document.getElementById('saveItemLoading');
            saveBtn.disabled = true;
            saveText.style.display = 'none';
            saveLoading.style.display = 'inline-block';

            try {
                let isNew = false;
                const today = getCurrentDate();

                if (itemId) {
                    // 编辑现有商品 - 自动标记为今日已统计
                    const index = inventory.findIndex(item => item.id === parseInt(itemId));
                    if (index !== -1) {
                        inventory[index] = {
                            ...inventory[index],
                            name,
                            category,
                            quantity,
                            unit,
                            dailySales,
                            warningDays,
                            todayEdited: true,  // 编辑后自动设为已统计
                            lastEditedDate: today  // 记录最后编辑日期
                        };
                    }
                } else {
                    // 添加新商品 - 默认未统计
                    isNew = true;
                    currentId++;
                    inventory.push({
                        id: currentId,
                        name,
                        category,
                        quantity,
                        unit,
                        dailySales,
                        warningDays,
                        todayEdited: false,  // 新商品默认为未统计
                        lastEditedDate: today,  // 记录创建日期
                        isNew: true  // 标记为新添加，用于高亮
                    });
                }

                // 保存到服务器
                const success = await saveDataToServer();
                if (success) {
                    renderInventory();
                    itemModal.hide();
                    showToast(itemId ? '商品已更新并标记为今日已统计' : '商品已添加');
                }
            } finally {
                // 恢复按钮状态
                saveBtn.disabled = false;
                saveText.style.display = 'inline-block';
                saveLoading.style.display = 'none';
            }
        });

        // 导出数据
        document.getElementById('exportData').addEventListener('click', () => {
            const data = JSON.stringify(inventory, null, 2);
            copyToClipboard(data);
        });

        // 复制到剪贴板的兼容性函数
        function copyToClipboard(text) {
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text)
                    .then(() => showToast('数据已复制到剪贴板'))
                    .catch(err => {
                        console.error('无法使用Clipboard API复制:', err);
                        fallbackCopyToClipboard(text);
                    });
            } else {
                fallbackCopyToClipboard(text);
            }
        }

        // 后备复制方法
        function fallbackCopyToClipboard(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            document.body.appendChild(textArea);

            const selected = document.getSelection().rangeCount > 0 ?
                document.getSelection().getRangeAt(0) : false;

            textArea.select();
            let success = false;

            try {
                success = document.execCommand('copy');
                showToast(success ? '数据已复制到剪贴板' : '复制失败，请手动复制');
            } catch (err) {
                console.error('复制失败:', err);
                showToast('复制失败，请手动复制');
            }

            document.body.removeChild(textArea);

            if (selected) {
                document.getSelection().removeAllRanges();
                document.getSelection().addRange(selected);
            }
        }

        // 导入数据
        document.getElementById('importData').addEventListener('click', async () => {
            const jsonInput = document.getElementById('jsonInput').value.trim();

            if (!jsonInput) {
                alert('请输入要导入的JSON数据');
                return;
            }

            // 显示加载状态
            const importBtn = document.getElementById('importData');
            const importText = document.getElementById('importDataText');
            const importLoading = document.getElementById('importDataLoading');
            importBtn.disabled = true;
            importText.style.display = 'none';
            importLoading.style.display = 'inline-block';

            try {
                const data = JSON.parse(jsonInput);
                if (Array.isArray(data)) {
                    const today = getCurrentDate();
                    // 导入时重置今日统计状态为未统计
                    const importedData = data.map(item => ({
                        ...item,
                        todayEdited: false,
                        lastEditedDate: today
                    }));

                    inventory = importedData;
                    currentId = Math.max(...data.map(item => item.id || 0), 0);

                    // 保存到服务器
                    const success = await saveDataToServer();
                    if (success) {
                        renderInventory();
                        importModal.hide();
                        showToast('数据导入成功，所有商品标记为未统计');
                    }
                } else {
                    alert('导入的数据必须是数组格式');
                }
            } catch (error) {
                alert(`JSON格式错误: ${error.message}`);
                console.error('JSON解析错误:', error);
            } finally {
                // 恢复按钮状态
                importBtn.disabled = false;
                importText.style.display = 'inline-block';
                importLoading.style.display = 'none';
            }
        });

        // 显示提示消息
        function showToast(message) {
            const toastContainer = document.createElement('div');
            toastContainer.style.position = 'fixed';
            toastContainer.style.bottom = '20px';
            toastContainer.style.right = '20px';
            toastContainer.style.zIndex = '9999';

            const toast = document.createElement('div');
            toast.className = 'toast show';
            toast.style.backgroundColor = '#fff';
            toast.style.boxShadow = '0 0.5rem 1rem rgba(0, 0, 0, 0.15)';
            toast.style.borderRadius = '0.25rem';
            toast.style.minWidth = '200px';

            toast.innerHTML = `
                <div class="toast-header bg-primary text-white">
                    <strong class="me-auto">提示</strong>
                    <button type="button" class="btn-close" onclick="this.parentElement.parentElement.parentElement.remove()"></button>
                </div>
                <div class="toast-body">${message}</div>
            `;

            toastContainer.appendChild(toast);
            document.body.appendChild(toastContainer);

            setTimeout(() => {
                toastContainer.remove();
            }, 3000);
        }

        // 添加筛选事件监听
        searchName.addEventListener('input', function() {
            filters.name = this.value;
            renderInventory();
        });

        filterCategory.addEventListener('change', function() {
            filters.category = this.value;
            renderInventory();
        });

        filterTodayEdited.addEventListener('change', function() {
            filters.todayEdited = this.value;
            renderInventory();
        });

        filterWarning.addEventListener('change', function() {
            filters.warning = this.value;
            renderInventory();
        });

        filterWarningDays.addEventListener('change', function() {
            filters.warningDays = this.value;
            renderInventory();
        });

        resetFilter.addEventListener('click', function() {
            searchName.value = '';
            filterCategory.value = 'all';
            filterTodayEdited.value = 'all';
            filterWarning.value = 'all';
            filterWarningDays.value = 'all';
            filters = {
                name: '',
                category: 'all',
                todayEdited: 'all',
                warning: 'all',
                warningDays: 'all'
            };
            renderInventory();
        });

        // 初始加载 - 从服务器获取数据
        loadDataFromServer();
    </script>
</body>
</html>
