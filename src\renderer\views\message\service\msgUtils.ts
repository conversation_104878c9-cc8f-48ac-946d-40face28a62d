import { toRaw } from 'vue';
import moment from 'moment';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { logHandler } from '@renderer/log';
import { getAllCards } from '@renderer/components/selectMember/group-data';
import { cardIdType } from '@renderer/views/identitycard/data';
import { SceneTypeArr as AnnoSceneTypeArr } from '@renderer/views/message/chat/msgTypeContent/AppWorks/Anno/constant';
import {
  SceneTypeArr as AdSceneTypeArr,
  getTitle as AdSceneTypeGetTitle,
} from '@renderer/views/message/chat/msgTypeContent/AppWorks/AdCard/constant';
import {
  SceneTypeArr as FeePackageSceneTypeArr,
  getTitle as FeePackageSceneGetTitle,
} from '@renderer/views/message/chat/msgTypeContent/AppWorks/FeePackage/constant';
import {
  SceneTypeArr as PopularizeSceneTypeArr,
  getTitle as PopularizeSceneAdSceneTypeGetTitle,
} from '@renderer/views/message/chat/msgTypeContent/AppWorks/PopularizeCard/constant';
import {
  SceneTypeArr as SceneTypeArrVisitor,
  titleMap,
} from '@renderer/views/message/chat/msgTypeContent/AppWorks/Visitor/constant';
import {
  PolicySceneTypeArr, getPolicyTitle
} from '@renderer/views/message/chat/msgTypeContent/AppWorks/Policy/constant';
import { SceneTypeArr as SceneTypeArrAdministratorAppy, titleMap as titleMapAdministratorAppy } from '@renderer/views/message/chat/msgTypeContent/square/administrator-appy/constant';
import { getTitle as getForumTitle } from '@renderer/views/message/chat/msgTypeContent/Forum/constant';
import { SceneTypeActivityArr, getActivityTitle } from '@renderer/views/message/chat/msgTypeContent/Activity/constant';
import { getTitle as getActivityPromoteTitle } from '@renderer/views/message/chat/msgTypeContent/components/activity-promote/composables/useSceneData';
import { summaryMap } from '@renderer/views/message/chat/common/constant';
import { getAppStoreText } from '@renderer/views/message/chat/msgTypeContent/shop/constant';
import {
  OfflinePush,
} from '@rk/im-sdk/dist/types/entity';
import {CultureSceneTypeArr, getTitle as CultureSceneTypeGetTitle} from '@renderer/views/message/chat/msgTypeContent/AppWorks/CultureTourism/constant';
import { SpServerSceneTypeArr, getTitle as SpServerSceneTypeGetTitle } from '@renderer/views/message/chat/msgTypeContent/AppWorks/SpServer/constant';
import { getTitle as geTitleOrginaze } from '@renderer/views/message/chat/msgTypeContent/AppWorks/constant';
import { fileDetail, DiskFileList } from '@renderer/api/cloud';
import { getImCardIds, getOpenid, getProfilesInfo } from '@renderer/utils/auth';
import { goToAdmin as goToAdminMember } from '@renderer/views/member/utils/auth';
import { goToAdmin as goToAdminPolitics } from '@renderer/views/politics/utils/auth';
import { EmojiData, emojiRegex, CocoEmojiRegex, CocoEmojiData, EmojiObject } from '@/assets/im/emoji';
import { useMessageStore } from './store';
import { i18nt } from '@/i18n';
import { setAccountAuthRouters } from '@/utils/auth';
import MsgWrapper from './message';
import { statusTitle } from '../chat/msgTypeContent/AppPartner';
import { getYearMonthDay } from '../scene/ranking/utils';
import { CHROME_SUPPORT_IMG_TYPES } from '@/constants/common';
import { MsgShareType } from '@/utils/share';
import LynkerSDK from '@renderer/_jssdk';
import { getIdleTitle } from '../chat/msgTypeContent/Idle/constant';

const { ipcRenderer } = LynkerSDK;
const anchorme = require('rk-anchorme').default;

/**
 * 本地消息类型
 * 本地消息不会通过网络发送，仅用于本地展示
 */
export enum ClientMsg {
  /**
   * 对方设置了隐私，发送消息失败后的提示
   */
  blacklist = 'client_blacklist',
}

/**
 * 是否是本地消息
 * @param msg
 * @returns
 */
export const isClientMsg = (msg: MessageToSave | string) => {
  const clientMsgTypes = Object.values(ClientMsg) as string[];
  if (typeof msg === 'string') {
    return clientMsgTypes.includes(msg);
  }
  const type = msg?.contentExtra?.contentType;
  return clientMsgTypes.includes(type);
};

export async function msgJumpToCloudDisk(data) {
  try {
    if (data.disk_id) {
      await DiskFileList(data.disk_id);
    }
    if (data.file_id) {
      await fileDetail(data.file_id);
    }
    await setAccountAuthRouters('click-menu-item');
    ipcRenderer
      .invoke('click-menu-item', {
        url: '/clouddiskIndex/clouddiskhome',
        cloudDiskJumpId: data.disk_id, // 跳转到云盘的参数云盘id
        cloudDiskFileId: data.file_id, // 跳转到云盘的文件夹id
        teamId: data.teamId, // 跳转到云盘的组织id
        cloudDiskMB: data.cloudDiskMB || false, // 判断是否是跳转到管理后台
      })
      .then((res) => {
        if (res) {
          ipcRenderer.send('update-nume-index', 'disk');
        }
      });
  } catch (err) {
    MessagePlugin.error(err.response.data.message);
  }
}

// let params = {
//   from: "message",
//   redirect: `/PManage/PRegularMemberPanel/PActiveMemberPanel`,
//   // t: +new Date(),
//   teamId: '553619463718338560'
// };
// 跳转到数字商协会
export const msgJumpToMember = async (params) => {
  goToAdminMember(params?.teamId, { origin: 'message', redirect: params?.redirect });

  // await setAccountAuthRouters("click-menu-item");
  // // const profile = getProfilesInfo();
  // // PActiveMemberPanel, // 激活会员
  // // PApplyMemberPanel, // 入会申请
  // ipcRenderer.invoke("delect-memberWinBV");
  // ipcRenderer
  //   .invoke("click-menu-item", {
  //     url: "/memberIndex/member_number",
  //     query: params,
  //   })
  //   .then((res) => {
  //     if (res) {
  //       ipcRenderer.send("update-nume-index", 'member');
  //     }
  //   });
};

// 跳转到数字城市
export const msgJumpToGovernment = async (params) => {
  goToAdminPolitics(params?.teamId, { origin: 'message', redirect: params?.redirect });

  // await setAccountAuthRouters("click-menu-item");
  // ipcRenderer.invoke("delect-politicsWinBV");
  // ipcRenderer
  //   .invoke("click-menu-item", {
  //     url: "/politicsIndex/politics_number",
  //     query: params,
  //   })
  //   .then((res) => {
  //     if (res) {
  //       ipcRenderer.send("update-nume-index", 'government');
  //     }
  //   });
};

export async function msgJumpToApprove(data) {
  console.log(data, '参数');
  await setAccountAuthRouters('click-menu-item');
  const params = {
    dataid: data.dataid,
    id: data.id,
    departmentId: data.departmentId,
    jobId: data.jobId,
    staffId: data.staffId,
    isIm: true,
    projectId: data.projectId,
    jumpPath: '/workBenchIndex/approve_home',
    activationGroupItemTeamId: data.teamId,
    teamId: data.teamId,
    uuid: 'approve',
    toAdmin: true,
  };
  ipcRenderer
    .invoke('click-menu-item', {
      url: '/workBenchIndex/workBenchHome',
      teamId: data.teamId,
      activationGroupItemTeamId: data.teamId,
      query: params,
      reload: true,
    })
    .then((res) => {
      console.log(data, 'datadatadatadata');
      if (res) {
        ipcRenderer.send('update-nume-index', 'workBench');
      }
    });
}

// 群记录入口跳转
export async function msgGroupJumpToCloudDisk(data) {
  await setAccountAuthRouters('click-menu-item');
  ipcRenderer
    .invoke('click-menu-item', {
      url: '/clouddiskIndex/clouddiskhome',
      teamId: data.teamId, // 跳转到云盘的组织id
      cloudDiskFileId: data.file_id, // 跳转到云盘的文件夹id
    })
    .then((res) => {
      console.log(data, 'datadatadatadata');

      if (res) {
        ipcRenderer.send('update-nume-index', 'disk');
      }
    });
}

type MsgImgSize = {
  maxWidth: number;
  maxHeight: number;
  minWidth: number;
  minHeight: number;
  width?: number;
  height?: number;
};

const getScaleMin = (vScaled: number, limit = { max: 280, min: 40 }) => {
  if (vScaled > limit.max) {
    return limit.max;
  }
  if (vScaled < limit.min) {
    return limit.min;
  }
  return vScaled;
};

const getScaleSize = (big: number, less: number, limit = { max: 280, min: 40 }) => {
  if (big > limit.max) {
    const scale = big / limit.max;
    return { max: limit.max, min: getScaleMin(Math.round(less / scale), limit) };
  }
  if (big <= limit.max && big >= limit.min) {
    if (less >= limit.min) {
      return { max: big, min: less };
    }

    const max = Math.min(Math.round(big * (limit.min / less)), limit.max);
    return { max, min: limit.min };
  }
  const max = Math.min(Math.round(big * (limit.min / less)), limit.max);
  return { max, min: limit.min };
};

export function getImgScaleSize(width: number, height: number, limitSize = { max: 280, min: 40 }): MsgImgSize {
  const limitStyle: MsgImgSize = {
    maxWidth: limitSize.max,
    maxHeight: limitSize.max,
    minWidth: limitSize.min,
    minHeight: limitSize.min,
  };
  if (Number.isNaN(width) || Number.isNaN(height)) {
    return limitStyle;
  }

  // 缩放
  if (width > height) {
    const { max, min } = getScaleSize(width, height, limitSize);
    Object.assign(limitStyle, { width: max, height: min });
  } else {
    const { max, min } = getScaleSize(height, width, limitSize);
    Object.assign(limitStyle, { width: min, height: max });
  }
  return limitStyle;
}

// 获取消息图片展示的宽高
export function getImageStyle(width: number, height: number, limitSize = { max: 280, min: 40 }, extend?) {
  // if((!width || !height) && limitSize?.src){
  //   let img = new Image()
  //   img.src = limitSize.src
  //   img.onload = function () {
  //     console.log('=====>',img.width,img.height);
  //     if(img.width > img.height){
  //       imgSize = {width:limitSize.max + 'px',height:limitSize.max * img.height/img.width + 'px' }
  //     }else{
  //       imgSize = {height:limitSize.max + 'px',width:limitSize.max * img.width/img.height + 'px' }
  //     }
  //     console.log('=====>',imgSize);
  //     return imgSize
  //   }
  // }
  if (extend?.onlyHeight) {
    return { height: `${extend.onlyHeight}px` };
  }
  if (!width || !height) {
    return { minWidth: `${limitSize.min}px`, minHeight: `${limitSize.min}px` };
  }
  const imgSize = getImgScaleSize(width, height, limitSize);
  const sizeInfo: { [key in keyof MsgImgSize]?: string } = {};
  for (const key in imgSize) {
    sizeInfo[key] = `${imgSize[key]}px`;
  }
  return sizeInfo;
}

export function getLongImageStyle(width: number, height: number, limitSize = { height: 280, width: 280 }) {
  const w = Math.min(width, limitSize.width);
  return {
    width: `${w}px`,
    height: `${limitSize.height}px`,
    maxWidth: `${limitSize.width}`,
    maxHeight: `${limitSize.height}`,
  };
}
// 新增HTML转义方法
const escapeHtml = (unsafe: string) => {
  return unsafe.replace(/[&<"'>]/g, (match) => ({
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;'
  }[match] || match));
};
/**
 * 处理@消息文本，去除结尾的~
 * @param text 需要处理的文本
 * @returns 处理后的文本
 */
const removeAtTilde = (text: string, from?) => {
  if(from === 'offline'){
    return text.replace(/@[^~]*~/g, (match) => match.slice(0, -1));
  }
  return escapeHtml(text.replace(/@[^~]*~/g, (match) => match.slice(0, -1)));
};
// 用于会话列表消息展示
export const getConversationMessagePreview = (msg: MessageToSave, mycard?: string, from?: string): string => {
  try {
    const msgType = msg.messageType;
    if (msgType === 2101 || msgType === 111) {
      return mycard === msg.contentExtra.senderId ? i18nt('im.public.recall_self') : `[${i18nt('im.public.recall_peer2')}]`;
    }

    if (msgType !== 'text' && msgType !== 114) {
      return `[${i18nt('im.public.unsupport')}]`;
    }

    const type = msg.contentExtra?.contentType || msg.contentExtra?.type;
    const data = msg.contentExtra?.data;
    const scene = msg.contentExtra?.scene;
    switch (type) {
      case 'text':
        if(data.text){
          return removeAtTilde(data.text, from);
        }
        console.log('===>msg,msg.contentExtra?.data.text', msg);
        return'';
      case 'richText':
        return getRichText(data?.text ?? '')
      case 'image':
        return `[${i18nt('im.public.img')}]`;
      case 'emoji_image':
        return `[${i18nt('im.public.emoji')}]`;
      case 'emoji_coco_image':
        return `[${i18nt('im.public.emoji')}]`;
      case 'voice':
        return `[${i18nt('im.public.voice')}]`;
      case 'video':
        return `[${i18nt('im.public.video')}]`;
      case 'file':
        return `[${i18nt('im.public.file')}]${data?.fileName || ''}`;
      case 'location':
        return `[${i18nt('im.public.location')}]${data?.title || ''}`;
      case 'merge_forward':
        return `[${i18nt('im.public.chat_record')}]`;
      case 'card_id_card':
        return `[${i18nt('im.public.card')}]`;
      case 'cloud_disk':
        return `[${i18nt('im.public.cloud_file')}]${data?.title ?? ''}`;
      case 'zx_remind_detail':
        return `[${i18nt('im.public.remind')}]${data?.title ?? ''}`;
      case 'zx_notes_detail':
        return `[${i18nt('im.public.note')}]${data?.title ?? ''}`;
      case 'zx_list_detail':
        return `[${i18nt('im.public.zxlist')}]${data?.title ?? ''}`;
      case 'zx_schedule':
        return `[${i18nt('im.public.schedule')}]`;
      case 'approve':
        return `[${i18nt('im.public.approve')}]${data?.approveTitle ?? ''}`;
      case 'approve_to_rel_people':
        return `[${i18nt('im.public.approve')}]${data?.commentText ?? ''}`;
      case 'square':
        return `[${i18nt('im.public.square_moment')}] ${data?.post?.text ?? ''}`;
      case 'square_idcard':
        return `[${i18nt('im.public.square_id')}]`;
      case 'biz_opportunity':
        return `[${i18nt('im.msg.biz')}]`;

      case 'square_preview':
        return `[${i18nt('im.public.square_preview')}]`;

      case 'square_light_album':
        return `[${i18nt('im.public.album')}]`;

      case 'sharedPartner':
        return `${data.title ?? ''}`;
      case 'APP_ACTIVITY':
        if (SceneTypeActivityArr.includes(scene)) {
          return getActivityTitle(scene, msg.contentExtra);
        }
        if ([20052].includes(scene)) {
          return getActivityPromoteTitle(data.promotion_message_type);
        }
        return `${data?.who ?? ''}${i18nt('im.public.invite')}[${data?.title ?? data?.subject ?? ''}]`;
      case 'APP_ORDER_ASSISTANT':
      case 'APP_CITY_PROXY':
        return `${data?.content?.title || ''}`;
      case 'activity_card':
        return `${data?.who ?? ''}${i18nt('im.public.invite')}[${data?.title ?? data?.subject ?? '活动'}]`;
      case 'meeting_start':
        return getMeetingStartText(msg);
      case 'meeting_end':
        return getMeetingEndText(msg, mycard);
      case 'meeting':
        return getMeetingEndText(msg, mycard);

      case 'server_video_call_finish':
        return getMeetingTimeText(data?.call_time);
      case 'server_message_middle':
        return getServerMiddleMsgText(msg, mycard);
      case 'server_message_middle_weather':
        return `[${i18nt('im.public.day_weather')}]`;
      case 'server_message_middle_birthday':
        return `[${i18nt('im.tools.birthday')}]`;

      case 'group_notice':
        return `[${i18nt('im.groupNotice.groupNotice')}]`;
      case 'SportStepsEnabled':
        return `${i18nt('im.msg.joinRank', [data?.who?.title ?? ''])}`;
      case 'SportPlanAllUpToPar':
        return i18nt('im.msg.rankRequire');
      case 'SportStepsRanking':
        return i18nt('im.msg.rank1', [data?.top1?.title ?? '', data?.date ? getYearMonthDay(data.date * 1000) : '']);
      case 'SportPlanCreated':
        return data?.details?.title ?? '';
      case 'PredefinedDayReminder':
        return data?.title || '';
      case 'DayReminder':
        return data?.title || '';

      case ClientMsg.blacklist:
        return getServerMiddleMsgText(msg, mycard);

      case 'APP_CLOUD_DISK':
        return data?.content?.title || '';
      case 'ActivityInvitation':
        return `${data?.who ?? ''}${i18nt('im.public.invite')}[${data?.title ?? data?.subject ?? ''}]`;
      case 'APP_APPROVAL':
        return data?.content?.title || '';
      case 'APP_TEAMS':
        return data?.content?.title || '';
      case 'APP_BUSINESS':
        return data?.content?.title || '';
      case 'APP_NICHE':
        return data?.content?.title || '';
      case 'APP_SQUARE':
        return getAppSquareText(msg);
      case 'APP_KNOW':
        return getAppKnowText(msg);
      case 'APP_ADDRESS_BOOK':
        return `[${i18nt('im.public.friend_notify')}]`;
      case 'APP_SECRETARY':
        return getAppSecretaryText(msg);
      case 'APP_PARTNER':
        return `[${appPartnerText(scene, data?.content)}]`;
      case 'help_center':
        return `${data?.title ?? ''}`;
      case 'service_content':
        return `${data?.title ?? ''}`;
      case MsgShareType.consult:
        return `[${i18nt('order.consultOrder')}]`;
      case MsgShareType.consult_order:
        return `[${i18nt('order.zf')}${i18nt('order.order')}]`;
      case MsgShareType.about_us_experience:
      case MsgShareType.about_us_honor:
      case MsgShareType.about_us_organize:
      case MsgShareType.party_building:
      case MsgShareType.platform_charm:
        return `[${summaryMap[type]}]`;
      case 'APP_SERVE':
        return `${data?.header?.title ?? ''}`;
      case 'APP_WORKS':
        return getAppWorksText(msg);
      case 'APP_BBS':
        return getForumTitle(scene);
      case 'digital_card':
        return `[${i18nt('im.public.vcard')}] 我是${data.name},这是我的名片`;
      case 'APP_MEETING_MSG':
        return data?.media_type === 'audio' ? '[语音通话]' : '[视频通话]';
      case 'APP_DIGITAL_BUSINESS':
        return `${data.content.title}`;
      case  MsgShareType.usage_tips:
        return `【使用技巧】`;
      case 'APP_PENDING':
        return `${data?.content}`;
      case 'APP_STORE_ASSISTANT':
        return getAppStoreText(scene, data?.content?.title);
      case 'APP_AD_ASSISTANT':
        return '广告审核结果通知';
      case 'APP_IDLE_ASSISTANT':
        return getIdleTitle(scene, msg.contentExtra);
      default:
        console.log('====>不支持列表消息展示', type, msg);
        return `[${i18nt('im.public.unsupport')}${type}]`;
    }
  } catch (error) {
      console.log('====>不支持列表消息展示', error);
  }
};

const appPartnerText = (scene, content) => statusTitle(scene, content);

// 用于消息列表引用消息展示
export const getReferMessagePreview = (contentExtra: MessageToSave['contentExtra']) => {
  const data = contentExtra?.data;
  const type = contentExtra?.contentType;
  switch (type) {
    case 'text':
      return getParsedText(data?.text ?? '', true)
        .map((item) => item.str)
        .join('');
    case 'image':
      return `[${i18nt('im.public.img')}]`;
    case 'emoji_image':
      return `[${i18nt('im.public.emoji')}]`;
    case 'emoji_coco_image':
      return `[${i18nt('im.public.emoji')}]`;
    case 'voice':
      return `[${i18nt('im.public.voice')}]`;
    case 'video':
      return `[${i18nt('im.public.video')}]`;
    case 'file':
      return `[${i18nt('im.public.file')}]${data?.fileName || ''}`;
    case 'location':
      return `[${i18nt('im.public.location')}]${data?.title || ''}`;
    case 'merge_forward':
      return `[${i18nt('im.public.chat_record')}]`;
    case 'card_id_card':
      return `[${i18nt('im.public.card')}]`;
    case 'cloud_disk':
      return `[${i18nt('im.public.cloud_file')}]${data?.title ?? ''}`;
    case 'zx_remind_detail':
      return `[${i18nt('im.public.remind')}]${data?.title ?? ''}`;
    case 'zx_notes_detail':
      return `[${i18nt('im.public.note')}]${data?.title ?? ''}`;
    case 'zx_list_detail':
      return `[${i18nt('im.public.zxlist')}]${data?.title ?? ''}`;
    case 'zx_schedule':
      return `[${i18nt('im.public.schedule')}]`;
    case 'approve':
      return `[${i18nt('im.public.approve')}]${data?.approveTitle ?? ''}`;
    case 'approve_to_rel_people':
      return `[${i18nt('im.public.approve')}]${data?.commentText ?? ''}`;
    case 'square':
      return `[${i18nt('im.public.square_moment')}] ${data?.post?.text ?? ''}`;
    case 'square_idcard':
      return `[${i18nt('im.public.square_id')}]`;
    case 'biz_opportunity':
      return `[${i18nt('im.msg.biz')}]`;

    case 'square_preview':
      return `[${i18nt('im.public.square_preview')}]`;

    case 'square_light_album':
      return `[${i18nt('im.public.album')}]`;

    case 'sharedPartner':
      return `${data.title ?? ''}`;
    case 'activity_card':
      console.log('====>activity_card', contentExtra);
      return `${data?.who ?? ''}${i18nt('im.public.invite')}[${data?.title ?? data?.subject ?? ''}]`;
    case 'group_notice':
      return `[${i18nt('im.groupNotice.groupNotice')}]`;
    case 'help_center':
      return `[${i18nt('im.msg.help')}] ${data?.title ?? ''}`;
    case 'service_content':
      return `${data?.title ?? ''}`;
    case MsgShareType.consult:
      return `[${i18nt('order.consultOrder')}]`;
    case MsgShareType.consult_order:
      return `[${i18nt('order.zf')}${i18nt('order.order')}]`;
    case 'APP_DIGITAL_BUSINESS':
      return `${data.content.title}`;
    case 'digital_card':
      return `[${i18nt('vcard.numberVcard')}]`;
    case 'APP_MEETING_MSG':
      const text = data.media_type === 'audio' ? '[语音通话]' : '[视频通话]';
      return text;
    case 'richText':
      return getRichText(data?.text ?? '');
    default:
      return `[${i18nt('im.public.unsupport')}]`;
  }
};



// 用于合并转发消息列表消息展示
export const getForwardMessagePreview = (msg: MessageToSave, myCard: string) => {
  const data = msg.contentExtra?.data;
  const type = msg.contentExtra?.contentType;
  console.log(type, '啊实打实大师大师大师3333');

  switch (type) {
    case 'text':
      return getParsedText(data?.text ?? '', true)
        .map((item) => item.str)
        .join('');
    case 'image':
      return `[${i18nt('im.public.img')}]`;
    case 'emoji_image':
      return `[${i18nt('im.public.emoji')}]`;
    case 'emoji_coco_image':
      return `[${i18nt('im.public.emoji')}]`;
    case 'voice':
      return `[${i18nt('im.public.voice')}]`;
    case 'video':
      return `[${i18nt('im.public.video')}]`;
    case 'file':
      return `[${i18nt('im.public.file')}]${data?.fileName || ''}`;
    case 'location':
      return `[${i18nt('im.public.location')}]${data?.title || ''} `;
    case 'merge_forward':
      return `[${i18nt('im.public.chat_record')}]`;
    case 'card_id_card':
      return `[${i18nt('im.public.card')}]`;
    case 'cloud_disk':
      return `[${i18nt('im.public.cloud_file')}]${data?.title ?? ''}`;
    case 'zx_remind_detail':
      return `[${i18nt('im.public.remind')}]${data?.title ?? ''}`;
    case 'zx_notes_detail':
      return `[${i18nt('im.public.note')}]${data?.title ?? ''}`;
    case 'zx_list_detail':
      return `[${i18nt('im.public.zxlist')}]${data?.title ?? ''}`;
    case 'zx_schedule':
      return `[${i18nt('im.public.schedule')}]`;
    case 'approve':
      return `[${i18nt('im.public.approve')}]${data?.approveTitle ?? ''}`;
    case 'approve_to_rel_people':
      return `[${i18nt('im.public.approve')}]${data?.commentText ?? ''}`;
    case 'square':
      return `[${i18nt('im.public.square_moment')}] ${data?.post?.text ?? ''}`;
    case 'square_idcard':
      return `[${i18nt('im.public.square_id')}]`;
    case 'square_preview':
      return `[${i18nt('im.public.square_preview')}]`;
    case 'square_light_album':
      return `[${i18nt('im.public.album')}]`;
    case 'sharedPartner':
      return `${data.title ?? ''}`;
    case 'activity_card':
      return `${data?.who ?? ''}${i18nt('im.public.invite')}[${data?.title ?? data?.subject ?? ''}]`;
    case 'help_center':
      return `[${i18nt('im.msg.help')}] ${data?.title ?? ''}`;
    case 'service_content':
      return `${data?.title ?? ''}`;
    case 'APP_MEETING_MSG':
      return data?.media_type === 'audio' ? '[语音通话]' : '[视频通话]';
    case 'APP_DIGITAL_BUSINESS':
      return `${data.content.title}`;
    default:
      return `[${i18nt('im.public.unsupport')}]`;
  }
};

// 获取可读性的文件大小
export function getReadableFileSize(size: number, places = 2) {
  if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(places)}KB`;
  }
  if (size < 1024 * 1024 * 1024) {
    return `${(size / 1024 / 1024).toFixed(places)}MB`;
  }
  return `${(size / 1024 / 1024 / 1024).toFixed(places)}GB`;
}

/**
 * 获取字符串尾部子串
 * @param str 字符串
 * @param length 子串长度
 * @returns 子串
 */
export function getEndSubstring(str: string, length = 2) {
  if (!str || str.length < length) {
    return str ?? '';
  }

  // const namestr = str.replace(
  //   /[`~_!@#$^&*()=|{}':;',?~！@#￥……&*（）——|{}【】'；：""'。，、？\s]/g,
  //   ""
  // );
  const isletter = /^[a-zA-Z\s]+$/.test(str);
  if (isletter) {
    const letterArr = str.split(' ');
    if (letterArr?.length === 1) {
      return str.slice(0, length);
    }
    return `${letterArr[0]?.slice(0, 1)}${letterArr[1]?.slice(0, 1)}`;
  }
  return str.slice(-length);

  // // 获取最后一个UTF-16编码单元,即一个表情或字符
  // const lastCodePoint = str.codePointAt(str.length - 1);
  // // 如果最后一个UTF-16编码单元不是组合表情的一部分
  // if (lastCodePoint < 0x1f3fb) {
  //   const lastChar = String.fromCodePoint(lastCodePoint);
  //   const secondLastChar = str.slice(-length, -1);
  //   return secondLastChar + lastChar;
  // }

  // // 获取最后两个UTF-16编码单元,组合成一个完整的表情
  // const lastTwoCodePoints = [];
  // lastTwoCodePoints.push(str.codePointAt(str.length - 1), str.codePointAt(str.length - 2));

  // return String.fromCodePoint(...lastTwoCodePoints);
}

export function isMsgContentSame(msg1: MessageToSave, msg2: MessageToSave) {
  if (msg1?.contentExtra?.contentType !== msg2?.contentExtra?.contentType) {
    return false;
  }

  if (!['emoji_coco_image','text'].includes(msg1.contentExtra?.contentType)) {
    return false;
  }

  if (!msg1.contentExtra?.data?.text || msg1.contentExtra?.data?.text !== msg2.contentExtra?.data?.text) {
    return false;
  }

  return true;
}

// 仅群聊，文字，默认表情消息，且长度小于 100， 可以合并
export function isMsgCanMerge(msg: MessageToSave) {
  if (msg.sentStatus === 20) {
    return false;
  }

  if (msg.contentExtra?.source === 'server_rl') {
    return false;
  }

  // 新增撤回类型 回复消息 也不可合并
  if ([2101,111,114].includes(msg.messageType) && msg?.contentExtra?.contentType === 'text') {
    return false;
  }

  if (msg.messageType !== 'text' && msg?.contentExtra?.contentType !== 'text') {
    return false;
  }

  if (msg?.contentExtra?.data?.text?.length > 1000) {
    return false;
  }

  return true;
}
function addSpaceBeforeFirstChineseWithoutEqual(str) {
  // 查找第一个汉字的位置
  const firstChineseIndex = str.search(/[\u4e00-\u9fa5]/);
  // 确保找到的是汉字，并且其前面没有等号
  if (firstChineseIndex > -1 && str[firstChineseIndex - 1] !== '=') {
    // 在第一个汉字前插入空格
    return [str.slice(0, firstChineseIndex), str.slice(firstChineseIndex)];
  }
  return [str];
}
export enum MsgTextType {
  Text = 1,
  Emoji = 2,
  At = 3,
  Email = 4,
  File = 5,
  Url = 6,
}

export const getParsedTextMessage = (msg: MessageToSave, ignoreAt?: boolean) => {
  const text = msg?.contentExtra?.data?.text;
  if (!text) {
    return [];
  }

  if (msg.contentExtra?.contentType === 'richText') {
    try {
      const textArray = JSON.parse(text);
      return {
        type: 'richText',
        content: textArray,
      };
    } catch (error) {
      return text;
    }
  }
  // 只有群聊才需要解析@
  const isIgnoreAt = ignoreAt || msg.conversationType !== 3;
  // fix https://www.tapd.cn/tapd_fe/69781318/bug/detail/1169781318001055057
  const res = anchorme.list(text)?.map(i => {
    i.string = i?.string?.replace(/@[^~]*~$/g, "");
    return i
  });
  const splits: {
    str: string;
    type: MsgTextType;
    emojiSrc?: string;
    atIndex?: number;
    height?: number;
    width?: number;
    style?: {};
  }[] = [];
  let lastEndIndex = 0;
  res.forEach((item) => {
    if (item.start > lastEndIndex) {
      const othersMatch = matchEmojiAndAt(
        text.substring(lastEndIndex, item.start),
        isIgnoreAt,
        msg?.contentExtra?.contentType,
      );
      splits.push(...othersMatch);
    }

    if (item.string.includes('://')) {
      const strings = addSpaceBeforeFirstChineseWithoutEqual(item.string);
      splits.push({ str: strings[0], type: MsgTextType.Url });
      if (strings.length === 2) {
        splits.push({ str: strings[1], type: MsgTextType.Text });
      }
      lastEndIndex = item.start + item.string.length;
    } else {
      splits.push({ str: item.string, type: MsgTextType.Text });
      lastEndIndex = item.start + item.string.length;
    }
  });

  if (lastEndIndex < text.length) {
    const othersMatch = matchEmojiAndAt(text.substring(lastEndIndex), false, msg?.contentExtra?.contentType);
    splits.push(...othersMatch);
  }

  const totalMentions = splits.reduce((pre, cur) => (cur.type === MsgTextType.At ? pre + 1 : pre), 0);

  let atIndex = 0;
  const mentions = msg?.contentExtra?.data?.atInfo || [];
  splits.forEach((item) => {
    if (item.type === MsgTextType.At) {
      if (mentions.length === 0) {
        item.type = MsgTextType.Text;
      } else {
        // 如果@数量不匹配，则直接忽略@
        if (mentions?.length === totalMentions) {
          item.atIndex = atIndex;
          atIndex++;
        } else {
          const index = mentions.findIndex((val) => item.str.includes(val.name));
          if (index > 0) {
            item.atIndex = index;
          } else {
            item.type = MsgTextType.Text;
          }
        }
      }
    }
  });

  return splits;
};

export const getParsedText = (text: string, ignoreAt?: boolean, contentType?: string) => {
  if (!text) {
    return [];
  }
  // fix https://www.tapd.cn/tapd_fe/69781318/bug/detail/1169781318001055057
  const res = anchorme.list(text)?.map(i => {
    i.string = i?.string?.replace(/@[^~]*~$/g, "");
    return i
  });
  const splits: {
    str: string;
    type: MsgTextType;
    emojiSrc?: string;
    atIndex?: number;
    height?: number;
    width?: number;
  }[] = [];
  let lastEndIndex = 0;
  res.forEach((item) => {
    if (item.start > lastEndIndex) {
      const othersMatch = matchEmojiAndAt(text.substring(lastEndIndex, item.start), ignoreAt, contentType);
      splits.push(...othersMatch);
    }

    if (item.string.includes('://')) {
      splits.push({ str: item.string, type: MsgTextType.Url });
      lastEndIndex = item.start + item.string.length;
    } else {
      splits.push({ str: item.string, type: MsgTextType.Text });
      lastEndIndex = item.start + item.string.length;
    }
  });

  if (lastEndIndex < text.length) {
    const othersMatch = matchEmojiAndAt(text.substring(lastEndIndex), false, contentType);
    splits.push(...othersMatch);
  }

  return splits;
};
export const getRichParsedTextMessage = (msg: MessageToSave, ignoreAt?: boolean) => {
  const text = msg?.contentExtra?.data?.text;
  const textArray = JSON.parse(text)
  textArray.forEach(item => {

  });
}
export const getRichText = (text, html = false) => {
  try {
    const textArray = JSON.parse(text)
    const result = (textArray?.ops || textArray).map((item) => {
      if(item?.insert?.emoji?.value) {
        try {
          if(html) {
            const emojiData = EmojiObject[item?.insert?.emoji?.value]?.url;
            return `<img src="${emojiData}" />`
          }
          return item?.insert?.emoji?.value;
        } catch (error) {
          console.error('error',error)
          return ''
        }
      }
      if(item?.insert?.image) {
        try {
          if(html) {
            return `<img src="${item?.insert?.image}" />`
          }
          return `[图片]`;
        } catch (error) {
          console.error('error',error)
          return ''
        }
      }
      if(item?.insert?.mention) {
        return `${item?.insert?.mention?.denotationChar}${item?.insert?.mention?.value}`
      }
      if(typeof item.insert === 'string') {
        return item.insert
      }
      return ''
    })
    .join('')
    return result
  } catch (error) {
    console.error('error',error)
    return text;
  }
}
export const getParsedServerTextMsg = (msg: MessageToSave) => {
  const text = msg.contentExtra?.data?.text ?? ('' as string);
  return getParsedServerText(text);
};

export const getParsedServerText = (text: string) => {
  const matchRes = text.matchAll(/{.*?}/g);

  const list: { str: string; index?: number; isVar: boolean }[] = [];
  let curMatched = matchRes.next();
  let lastIndex = 0;
  let matchedIndex = 0;
  while (!curMatched.done) {
    const value = curMatched.value;
    const curText = value[0];

    const preText = text.substring(lastIndex, value.index);
    preText.length && list.push({ str: preText, isVar: false });

    list.push({
      str: curText.substring(1, curText.length - 1),
      isVar: true,
      index: matchedIndex++,
    });

    lastIndex = value.index + value[0].length;
    curMatched = matchRes.next();
  }

  const lastText = text.substring(lastIndex);
  lastText.length && list.push({ str: lastText, isVar: false });

  return list;
};

export const getServerMiddleMsgText = (msg: MessageToSave, myCard: string) => {
  const data = msg.contentExtra?.data;
  const displayInfo = getParsedServerTextMsg(msg);
  return displayInfo
    ?.map((item) => {
      const isMyself = item.isVar && data?.card_ids?.[item.index] === myCard;
      return isMyself ? i18nt('im.public.you') : item.str;
    })
    ?.join('');
};

export const getMiddleParsedText = (text: string) => {
  // 格式是【name】
  const matchRes = text.matchAll(/【.*?】/g);
  const list: { str: string; index?: number; isVar: boolean }[] = [];
  let curMatched = matchRes.next();
  let lastIndex = 0;
  let matchedIndex = 0;
  while (!curMatched.done) {
    const value = curMatched.value;
    const curText = value[0];

    const preText = text.substring(lastIndex, value.index);
    preText.length && list.push({ str: preText, isVar: false });

    list.push({
      str: curText,
      isVar: true,
      index: matchedIndex++,
    });

    lastIndex = value.index + value[0].length;
    curMatched = matchRes.next();
  }

  const lastText = text.substring(lastIndex);
  lastText.length && list.push({ str: lastText, isVar: false });

  return list;
};

/**
 * 广场助手消息列表缩
 * @param msg
 * @returns
 */
const getAppSquareText = (msg: MessageToSave) => {
  const scene = msg.contentExtra?.scene;
  if (SceneTypeArrAdministratorAppy.includes(scene)) {
    return `[${titleMapAdministratorAppy[scene] || ''}]`;
  }
  if (CultureSceneTypeArr.includes(scene)) {
    return `${CultureSceneTypeGetTitle(scene,'') || ''}`;
  }
  switch (scene) {
    case 7011:
      return `[${i18nt('im.msg.expire_remind')}]`;
    case 7012:
      return `[${i18nt('im.msg.expire_remind')}]`;
    default: {
      const title = msg.contentExtra?.data?.title;
      return title;
    }
  }
};

/**
 * 知行助手消息列表缩览
 * @param msg
 * @returns
 */
export const getAppKnowText = (msg: MessageToSave) => {
  switch (msg.contentExtra?.scene) {
    case 0:
      return `[${i18nt('im.public.remind')}]${msg.contentExtra?.data?.title}`;
    case 1:
      return `[${i18nt('im.public.zxlist')}]${msg.contentExtra?.data?.title}`;
    case 16:
      return `[${i18nt('im.public.remind')}]${msg.contentExtra?.data?.content.title}`;
    default:
      return `[${i18nt('im.public.unsupport')}]`;
  }
};

export const getAppSecretaryText = (msg: MessageToSave) => {
  //后端contentExtra.scene为0可能被吃掉不返回
  if (msg.contentExtra?.scene == 0 || msg.contentExtra?.data?.scene === 0) {
    // 小秘书生日提醒文案单独处理
    return '生日提醒';
  }
  if (msg.contentExtra?.scene === 30 || msg.contentExtra?.data?.scene === 30) {
    // 小秘书生日提醒文案单独处理
    const ex = msg.contentExtra?.data?.content?.ex;
    return ex?.title || `[${i18nt('im.public.notify')}]`;
  }
  if (msg.contentExtra?.data?.content?.title) {
    return msg.contentExtra?.data?.content?.title;
  }
  return `[${i18nt('im.public.notify')}]`;
};

export const getMeetingStartText = (msg: MessageToSave) => {
  const data = msg.contentExtra?.data as MeetingStartMsgData;
  const type = data.mediaType === 'audio' ? i18nt('im.rtc.call_voice') : i18nt('im.rtc.call_video');
  return `${data.creator.name} ${type}`;
};

export const getMeetingEndText = (msg: MessageToSave, mycard) => {
  const data = msg.contentExtra?.data as MeetingEndMsgData;
  let text = '';
  const reason = data.action || data.reason;
  if (mycard === msg.contentExtra.senderId) {
    switch (reason) {
      case 'invite':
        text = data?.mediaType === 'audio' ? i18nt('im.rtc.call_voice') : i18nt('im.rtc.call_video');
        break;
      case 'timeout':
        text = i18nt('im.rtc.timeout');
        break;
      case 'cancel':
        text = i18nt('im.rtc.cancel');
        break;
      case 'busy':
        text = i18nt('im.rtc.busy');
        break;
      case 'reject':
        text = i18nt('im.rtc.reject');
        break;
      case 'hangup': {
        text = data.duration ? getMeetingTimeText(data.duration) : i18nt('im.rtc.cancel');
        break;
      }
    }
  } else {
    switch (reason) {
      case 'invite':
        text = data?.mediaType === 'audio' ? i18nt('im.rtc.call_voice1') : i18nt('im.rtc.call_video1');
        break;
      case 'timeout':
        text = i18nt('im.rtc.timeout1');
        break;
      case 'cancel':
        text = i18nt('im.rtc.cancel1');
        break;
      case 'busy':
        text = i18nt('im.rtc.cancel');
        break;
      case 'reject':
        text = i18nt('im.rtc.reject1');
        break;
      case 'hangup': {
        text = data.duration ? getTimeDurationText(data.duration) : i18nt('im.rtc.cancel1');
        break;
      }
    }
  }
  return text;
};

export const getAppWorksText = (msg: MessageToSave) => {
  const sceneData = msg.contentExtra?.data;
  const scene = msg.contentExtra?.scene;
  const heaaderTitleMap = {
    15001: `[${msg.contentExtra?.data?.content?.title || '党建通知'}]`,
    15002: '[党建已通过]', // 被审核人审核通过
    15003: '[党建已拒绝]', // 被审核人审核拒绝
    15101: `[${msg.contentExtra?.data?.content?.title || i18nt('banch.ptfctz')}]`,
    15102: `[${i18nt('banch.ptfcytg')}]`, // 被审核人审核通过
    15103: `[${i18nt('banch.ptfcyjj')}]`, // 被审核人审核拒绝
  };
  const headerTitleMap = {
    association: i18nt('niche.szsq'),
    uni: i18nt('niche.szgx'),
    member: i18nt('im.public.biz'), // 被审核人审核通过
    government: i18nt('im.public.government'), // 被审核人审核拒绝
    cbd: i18nt('application.digital_cbd'), // 被审核人审核拒绝
  };
  // 关于我们-荣誉榜
  if ([15010, 15011, 15012].includes(msg.contentExtra?.scene)) {
    return `${msg.contentExtra?.data?.content?.title || '荣誉榜通知'}`;
  }
  if ([15004, 15005, 15006].includes(msg.contentExtra?.scene)) {
    return `${msg.contentExtra?.data?.content?.title || '历程通知'}`;
  }
  if ([15007, 15008, 15009].includes(msg.contentExtra?.scene)) {
    return `${msg.contentExtra?.data?.content?.title || '组织介绍通知'}`;
  }
  if ([15001, 15002, 15003, 15101, 15102, 15103].includes(msg.contentExtra?.scene)) {
    return `[${sceneData?.header?.title || heaaderTitleMap[msg.contentExtra?.scene]}]`;
  }
  if ([14007, 14008, 14015, 14016, 14020,14042].includes(msg.contentExtra?.scene)) {
    return `[${i18nt('im.public.government')}]`;
  }
  if ([16007, 16008, 16015, 16016,16042].includes(msg.contentExtra?.scene)) {
    return `[${i18nt('application.digital_cbd')}]`;
  }
  if ([17001, 17002].includes(msg.contentExtra?.scene)) {
    return `${msg.contentExtra?.data?.extend?.title || '数字平台'}`;
  }
  if ([19007, 19008, 19015, 19016,19042].includes(msg.contentExtra?.scene)) {
    return `[${i18nt('niche.szsq')}]`;
  }
    if ([51007, 51008, 51015, 51016,51042].includes(msg.contentExtra?.scene)) {
    return `[${i18nt('niche.szgx')}]`;
  }
  if ([5076, 5077, 5078].includes(msg.contentExtra?.scene)) {
    return `[${headerTitleMap[msg.contentExtra?.data?.extend?.digital_uuid]}]`;
  }
   if ([7017].includes(msg.contentExtra?.scene)) {
    return `[${sceneData?.extend?.title || i18nt('im.public.biz')}]`;
  }
  if ([20053, 20055].includes(msg.contentExtra?.scene)) {
    return `[${geTitleOrginaze(sceneData?.extend?.digital_platform?.digital_uuid)}]`;
  }
  if (AnnoSceneTypeArr.includes(msg.contentExtra?.scene)) {
    return `[${sceneData?.content?.title || ''}]`;
  }
  if (PopularizeSceneTypeArr.includes(msg.contentExtra?.scene)) {
    return `[${PopularizeSceneAdSceneTypeGetTitle(sceneData?.extend?.channel_type)}]`;
  }
  if (AdSceneTypeArr.includes(msg.contentExtra?.scene)) {
    return `[${AdSceneTypeGetTitle(sceneData?.extend?.platform_type)}]`;
  }
  if (FeePackageSceneTypeArr.includes(msg.contentExtra?.scene)) {
    return `[${FeePackageSceneGetTitle()}]`;
  }
  if (SceneTypeArrVisitor.includes(msg.contentExtra?.scene)) {
    return titleMap[msg.contentExtra?.scene];
  }
  if (PolicySceneTypeArr.includes(scene)) {
    return `[${getPolicyTitle(scene, sceneData?.content?.title)}]`;
  }
  if (CultureSceneTypeArr.includes(scene)) {
    return `${CultureSceneTypeGetTitle(scene, sceneData?.content?.title)}`;
  }
  if (SpServerSceneTypeArr.includes(scene)) {
    return `${SpServerSceneTypeGetTitle(scene, sceneData?.content?.title)}`;
  }
  if (sceneData?.header?.title) {
    return `[${sceneData?.header?.title}]`;
  }
  return `[${i18nt('im.public.biz')}]`;
};

const emojiData = (type) => {
  switch (type) {
    case 'text':
      return { regex: emojiRegex, iconMap: EmojiData, isBig: false };
    case 'emoji_coco_image':
      return { regex: CocoEmojiRegex, iconMap: CocoEmojiData, isBig: true };
  }
};
export const matchEmojiAndAt = (text: string, ignoreAt = false, contentType?: string) => {
  const res: { str: string; type: MsgTextType; emojiSrc?: string; height?: number; width?: number, index?:number }[] = [];
  const emoji = emojiData(contentType || 'text');
  // 匹配表情 和 @
  const msgRegex = new RegExp(`(${emoji.regex})${ignoreAt ? '' : '|(@.*?~)'}`, 'g');
  let lastMatched: RegExpExecArray = null;
  let lastEndIndex = 0;
  let atIndex = 0
  do {
    const curMatched = msgRegex.exec(text);

    // 当前匹配之前存在未匹配的文本
    if (curMatched && lastEndIndex < curMatched.index) {
      const unmatched = text.substring(lastEndIndex, curMatched.index);
      res.push({ str: unmatched, type: MsgTextType.Text });
    }

    if (curMatched) {
      const matched = curMatched[0];
      if (matched.startsWith('@')) {
        atIndex += 1
        res.push({
          str: matched.substring(0, matched.length - 1),
          type: MsgTextType.At,
          index: atIndex
        });
      } else {
        const emojiText = emoji.isBig ? matched : matched.substring(1, matched.length - 1);
        const emojiSrc = emoji.iconMap.get(emojiText);
        emojiSrc
          ? res.push(
            emoji.isBig
              ? { str: matched, type: MsgTextType.Emoji, emojiSrc, height: 120, width: 120 }
              : { str: matched, type: MsgTextType.Emoji, emojiSrc },
          )
          : res.push({ str: matched, type: MsgTextType.Text });
      }
      lastEndIndex = curMatched.index + curMatched[0].length;
    } else if (lastEndIndex < text.length) {
      // 最后一段未匹配的文本
      const unmatched = text.substring(lastEndIndex);
      res.push({ str: unmatched, type: MsgTextType.Text });
    }

    lastMatched = curMatched;
  } while (lastMatched);
  return res;
};

export const getMergedMessage = (messageList: MessageToSave[], conversation: ConversationToSave) => {
  let myInfo = { title: '', avatar: '' };
  // 文件传输助手，发送人信息存自己的。不存会造成转发文件助手聊天信息头像和名称为空
  if (conversation.localSessionId === 'assistant8app8file8helper') {
    myInfo = getProfilesInfo();
  }
  const msgData = [];
  messageList.map((msg) => msgData.push({
    content: getConversationMessagePreview(msg, conversation.myCardId),
    data: msg.contentExtra?.data ? toRaw(msg.contentExtra?.data) : null,
    type: msg.contentExtra?.contentType,
    sendTime: msg.sentTime,
    cardId: msg.contentExtra.senderId,
    openId: msg.senderUserId,
    name: myInfo.title || getMsgSenderName(msg),
    avatar: myInfo.avatar || getMsgSenderAvatar(msg),
  }));

  return msgData;
};

export const getZxNoticeTypeText = (type = '') => {
  switch (type) {
    case 'ONCE':
      return i18nt('im.time.once');
    case 'EVERY_DAY':
      return i18nt('im.time.daily');
    case 'EVERY_WEEK':
      return i18nt('im.time.weekly');
    case 'EVERY_MONTH':
      return i18nt('im.time.monthly');
    case 'EVERY_YEAR':
      return i18nt('im.time.yearly');
    case 'EVERY_WORKDAY':
      return i18nt('im.time.workday');
    case 'EVERY_MON_SAT':
      return i18nt('im.time.rang1');
    case 'EVERY_SUN_FRI':
      return i18nt('im.time.rang2');
    default:
      return i18nt('im.time.none');
  }
};

export const getZxNoticeRepeatText = (types?: string[]) => {
  const res = types
    ?.map((type) => {
      switch (type) {
        case 'NONE':
          return i18nt('im.time.none');
        case 'AFTER_5M':
          return i18nt('im.time.after5min');
        case 'AFTER_15M':
          return i18nt('im.time.after15min');
        case 'AFTER_30M':
          return i18nt('im.time.after30min');
        case 'AFTER_1H':
          return i18nt('im.time.after1h');
        case 'AFTER_2H':
          return i18nt('im.time.after2h');
        case 'AFTER_1D':
          return i18nt('im.time.after1d');
        case 'AFTER_2D':
          return i18nt('im.time.after2d');
        default:
          return i18nt('im.time.none');
      }
    })
    .join('；');
  return res || i18nt('im.time.none');
};

export const getZxListRemindTimeText = (knock_at: number, mission_at: number) => {
  let time = mission_at - knock_at;
  if (Number.isNaN(time) || knock_at === 0) {
    return i18nt('im.time.immediately');
  }

  // 毫秒转分钟
  time = Math.floor(time / 1000 / 60);
  if (time === 1) {
    return i18nt('im.time.immediately');
  }
  if (time === 5) {
    return i18nt('im.time.before5min');
  }
  if (time === 15) {
    return i18nt('im.time.before15min');
  }
  if (time === 30) {
    return i18nt('im.time.before30min');
  }
  if (time === 60) {
    return i18nt('im.time.before1h');
  }
  if (time === 120) {
    return i18nt('im.time.before2h');
  }
  if (time === 1440) {
    return i18nt('im.time.before1d');
  }
  const day = Math.floor(time / 1440);
  const hours = Math.floor((time - day * 1440) / 60);
  const minute = time % 60;
  const dayText = `${day}${i18nt('im.time.day')}`;
  const minuteText = minute ? `${minute}${i18nt('im.time.minute')}` : '';
  const hoursText = hours > 0 || minute > 0 ? `${hours}${i18nt('im.time.hour')}` : '';
  return `${i18nt('im.time.before')}${day ? dayText : ''}${hours ? hoursText : ''}${minuteText}`;
};

// current 只是用来触发更新UI展示
export const getSampleTimeText = (time?: number | string, current?) => {
  if (!time) {
    return ' ';
  }
  const currentTime = current ? current : Date.now();
  const minutesDiff = moment(currentTime).diff(moment(time), 'seconds');

  // 如果时间差小于60秒
  if (minutesDiff < 60) {
    return i18nt('im.time.current');
  }

  const minutes = moment(currentTime).diff(moment(time), 'minutes');

  // 如果时间差小于1小时
  if (minutes < 60) {
    return `${minutes}${i18nt('im.time.beforemin')}`;
  }

  const daysDiff = moment(currentTime).startOf('day').diff(moment(time).startOf('day'), 'days');

  // 如果时间差小于1天
  if (daysDiff < 1) {
    return `${i18nt('im.time.today')} ${moment(time).format('HH:mm')}`;
  }

  // 如果时间差小于2天
  if (daysDiff < 2) {
    return `${i18nt('im.time.yesterday')} ${moment(time).format('HH:mm')}`;
  }

  // 如果时间差小于1年
  if (moment().startOf('year').diff(moment(time).startOf('year'), 'years') < 1) {
    return `${moment(time).format('MM-DD HH:mm')}`;
  }

  // 其他时间
  return `${moment(time).format('YYYY-MM-DD HH:mm')}`;
};

export const getRemindTimeText = (time?: number | string) => moment(time).format('YYYY-MM-DD HH:mm');

export const getSampleTimeTextRemind = (time?: number | string) => {
  // 提醒卡片
  if (!time) {
    return ' ';
  }

  const minutesDiff = moment().diff(moment(time), 'seconds');

  // 如果时间差小于60秒
  if (minutesDiff < 60 && minutesDiff > 0) {
    return i18nt('im.time.inOneMin');
  }

  const minutes = moment().diff(moment(time), 'minutes');

  // 如果时间差小于1小时
  if (minutes < 60) {
    if (minutes > 0) {
      // 几分钟前
      return `${minutes}${i18nt('im.time.beforemin')}`;
    }
    return `${Math.abs(minutes)}${i18nt('im.time.aftermin')}`;
  }

  const daysDiff = moment().startOf('day').diff(moment(time).startOf('day'), 'days');

  // 如果时间差小于1天
  if (daysDiff < 1) {
    return `${i18nt('im.time.today')} ${moment(time).format('HH:mm')}`;
  }

  // 如果时间差小于2天
  if (daysDiff < 2) {
    return `${i18nt('im.time.yesterday')} ${moment(time).format('HH:mm')}`;
  }

  // 如果时间差小于1年
  if (moment().startOf('year').diff(moment(time).startOf('year'), 'years') < 1) {
    return `${moment(time).format('MM-DD HH:mm')}`;
  }

  // 其他时间
  return `${moment(time).format('YYYY-MM-DD HH:mm')}`;
};
export const getTimeDurationText = (duration: number) => {
  const minutes = `${Math.floor((duration % 3600) / 60)}`.padStart(2, '0');
  const seconds = `${duration % 60}`.padStart(2, '0');
  if (duration > 3600) {
    const hours = `${Math.floor(duration / 3600)}`.padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
  }

  return `${minutes}:${seconds}`;
};

export const getDurationText = (duration: number, padStart = 1) => {
  const seconds = `${duration % 60}`.padStart(2, '0');
  const minutes = `${Math.floor((duration % 3600) / 60)}`.padStart(padStart, '0');
  if (duration > 3600) {
    const hours = `${Math.floor(duration / 3600)}`.padStart(padStart, '0');
    return `${hours}:${minutes}:${seconds}`;
  }

  return `${minutes}:${seconds}`;
};

export const getConversationMember = (conversationId: string, card: string) => {
  const members = useMessageStore().allMembers.get(conversationId);
  return members?.get(card);
};

export const getConversationName = (conversation: ConversationToSave, withComment = true) => {
  if (conversation.conversationType === 1) {
    const member = getConversationMember(conversation.localSessionId, conversation.targetCardId);
    if (withComment && member?.comment) {
      return member?.comment;
    }
    const name = member?.nickname || member?.staffName || i18nt('im.public.unknown');
    if(conversation.relation === 'IDLE_TEMPORARY') {
      if(member.bindCard?.cardName) {
        return name + '(' + member.bindCard.cardName + ')'
      }
    }
    return name;
  }
  if (conversation.conversationType === 3) {
    const member = getConversationMember(conversation.targetId, conversation.myCardId);
    const group = useMessageStore().allGroups.find((item) => item.group === conversation.targetId);
    return member?.comment || group?.name || i18nt('im.public.unknown');
  }
  if (conversation.conversationType === 6) {
    return conversation.name || i18nt('im.public.unknown');
  }
  return i18nt('im.public.unknown');
};
export const getMember = (conversation: ConversationToSave) => {
  if (conversation.conversationType === 1) {
    const member = getConversationMember(conversation.localSessionId, conversation.targetCardId);
    if (member) {
      return member;
    }
  }
  return {};
};

export const getConversationGroupName = (conversation: ConversationToSave) => {
  // 只针对会话列表展示群备注+原群名 单独提出来使用
  if (conversation.conversationType === 3) {
    const member = getConversationMember(conversation.targetId, conversation.myCardId);
    const group = useMessageStore().allGroups.find((item) => item.group === conversation.targetId);
    return (member?.comment ? `${member?.comment} (${group.name})` : group?.name) || i18nt('im.public.unknown');
  }
  return i18nt('im.public.unknown');
};

export const assistantMap = {
  'assistant8app8know': {
    avatar: 'https://image.ringkol.com/ringkol/helper/know.svg',
    name: '知行助手'
  },
  'assistant8app8cloud8disk': {
    avatar: 'https://image.ringkol.com/ringkol/helper/cloud.svg',
    name: '云盘助手'
  },
  'assistant8app8approval': {
    avatar: 'https://image.ringkol.com/ringkol/helper/approve.svg',
    name: '审批助手'
  },
  'assistant8app8square': {
    avatar: 'https://image.ringkol.com/ringkol/helper/square.svg',
    name: '广场助手'
  },
  'assistant8app8address8book': {
    avatar: 'https://image.ringkol.com/ringkol/helper/add.svg',
    name: '新联系人通知'
  },
  'assistant8app8secretary': {
    avatar: 'https://image.ringkol.com/ringkol/helper/mishu.svg',
    name: '小秘书'
  },
  'assistant8app8business': {
    avatar: 'https://image.ringkol.com/ringkol/helper/remove.svg',
    name: '删除联系人申请'
  },
  'assistant8app8teams': {
    avatar: 'https://image.ringkol.com/ringkol/helper/contact.svg',
    name: '新成员申请'
  },
  'assistant8app8works': {
    avatar: 'https://image.ringkol.com/ringkol/helper/work.svg',
    name: '工作通知'
  },
  'assistant8app8file8helper': {
    avatar: 'https://image.ringkol.com/ringkol/helper/file.svg',
    name: '文件传输助手'
  },
  'assistant8app8niche': {
    avatar: 'https://image.ringkol.com/ringkol/helper/biz.svg',
    name: '商机助手'
  },
  'assistant8app8partner': {
    avatar: 'https://image.ringkol.com/ringkol/helper/union.svg',
    name: '联盟助手'
  },
  'assistant8app8activity': {
    avatar: 'https://image.ringkol.com/ringkol/helper/activity.svg',
    name: '活动助手'
  },
  'assistant8app8serve': {
    avatar: 'https://image.ringkol.com/ringkol/helper/service.svg',
    name: '服务助手'
  },
  'assistant8app8announcement8assistant': {
    avatar: 'https://image.ringkol.com/ringkol/helper/service.svg',
    name: '公告助手'
  },
  'assistant8app8city8proxy': {
    avatar: 'https://image.ringkol.com/ringkol/helper/application_list.svg',
    name: '城市代理助手'
  },
  'assistant8app8bbs': {
    avatar: 'https://image.ringkol.com/ringkol/helper/forum.svg',
    name: '论坛助手'
  },
  'assistant8app8digital8business': {
    avatar: 'https://image.ringkol.com/ringkol/helper/vcard.svg',
    name: '名片助手'
  },
  'assistant8app8pending': {
    avatar: "https://image.ringkol.com/ringkol/helper/pending.svg",
    name: '稍后处理'
  },
  'assistant8app8store8assistant': {
    avatar: "https://image.ringkol.com/common/5f08a36/shop_circle.png",
    name: '店铺助手'
  },
  'assistant8app8order8assistant': {
    avatar: "https://image.ringkol.com/ringkol/helper/order.png",
    name: '订单通知'
  },
  'assistant8app8ad8assistant': {
    avatar: "https://image.ringkol.com/ringkol/helper/1750834902703.png",
    name: '广告助手'
  },
  'assistant8app8idle8assistant': {
    avatar: "https://image.ringkol.com/ringkol/helper/xianzhi.png",
    name: '闲置助手'
  }
};
export const getConversationAvatar = (conversation: ConversationToSave) => {
  if (conversation.conversationType === 1) {
    const member = getConversationMember(conversation.localSessionId, conversation.targetCardId);
    return member?.avatar || '';
  }
  if (conversation.conversationType === 3 ) {
    const group = useMessageStore().allGroups.find((item) => item.group === conversation.targetId);
    return group?.attachment?.avatar || '';
  }
};

export const getIsSender = (msg: MessageToSave, session: ConversationToSave) => {
  if (msg.sendID && msg.sendID !== 'imAdminGateway') return session?.myOpenImId === msg.sendID;
  return session?.myCardId === msg.contentExtra?.senderId;
};

export const getMsgSender = (msg: MessageToSave) => {
  const { allMembers } = useMessageStore();
  const id = msg.conversationType === 3 ? msg.targetId : msg.localSessionId;
  let senderId = msg.contentExtra?.senderId;
  if (senderId?.includes('RKIM')) {
    senderId = senderId.split('RKIM')[1];
  }
  const info = allMembers.get(id)?.get(senderId);
  return info;
};

export const getMsgSenderName = (msg: MessageToSave, withComment = true) => {
  const notShowSendName = ['APP_ACTIVITY'];
  const type = msg.contentExtra?.type;
  if (notShowSendName.includes(type)) {
    return '';
  }

  const sender = getMsgSender(msg);
  if (!sender) {
    return msg.senderNickname || msg.content?.user?.name || msg.content?.name || msg.contentExtra?.data?.who || '';
  }

  if (msg.conversationType === 1) {
    const comment = withComment ? sender?.comment : '';
    return comment || sender?.nickname || sender?.staffName || msg.senderNickname || '';
  }
  const comment = withComment ? sender?.nickname : '';
  return comment || sender?.staffName || msg.senderNickname || '';
};

export const getMsgSenderAvatar = (msg: MessageToSave) => {
  const sender = getMsgSender(msg);
  return (
    sender?.avatar
    || msg.senderFaceUrl
    || msg.senderNickname
    || msg.content?.user?.portrait
    || msg.content?.user?.name
    || msg.content?.avatar
    || msg.contentExtra?.data?.who
    || ''
  );
};

export const getMsgSendTime = (msg: MessageToSave) => getChatTimeText(msg.sentTime);

// current 只是用来触发更新UI展示
export const getChatTimeText = (timestamp: number, current = 0) => {
  const currentTime = current > 0 ? current : Date.now();
  // 与当前时间的秒差
  const diffSecond = Math.floor((currentTime - timestamp) / 1000);
  if (diffSecond < 60) {
    // 1分钟内
    return `${i18nt('im.time.current')}`;
  }
  if (diffSecond < 3600) {
    // 1分钟 到 1小时内
    return `${Math.floor(diffSecond / 60)} ${i18nt('im.time.beforemin')}`;
  }
  if (diffSecond < 3600 * 24) {
    // 1小时 到 24小时内, 可能今天、昨天
    // 今天开始时间戳
    const todayStart = moment().startOf('day').valueOf();
    const prefix = timestamp >= todayStart ? '' : `${i18nt('im.time.yesterday')} `;
    return prefix + moment(timestamp).format('HH:mm');
  }
  if (diffSecond < 3600 * 24 * 2) {
    // 24小时 到 48小时内,可能昨天、前天
    // 昨天开始时间戳
    const yesterdayStart = moment().startOf('day').valueOf() - 24 * 60 * 60 * 1000;
    const prefix = timestamp >= yesterdayStart ? `${i18nt('im.time.yesterday')} ` : `${i18nt('im.time.dayBeforeYesterday')} `;
    return prefix + moment(timestamp).format('HH:mm');
  }
  if (diffSecond < 3600 * 24 * 3) {
    // 48小时 到 72小时内，包含前天、上前天
    // 前天开始时间戳
    const yesterdayBeforeStart = moment().startOf('day').valueOf() - 2 * 24 * 60 * 60 * 1000;
    if (timestamp >= yesterdayBeforeStart) {
      // 前天之内的时间
      return `${i18nt('im.time.dayBeforeYesterday')} ${moment(timestamp).format('HH:mm')}`;
    }
  }

  // 今年开始时间戳
  const yearStart = moment().startOf('year').valueOf();
  // 今年展示月日，之前则展示年月日
  const fmt = timestamp >= yearStart ? 'MM-DD' : 'YYYY-MM-DD';
  return moment(timestamp).format(fmt);
};

export const toBase64 = (imgUrl) => new Promise((resove) => {
  const image = new Image();
  image.setAttribute('crossOrigin', 'anonymous');
  const imageUrl = imgUrl;
  image.src = imageUrl;
  image.onload = () => {
    const canvas = document.createElement('canvas');
    canvas.width = image.width;
    canvas.height = image.height;
    const context = canvas.getContext('2d');
    context.drawImage(image, 0, 0, image.width, image.height);
    const dataurl = canvas.toDataURL('image/png');
    resove(dataurl);
  };
});

export const convertBase64ToBlob = (base64) => {
  const arr = base64.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = window.atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
};

export const imLogoutTip = (chatingSession) => {
  const isLogout = chatingSession.conversationType === 1 && chatingSession.unregistered;
  if (isLogout) {
    const confirmDia = DialogPlugin.confirm({
      header: i18nt('account.tip'),
      body: i18nt('contacts.unregisteredImTip'),
      confirmBtn: i18nt('contacts.iknow'),
      cancelBtn: null,
      closeBtn: null,
      closeOnOverlayClick: true,
      className: 'imLogoutTipDialog',
      theme: 'info',
      onConfirm: async () => {
        confirmDia.destroy();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
    return true;
  }
  return false;
};

const _isCenterMsg = (msg: MessageToSave) => {
  const msgType = msg.messageType;
  if (msgType === 2101 || msgType === 111) {
    return true;
  }
  const extraType = msg.contentExtra?.contentType;

  if (msg.conversationType === 3 && extraType === 'APP_MEETING_MSG') {
    return true;
  }
  switch (extraType) {
    case 'server_message_middle':
      // 是否是刚成为联系人, 临时前端处理
      // const isFastReply = /已成为联系人|已成為聯繫人/gi.test(msg.contentExtra?.data?.text);
      // if (isFastReply) {
      //   return false;
      // }
      return true;
    case 'server_message_middle_weather':
      return true;
    case 'server_message_middle_birthday':
      return true;
    case 'server_pair_add':
      return true;
    case 'server_rela_trans':
      return true;
    case 'server_rela_trans_secretary':
      return true;
    case 'server_group_update':
      return true;
    case 'meeting_start':
      return true;
    case 'meeting_end':
      return true;
    case 'server_video_call_finish':
      return true;
    case 'SportStepsEnabled':
      return true;
    case 'SportPlanAllUpToPar':
      return true;
    case 'SportStepsRanking':
      return true;
    case 'SportPlanCreated':
      return true;
    case 'PredefinedDayReminder':
      return true;
    case 'DayReminder':
      return true;
    case 'meeting':
      return true;
    case 'APP_MEETING_INVITE':
      return true;
    case 'client_blacklist':
      return true;
    default:
      return false;
  }
};

export const isCenterMsg = (item: MsgWrapper | MessageToSave) => {
  if ('isMerged' in item) {
    return item.isMerged ? true : _isCenterMsg(item.msg);
  }
  return _isCenterMsg(item);
};
/**
 * 同事关系、内部群、公司群、部门群，才显示已读状态
 * @param conversation
 * @returns
 */
export const conversationShouldRead = (conversation: ConversationToSave) => {
  if ([1,3].includes(conversation.conversationType) && ['CO_WORKER', 'PLATFORM_FRIEND','IDLE_TEMPORARY', '1', '2', '3', '20', '22', '23'].includes(conversation.relation)) {
    return true;
  }
  return false;
};

export const showReadStatus = (conversation: ConversationToSave, msgItem: MsgWrapper | MessageToSave) => {
  if (!conversationShouldRead(conversation)) return false;
  if (isCenterMsg(msgItem)) {
    return false;
  }
  return true;
};

export const getMeetingTimeText = (duration: number) => {
  const minutes = `${Math.floor((duration % 3600) / 60)}`.padStart(2, '0');
  const seconds = `${duration % 60}`.padStart(2, '0');
  if (duration > 3600) {
    const hours = `${Math.floor(duration / 3600)}`.padStart(2, '0');
    return `${i18nt('im.rtc.duration')}：${hours}:${minutes}:${seconds}`;
  }

  return `${i18nt('im.rtc.duration')}：${minutes}:${seconds}`;
};

/**
{
    "imgUrl": "https://img.kuaiyouyi.com/chat/ac0cb08edc3dfbc35e0d4223a97ca74b/20231024/10ee5cba2fa3c823cb82eff9c7044f33a.HEIC",
    "thumbnail": "https://img.kuaiyouyi.com/chat/ac0cb08edc3dfbc35e0d4223a97ca74b/20231024/10ee5cba2fa3c823cb82eff9c7044f33a.HEIC?x-oss-process=image/resize,w_400/format,webp",
    "width": 3024,
    "height": 4032,
    "size": 3327142,
    "type": "HEIC",
    "recallFileId": null,
    "name": "IMG_9121.HEIC"
}
 */
export const getMsgImageSrc = (data: any) => {
  let { imgUrl, thumbnail } = data;
  if (!thumbnail) {
    thumbnail = imgUrl;
  }
  // thumbnail 是base64
  if (!imgUrl && thumbnail && thumbnail.startsWith('data:image')) {
    return { src: thumbnail ?? '', thumbnail };
  }
  if (data.type === 'gif') {
    return { src: imgUrl, thumbnail: imgUrl };
  }
  if (data && data.type) {
    try {
      // 400K 以下的图片不进行压缩
      if (data.size < 400 * 1024) {
        return { src: imgUrl, thumbnail: getSrcTransform(imgUrl, data.type) };
      }

      const thumbnail = getSrcThumbnail(imgUrl, data.type);
      return { src: imgUrl, thumbnail };
    } catch (error) {
      console.error(error);
    }
  }

  return { src: imgUrl ?? '', thumbnail };
};

export const getSrcThumbnail = (src: string, type?: string) => {
  // if (!src.includes("kuaiyouyi.oss-cn-shenzhen.aliyuncs.com")) {
  //   // 对非阿里云不做处理
  //   return src;
  // }
  if (!src) return '';
  try {
    const url = new URL(src);
    const imgType = (type || url.pathname.split('.').pop() || '').toUpperCase();
    if (['ICO', 'SVG', 'GIF'].includes(imgType)) {
      return src;
    }

    if (['PNG', 'JPG', 'JPEG', 'BMP', 'GIF', 'WEBP'].includes(imgType?.toUpperCase())) {
      url.searchParams.set('x-oss-process', 'image/resize,w_400');
    } else {
      // 阿里云不支持缩放的图片格式，加上格式转换
      url.searchParams.set('x-oss-process', 'image/resize,w_400/format,webp');
    }
    return url.href;
  } catch (error) {
    console.log(error);
    return '';
  }
};

export const getSrcLogo = (src: string) => `${src}?x-oss-process=image/resize,m_fill,w_200,quality,q_60`;

export const getSrcTransform = (src: string, type?: string) => {
  // 对非阿里云不做处理
  // if (!src.includes("kuaiyouyi.oss-cn-shenzhen.aliyuncs.com")) {
  //   return src;
  // }

  try {
    const url = new URL(src);
    const imgType = (type || url.pathname.split('.').pop() || '').toUpperCase();
    if (CHROME_SUPPORT_IMG_TYPES.includes(imgType)) {
      return src;
    }
    // 不支持的图片转换格式为webp
    url.searchParams.set('x-oss-process', 'image/format,webp');
    console.log(url.href);
    return url.href;
  } catch (error) {
    console.log(error);
    return '';
  }
};

/**
 * 是否信令消息
 */
export const isSignalingMsg = (msg: MessageToSave) => {
  const signalingTypes: ReadonlyArray<string> = [
    'meeting',
    'APP_MEETING_INVITE',
    'clear_unread',
    'server_group_card_update',
  ];
  const type = msg?.contentExtra?.contentType;
  const data = msg?.contentExtra?.data;
  // 音视频信令
  if (type === 'meeting') {
    if (['ringing', 'accept'].includes(data?.action)) {
      return true;
    }
    // 信令消息都是发送给个人的，过滤掉单聊里面的群语音信令
    if (msg?.conversationType === 1 && data?.meetingType === 'group') {
      return true;
    }
  }
  return !type || signalingTypes.includes(type);
};

/**
 * 是否可存储消息
 * @param msg
 * @returns
 */
export const isStorageMsg = (msg: MessageToSave) => (msg.messageType === 'text' && !isSignalingMsg(msg)) || isReferMsg(msg);

/**
 * 是否引用回复消息
 * @param msg
 * @returns
 */
export const isReferMsg = (msg: MessageToSave) => msg.messageType === 114;

const shouldCountUnread = (msg: MessageToSave, myCarId) => {
  // 消息通过 clear_unread 判断处理后，判断为已读消息，不进行计数
  const serverMsg = [
    'PredefinedDayReminder',
    'DayReminder',
    'SportStepsEnabled',
    'SportStepsRanking',
    'SportPlanAllUpToPar',
    'SportPlanCreated',
  ];
  // 已读的和离线消息不统计，通过onconversationchange统计
  if (msg.isRead || msg.isOffline) { return false; }
  // 外部身份转移后senderUserId的openid不会变
  return (
    !(msg?.senderUserId?.includes(myCarId) || msg?.senderUserId?.includes(getOpenid()))
    || serverMsg.includes(msg.contentExtra?.contentType)
  );
};

export const shouldCountMention = (msg: MessageToSave) => {
  if(msg.conversationType !== 3) return false;
  const atInfo = msg.contentExtra?.data?.atInfo;

  if (!Array.isArray(atInfo)) {
    return false;
  }
  const ids = getImCardIds();

  return atInfo.some((it) => (it?.cardId === '' && it?.openId === '') || ids.includes(it.cardId));
};

export const getUnreadInfo = (datas: ConversationMsgReceiveData, myCarId): ConversationChangeData => {
  const { msgList } = datas;
  let unread = 0;
  let mention = 0;
  let ringType = ''
  // const isClean = Boolean(cleans.length);
  msgList.forEach((msg) => {
    if(msg.conversationType === 6 ){
      if(!msg.isOffline){
        unread++;
        if(msg.contentExtra?.scene === 8212){
          ringType = 'orderRing'
        }
      }
       return;
    }
    if (shouldCountUnread(msg, myCarId)) {
      // if (cleans.every((item) => item.sentTime < msg.sentTime) || !isClean) {
        unread++;
        if (shouldCountMention(msg)) {
          mention++;
        }
      // }
    }
  });

  const latestMsg = latestMsgData(msgList);
  return { unread, mention, latestMsg, ringType };
};
/**
 * 发送，收到消获取摘要数据
 * @param msgList []
 * @returns
 */
export const latestMsgData = (msgList) => {
  let latestMsg = null;
  if (msgList.length > 0) {
    latestMsg = msgList[msgList.length - 1];
    delete latestMsg?.textElem;
    delete latestMsg?.attachedInfoElem;
    delete latestMsg?.ex;
  }
  return latestMsg
}

/**
 * 移动端上报的融云历史数据传了移动端的结构,需要做兼容
 * @param contentExtra
 * @returns
 */
export const appContentType = (contentExtra) => {
  if (['APP_ACTIVITY', 'APP_NICHE', 'APP_SERVE'].includes(contentExtra.data?.type)) {
    return true;
  }
  return [
    'square_help',
    'zx_help',
    'approve_help',
    'delete_contact_apply',
    'work_notice',
    'new_contact',
    'new_member_apply',
    'little_secret',
    'cloud_disk_help',
  ].includes(contentExtra?.contentType);
};

// 选人组件参数
const menusObj = {
  inner: ['organize', 'recent'],
  outer: ['recent', 'orgcontacts'],
  personal: ['friend', 'recent', 'orgcontacts'],
  platform: ['platform', 'receiver'],
};

/**
 * 转发选人获取参数
 * @param cardId
 * @returns
 */
export const getSelectDialogData = (cardId) => {
  const card = getAllCards()?.find((item) => item.cardId === cardId);
  const type = cardIdType(cardId);
  return {
    cardId: cardId ? [cardId] : [],
    teamId: card?.teamId ? [card.teamId] : [],
    menus: menusObj[type],
  };
};

/**
 * 离线推送展示信息
 * @param msg MessageToSave
 * @returns
 */
export const getOfflinePushInfo = (msg: MessageToSave) => {
  let pushContent = getConversationMessagePreview(msg, msg.contentExtra.senderId, 'offline');
  console.log('===>getOfflinePushInfo', msg, pushContent);

  const offlinePushInfo: OfflinePush = { title: '', desc: '', iOSBadgeCount: true, iOSPushSound: 'default' };
  // 如果没有摘要信息，走默认兜底内，最好检查下原因
  if (!pushContent) {
    pushContent = '你有一条新消息';
    console.log('pushContent 摘要没有，显示兜底文案');
  }

  // 设置推送移动端通知栏消息
  if (msg.conversationType === 3) {
    // 群聊通知消息拼凑发送人名称
    const msgStore = useMessageStore();
    const groupInfo = msgStore.allGroups.find((item) => item.group === msg.targetId);
    offlinePushInfo.title = groupInfo?.name || '群聊';
    offlinePushInfo.desc = msg.contentExtra?.senderNickname ? `${msg.contentExtra.senderNickname}:${pushContent}` : pushContent;
  } else if (msg.conversationType === 1 && msg.contentExtra?.senderNickname) {
    // 单聊通知栏消息推送
    offlinePushInfo.title = msg.contentExtra.senderNickname;
    offlinePushInfo.desc = pushContent;
  }

  if (!offlinePushInfo.title) {
    const offLine = JSON.stringify(offlinePushInfo);
    const msgString = JSON.stringify(msg);
    logHandler({ name: 'im-发送消息没有人员名称', info: `offLine:${offLine},msg:${msgString}`, desc: '' });
  }
  return offlinePushInfo;
};

export const transformRichTextToText = (text: string) => {
  const data = {
    text: '',
    atInfo: [],
  }
  try {
    const msg = JSON.parse(text);
    msg.forEach(item => {
      if (item.insert?.mention) {
        data.atInfo.push({ cardId: item.insert.mention.cardId, openId: item.insert.mention.openId, sessionId: item.insert.mention.sessionId })
        data.text += (`@${item.insert.mention.value}~`)
        return;
      }
      if (item.insert?.emoji?.value) {
        data.text += (`${item?.insert?.emoji?.value}`)
        return;
      }
      if (item?.attributes?.link) {
        data.text += (item?.insert + ' ' + item?.attributes?.link)
        return;
      }
      if (typeof item.insert === 'string') {
        data.text += (item.insert)
        return;
      }
    })
  } catch (error) {
    console.error('transformRichTextToText', error)
  }
  return data;
}

export const transformTextToRichText = (text: string, atInfo?: any[]) => {
  const data = {
    ops: [],
  }
  try {
    // 在这把文本按[xxx]和@xx~正则分割成数组
    const textList = text.split(/(\[.*?\]|\@.*?~)/)
    let mentionIndex = 0;
    textList.forEach(item => {
      if (item.startsWith('@') && item.endsWith('~')) {
        const atInfoItem = atInfo?.[mentionIndex]
        mentionIndex++
        if (atInfoItem) {
          if (atInfoItem) {
            data.ops.push({ insert: { mention: { denotationChar: '@', value: item.replace('@', '').replace('~', ''), cardId: atInfoItem.cardId, openId: atInfoItem.openId, sessionId: atInfoItem.sessionId } } })
          }
          return;
        }
      }
      if (item.startsWith('[') && item.endsWith(']')) {
        data.ops.push({ insert: { emoji: { value: item } } })
        return;
      }
      if (typeof item === 'string') {
        data.ops.push({ insert: item })
      }
    })
  } catch (error) {
    console.error('transformTextToRichText', error)
  }
  return data;
}

export const getUtf8Length = (str: string) => {
    return new TextEncoder().encode(str).length;
};
