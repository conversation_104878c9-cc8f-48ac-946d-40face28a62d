import { client_orgRequest } from "@renderer/utils/apiRequest";

/**
 * @param ids string 多个英文逗号隔开
 * @returns
 */
export const getAdBatchStatus = (params, teamId) =>
  client_orgRequest({
    url: `/ad/batch-status`,
    method: "get",
    params,
    headers: {
      teamId,
    },
  });

/**
 * @param ids array[string] 多个英文逗号隔开
 * @param type string member 数字商协 government 数字城市 association 数字社群 cbd 数字CBD
 * @returns
 */
export const getApplyStatus = (data) =>
  client_orgRequest({
    url: `/digit212/ApplyStatus`,
    method: "post",
    data,
  });

/**
 * @param push_ids string 多个英文逗号隔开
 * @returns
 */
export const getDynamicStatus = (params, teamId) =>
  client_orgRequest({
    url: `/dynamic/batch-status`,
    method: "get",
    params,
    headers: {
      teamId,
    },
  });

/** 获取访客申请加入消息同步状态
 * @param ids array[string] 多个英文逗号隔开
 * @param type string member 数字商协 government 数字城市 association 数字社群 cbd 数字CBD
 * @returns
 */
export const getApplyVisitStatus = (data) =>
  client_orgRequest({
    url: `/digit212/ApplyVisitStatus`,
    method: "post",
    data,
  });

/**
 * 获取商品信息
 * @param channelId 渠道ID
 * @returns 商品信息
 */
export const getProductInfo = (goodsId: string) =>
  client_orgRequest({
    url: `/idle/market/channel/detail?channel_id=${goodsId}`,
    method: "get",
  });
