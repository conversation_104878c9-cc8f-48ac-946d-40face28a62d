<template>
  <div class="im-status">
    <div v-if="!netStore.netWork" class="status-tips disconnect">
      <iconpark-icon name="iconclosecircle" style="font-size: 20px;" />
      {{ t('im.public.networkErr') }}
    </div>
    <div v-else-if="!netStore.isServerFinish" class="status-tips">
      <t-loading size="small" :text="t('im.public.syncing')" />
    </div>
    <div v-else-if="netStore.imStatus === IMStatus.connecting" class="status-tips disconnect">
      <t-loading size="small" :text="t('im.public.linking')" />
    </div>
    <div
      v-else-if="netStore.imStatus === IMStatus.disconnected || netStore.imStatus === IMStatus.error"
      class="status-tips disconnect"
    >
      <iconpark-icon name="iconclosecircle" style="font-size: 20px;" />
      {{ t('im.public.disconnect') }}
    </div>
  </div>
  <!-- 消息分组 -->
  <!-- TODO: 可控制是否开启分组； -->
  <div class="group-bar" v-if='groupTab.length > 1'>
    <div class="flex flex-row justify-center relative">
      <div :class="['group-list flex-wrap w-full pr-[20px]', { 'justify-center': groupTab.length <= groupSetting.maxLength }]">
        <div v-for="group in (groupSetting.showMore ? groupTab : groupTab.slice(0, groupSetting.maxLength))" :key="group.key"
          :class="['group-item' , { active: activeGroupType === group.key }]" @click="handleGroupClick(group.key)">
          <div class="icon-wrap">
            <img
              draggable="false"
              class="icon"
              :src="group.image"
              :alt="group.value"
            >
            <div v-if="group.count" class="red-dot" />
          </div>
          <span>{{ group.value }}</span>
        </div>
      </div>
      <div v-if="groupTab.length > groupSetting.maxLength" class="group-more-btn flex-shrink-0 absolute right-0 top-0"
        @click="groupSetting.showMore = !groupSetting.showMore"
      >
        <iconpark-icon v-if="groupSetting.showMore" name="iconarrowup-a960jjb9" class="icon" />
        <iconpark-icon v-else name="iconarrowdown" />
      </div>
    </div>

    <div class="divider" />
  </div>

  <div ref="conversationsRef" class="list select-none" @scroll="onScroll">
    <!-- 常用聊天 -->
    <div v-show="commonChatActive && commonSessionList.length > 0" id="conversationCommon">
      <div class="common-chat-active">
        <div class="btn-back" @click="commonChatClick(false)">
          <iconpark-icon name="iconarrowlift" class="icon" />
        </div>
        <div class="title">常用聊天</div>
        <div class="w-28">&nbsp;</div>
      </div>
      <div
        v-for="session in commonSessionList"
        :key="session.localSessionId"
        :class="{ item: true, sessionItem: true, active: session.localSessionId === chatingSession?.localSessionId }"
        @click="changeConversation(session)"
        @contextmenu="onRightClick($event, session)"
      >
        <chat-avatar size="large" :conversation="session" />
        <template v-if="session.unreadCount">
          <div v-if="!session.isMute " class="badge">
            {{ session.unreadCount > 99 ? '...' : session.unreadCount }}
          </div>
          <svg
            v-else
            class="badge-dot"
            xmlns="http://www.w3.org/2000/svg"
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
          >
            <path
              d="M0.5 6C0.5 2.96243 2.96243 0.5 6 0.5C9.03757 0.5 11.5 2.96243 11.5 6C11.5 9.03757 9.03757 11.5 6 11.5C2.96243 11.5 0.5 9.03757 0.5 6Z"
              fill="#FF4AA1"
              stroke="white"
            />
          </svg>
        </template>

        <div class="conversation-info">
          <div class="conversation-row1">
            <div class="name">
              <div class="text-truncate">
                {{ session.conversationType === 1 ? getConversationName(session) : getGroupName(session) }}
              </div>
              <RelationTag v-if="session.conversationType !== 6" :relation="session.relation" />
            </div>
            <div v-if="session.updateTime && (session.conversationType !== 6 || session.latestMessage)" class="time">{{ getChatTimeText(session.updateTime, currentTime) }}</div>
          </div>
          <div class="conversation-row2" style="min-height: 16px;">
            <svg v-if="session.latestMessage?.sentStatus === 10" class="svg-size20 sending">
              <use href="#iconloading" />
            </svg>
            <svg v-else-if="session.latestMessage?.sentStatus === 20" class="svg-size20 error">
              <use href="#iconattention" />
            </svg>
            <!-- 摘要 -->
            <div v-safe-html="getSessionPreviewPrefix(session)" class="text-truncate preview select-text" />
            <svg
              v-if="session.isMute"
              class="mute-icon"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M16 16L4 4"
                stroke="#ACB3C0"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M13.5002 13.4951H16.0002C15.5583 13.4951 15.2432 13.3195 14.9308 13.0071C14.6183 12.6947 14.4428 12.2709 14.4428 11.8291V7.9417C14.4428 6.76343 13.9747 5.63342 13.1416 4.80025C12.3085 3.96709 11.1785 3.49902 10.0002 3.49902C8.8219 3.49902 7.69189 3.96709 6.85873 4.80025C6.51568 5.1433 6.23453 5.53667 6.02246 5.96302"
                stroke="#ACB3C0"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M9 16.501H11"
                stroke="#ACB3C0"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M5.5573 8.49902V11.8291C5.5573 12.2709 5.38178 12.6947 5.06934 13.0071C4.75691 13.3195 4.44185 13.4951 4 13.4951H10.5"
                stroke="#ACB3C0"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>

            <!-- <iconpark-icon class="mute-icon" v-if="session.isMute" size="20px" name="iconcloseremind" /> -->
          </div>
          <ConversationRelationTag v-if="session.conversationType !== 6" :session="session" />
          <svg
            v-if="session.isTop"
            style="position: absolute;top:4px; right:4px;"
            xmlns="http://www.w3.org/2000/svg"
            width="11"
            height="11"
            viewBox="0 0 11 11"
            fill="none"
          >
            <path xmlns="http://www.w3.org/2000/svg" d="M0.707107 1.70711C0.0771425 1.07714 0.523309 0 1.41421 0H8C9.65685 0 11 1.34315 11 3V9.58579C11 10.4767 9.92286 10.9229 9.29289 10.2929L0.707107 1.70711Z" fill="#C9CFFF" />
          </svg>
        </div>
      </div>
    </div>
    <!-- 非常用会话列表 -->
    <div v-show="!commonChatActive || !commonSessionList.length" id="conversationAll">
      <div class="item mt-4 common-conversation" :class="{active: commonChatActive}" @click="commonChatClick(true)">
        <div class="content w-44 h-44">
          <img class="avatar border-rd-full" src="@/assets/im/common-im.svg" data-alt="常用聊天">
        </div>

        <BadgeDot v-bind="{unreadCount: commonListCounts, isMute: false}" />

        <div class="conversation-info">
          <div class="conversation-row1">
            <div class="name">常用聊天</div>
            <div v-if="latestConversation?.updateTime" class="time">
              {{
                getChatTimeText(latestConversation.updateTime, currentTime) }}
            </div>
          </div>
          <div class="conversation-row2 min-h-16">
            <!-- 常用聊天为空：可将您的重要会话添加至常用聊天 -->
            <div v-if="latestConversation" class="text-truncate preview">
              {{ latestConversation.conversationType === 1 ? getConversationName(latestConversation) :
                getGroupName(latestConversation) }}:
              <span v-safe-html="getSessionPreviewPrefix(latestConversation)" />
            </div>
            <div v-else class="text-truncate preview">{{ t('im.public.common_session_none') }}</div>
          </div>
        </div>
      </div>

      <div
        v-for="session in sessionListActive"
        :key="session.localSessionId"
        :class="{ item: true, sessionItem: true, active: session.localSessionId === chatingSession?.localSessionId }"
        @click="changeConversation(session)"
        @contextmenu="onRightClick($event, session)"
      >
        <chat-avatar size="large" :conversation="session" />
        <template v-if="session.unreadCount">
          <div v-if="!session.isMute " class="badge">
            {{ session.unreadCount > 99 ? '...' : session.unreadCount }}
          </div>
          <svg
            v-else
            class="badge-dot"
            xmlns="http://www.w3.org/2000/svg"
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
          >
            <path
              d="M0.5 6C0.5 2.96243 2.96243 0.5 6 0.5C9.03757 0.5 11.5 2.96243 11.5 6C11.5 9.03757 9.03757 11.5 6 11.5C2.96243 11.5 0.5 9.03757 0.5 6Z"
              fill="#FF4AA1"
              stroke="white"
            />
          </svg>
        </template>

        <div class="conversation-info">
          <div class="conversation-row1">
            <div class="name">
              <div class="text-truncate">
                {{ session.conversationType === 1 ? getConversationName(session) : getGroupName(session) }}
              </div>
              <RelationTag v-if="session.conversationType !== 6" :relation="session.relation" />
            </div>
            <div v-if="session.updateTime && (session.conversationType !== 6 || session.latestMessage)" class="time">{{ getChatTimeText(session.updateTime, currentTime) }}</div>
          </div>
          <div class="conversation-row2" style="min-height: 16px;">
            <svg v-if="session.latestMessage?.sentStatus === 10" class="svg-size20 sending">
              <use href="#iconloading" />
            </svg>
            <svg v-else-if="session.latestMessage?.sentStatus === 20" class="svg-size20 error">
              <use href="#iconattention" />
            </svg>
            <!-- 摘要 -->
            <div v-safe-html="getSessionPreviewPrefix(session)" class="text-truncate preview select-text" />
            <svg
              v-if="session.isMute"
              class="mute-icon"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M16 16L4 4"
                stroke="#ACB3C0"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M13.5002 13.4951H16.0002C15.5583 13.4951 15.2432 13.3195 14.9308 13.0071C14.6183 12.6947 14.4428 12.2709 14.4428 11.8291V7.9417C14.4428 6.76343 13.9747 5.63342 13.1416 4.80025C12.3085 3.96709 11.1785 3.49902 10.0002 3.49902C8.8219 3.49902 7.69189 3.96709 6.85873 4.80025C6.51568 5.1433 6.23453 5.53667 6.02246 5.96302"
                stroke="#ACB3C0"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M9 16.501H11"
                stroke="#ACB3C0"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M5.5573 8.49902V11.8291C5.5573 12.2709 5.38178 12.6947 5.06934 13.0071C4.75691 13.3195 4.44185 13.4951 4 13.4951H10.5"
                stroke="#ACB3C0"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>

            <!-- <iconpark-icon class="mute-icon" v-if="session.isMute" size="20px" name="iconcloseremind" /> -->
          </div>
          <ConversationRelationTag v-if="session.conversationType !== 6" :session="session" />
          <svg
            v-if="session.isTop"
            style="position: absolute;top:4px; right:4px;"
            xmlns="http://www.w3.org/2000/svg"
            width="11"
            height="11"
            viewBox="0 0 11 11"
            fill="none"
          >
            <path xmlns="http://www.w3.org/2000/svg" d="M0.707107 1.70711C0.0771425 1.07714 0.523309 0 1.41421 0H8C9.65685 0 11 1.34315 11 3V9.58579C11 10.4767 9.92286 10.9229 9.29289 10.2929L0.707107 1.70711Z" fill="#C9CFFF" />
          </svg>
        </div>
      </div>
    </div>

    <div v-if="!sessionListActive.length && !commonSessionList.length" class="session-empty">
      <img class="icon" :src="groupEmpty" alt="暂无数据">
    </div>
  </div>

  <remove-alert :target="removeConfirmTarget" @confirm="onConfirmRemove" @cancel="onCancelRemove" />
  <context-menu
    :target="contextTarget"
    placement="right"
    :menus="contextMenus"
    @selected="onMenuSelected"
  />

  <t-dialog
    v-model:visible="alertQuitGroup"
    :confirm-btn="t('im.public.exit')"
    @cancel="alertQuitGroup = false;"
    @confirm="quitGroup"
  >
    <template #header>
      <div style="font-size: 16px; font-weight: 700;color: #13161b;">
        <t-icon name="help-circle-filled" style="color: #E66800" />
        {{ t('im.public.exit') }}
      </div>
    </template>

    <template #body>
      <div style="font-size: 14px; font-weight: 400;color: #717376;padding-bottom: 20px;">
        {{ t('im.public.exitTip1') }}
      </div>
      <div style="display: block;font-size: 14px; font-weight: 400;color: #13161b;">
        <t-checkbox v-model="cleanMsg">{{ t('im.public.clearChat') }}</t-checkbox>
      </div>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, shallowRef, onMounted, onBeforeUnmount, computed } from 'vue';
import { debounce } from 'lodash';
import { storeToRefs } from 'pinia';
import { MessagePlugin } from 'tdesign-vue-next';
import { SentStatus } from '@renderer/types/enumer';
// import type { ConversationToSave, MessageToSave } from '@renderer/customTypes/message';
import RelationTag from '@renderer/components/contacts/relationTag.vue';
import ConversationRelationTag from '@renderer/views/message/components/conversationRelationTag.vue';
import { useContactsStore } from '@renderer/store/modules/contacts';
import { getOpenid } from '@renderer/utils/auth';
import { quitGroupApi, updatePrivateChatApi, updateGroupMemberApi } from '@renderer/api/im/api';
import { useI18n } from 'vue-i18n';
import LynkerSDK from '@renderer/_jssdk';
import { nextTick } from 'process';
import ChatAvatar from '../chat/ChatAvatar.vue';
import ContextMenu from '../components/ContextMenu.vue';
import RemoveAlert from '../components/RemoveAlert.vue';
import { useNetStore, IMStatus } from '../service/imStatus';
import { useChatActionStore, useChatVoiceStore } from '../service/actionStore';
import { useMessageStore } from '../service/store';
import { updateConversationTopAndMute, updateConversationRelation } from '../service/relationUpdate';
import { getGroupInfo, updateConversation } from '../service/request';

import { getConversationMessagePreview, getMember, getChatTimeText, getConversationName, getConversationGroupName, getMsgSenderName, getIsSender, getRichText } from '../service/msgUtils';
import { ConversationAction, getConversationMenus } from '../service/contextMenuUtils';
import { onDbConversationSetting } from '../service/dbUtils';

import { msgEmit } from '../service/msgEmit';
import { useNewEditorStore } from '../service/editor';
import { getAddressbookStatus } from '../service/extend/statusUtils';

import BadgeDot from '../components/BadgeDot.vue';
import groupAllEmptyIcon from '@/assets/im/group-all-empty.svg';
import groupFriendEmptyIcon from '@/assets/im/group-friend-empty.svg';
import groupInnerEmptyIcon from '@/assets/im/group-inner-empty.svg';
import groupOuterEmptyIcon from '@/assets/im/group-outer-empty.svg';
import groupCommunityEmptyIcon from '@/assets/im/group-community-empty.svg';
import groupIdleEmptyyIcon from '@/assets/im/group-community-temporary.svg';
import { useSessionSetting } from '@/views/message/tool/service/chatSetting';

const { ipcRenderer } = LynkerSDK;
const groupSetting = ref({
  showMore: false,
  maxLength: 5,
});
const newEditorStore = useNewEditorStore();
const { newEditorRef } = storeToRefs(newEditorStore);

const { t } = useI18n();

const netStore = useNetStore();
const contextConversation = ref<ConversationToSave | null>(null);
const contextTarget = shallowRef<{ ele: HTMLElement}>(null);
const contextMenus = shallowRef([]);

const msgStore = useMessageStore();
const voiceStore = useChatVoiceStore();
const contactStore = useContactsStore();

const {
  sessionList,
  chatingSession,
  currentTime,
  groupTab,
  activeGroupType,
  sessionListActive,
  commonChatActive,
  commonSessionList,
  latestConversation,
  commonListCounts,
} = storeToRefs(msgStore);

// 消息分组-空状态
const groupEmpty = computed(() => {
  console.log(activeGroupType.value, 'activeGroupType.value');
  return ({
    all: groupAllEmptyIcon,
    friend: groupFriendEmptyIcon,
    internal: groupInnerEmptyIcon,
    external: groupOuterEmptyIcon,
    community: groupCommunityEmptyIcon,
    idle: groupIdleEmptyyIcon,
  }[activeGroupType.value]);
});
// 常用选中
const commonChatClick = (data) => {
  msgStore.onCleanReferMsg();
  msgStore.commonChatClick(data);
  if (data) setting.reset();
};
// 分组点击事件
const handleGroupClick = debounce((key) => {
  msgStore.onSwitchGroup(key);
  lastLocalId = null;
  const ele = conversationsRef.value.querySelectorAll('.common-conversation')?.item(0);
  ele?.scrollIntoView({ block: 'start' });
}, 500, { leading: true, trailing: false });

ipcRenderer.removeAllListeners('update-contact-list');
ipcRenderer.on('update-contact-list', (_, args) => {
  contactStore.setFollowList();
  console.log('update-contact-list', args);
  if (args?.applyId && chatingSession.value?.relation === '8') {
    msgStore.chatingMessages.forEach((item) => {
      const msgApplyId = item.msg?.contentExtra?.data?.extend?.apply_id;
      if (msgApplyId == args.applyId) {
        setTimeout(() => {
          // 后端微服务通知更新需要时间，直接请求可能因为未同步，获取状态错误
          getAddressbookStatus(item.msg);
        }, 1000);
      }
    });
  }
});

const changeConversation = (conversation: ConversationToSave) => {
  if (chatingSession.value?.localSessionId === conversation.localSessionId) return;
  contextTarget.value = null;
  useChatActionStore().onEndSelecting(true);
  msgStore.setCurrentSession(conversation);
  // 助手类不用更新群关系和关注列表
  if (conversation.conversationType === 6 && conversation.targetId === 'assistant8app8address8book') {
    // 新联系人通知清除通讯录红点
    contactStore.clearNotice();
    return;
  }
  if (conversation.conversationType === 6) return;
  voiceStore.endVoiceMsgPlay();
  chatingSession.value && !conversation.draft && (conversation.draft = useNewEditorStore().newDraftMap.get(conversation.conversationID));
  conversation.unreadCount = 0;
  conversation.unreadMentionedCount = 0;
  updateConversationRelation(conversation, 'setCurrentSession');
  contactStore.setFollowList();
};

const onRightClick = (evt, conversation: ConversationToSave) => {
  evt.preventDefault();
  evt.stopPropagation();

  contextConversation.value = conversation;
  contextTarget.value = { ele: evt.target };
  contextMenus.value = getConversationMenus(conversation);
};

const resetContextMenu = () => {
  contextConversation.value = null;
  contextTarget.value = null;
  contextMenus.value = null;
};

const setting = useSessionSetting();

// 更新常用聊天状态
const updateCommonChatStatus = async (conversation: ConversationToSave, action: ConversationAction) => {
  const isMoveIntoCommon = action === ConversationAction.MoveIntoCommon;
  const group_type = isMoveIntoCommon ? 1 : 0;

  try {
    group_type === 1 && (commonChatActive.value = false);
    // 根据会话类型处理
    // conversation.group_type = group_type;
    console.log('conversation', conversation, group_type)
    const res = await updateConversation({...conversation, group_type});
    console.log('updateCommonChatStatusres', res)
    if (!res) {
      MessagePlugin.warning(isMoveIntoCommon ? t('im.public.move_into_common_error') : t('im.public.remove_common_error'));
      return;
    }
    conversation.group_type = group_type;
    // 更新会话的移入常用状态
    if (chatingSession.value?.localSessionId === conversation.localSessionId) {
      chatingSession.value = null;
    }

    MessagePlugin.success(isMoveIntoCommon ? t('im.public.move_into_common_success') : t('im.public.remove_common_success'));
  } catch (error) {
    console.error('更新常用聊天状态失败:', error);
    MessagePlugin.warning(isMoveIntoCommon ? t('im.public.move_into_common_error') : t('im.public.remove_common_error'));
  }
};

const onMenuSelected = async (menu) => {
  const action = menu.id as ConversationAction;
  if (action === ConversationAction.Top || action === ConversationAction.UnTop) {
    contextConversation.value.isTop = !contextConversation.value.isTop;
    onDbConversationSetting(contextConversation.value);
    await updateConversationTopAndMute(contextConversation.value);
    MessagePlugin.success(contextConversation.value.isTop ? t('im.public.topTip') : t('im.public.cancelTopTip'));
  } else if (action === ConversationAction.Mute || action === ConversationAction.UnMute) {
    contextConversation.value.isMute = !contextConversation.value.isMute;
    // if (contextConversation.value.conversationType === 3) {
    //   // 群聊设置免打扰通知,调用融云setConversationNotificationLevel api
    //   setConversationNotification(contextConversation.value.targetId, 3, action === ConversationAction.Mute ? 5 : 0)
    // }
    MessagePlugin.success(contextConversation.value.isMute ? t('im.public.isMute') : t('im.public.notMute'));
    onDbConversationSetting(contextConversation.value);
    updateConversationTopAndMute(contextConversation.value);

  } else if (action === ConversationAction.Hide) {
    const index = sessionList.value.findIndex((item) => item.localSessionId === contextConversation.value.localSessionId);
    if (index > -1) {
      removeConfirmTarget.value = { ele: contextTarget.value.ele };
      removeConfirmConversation.value = contextConversation.value;
    }

  } else if (action === ConversationAction.QuitGroup) {
    alertQuitGroup.value = true;
    alertQuitConversation = contextConversation.value;
  }

  // 移入/移出常用
  else if ([ConversationAction.MoveIntoCommon, ConversationAction.RemoveCommon].includes(action)) {
    await updateCommonChatStatus(contextConversation.value, action);
  }

  resetContextMenu();
};

const removeConfirmTarget = shallowRef<{ ele: HTMLElement}>(null);
const removeConfirmConversation = ref<ConversationToSave>();

const onConfirmRemove = () => {
  msgStore.onRemoveSession(removeConfirmConversation.value);
  onCancelRemove();
};

const onCancelRemove = () => {
  removeConfirmConversation.value = null;
  removeConfirmTarget.value = null;
};

const _getGroupInfo = (session: ConversationToSave) => {
  const groupInfo = msgStore.allGroups.find((item) => item.group === session.targetId);
  return groupInfo;
};

const getGroupName = (session: ConversationToSave) => {
  if (session.conversationType === 6) {
    return session.name;
  }
  let groupInfo = _getGroupInfo(session);
  let member = msgStore.allMembers.get(session.targetId)?.get(session.myCardId);
  if (member?.comment) {
    return `${member?.comment} (${groupInfo?.name})`;
  }
  return groupInfo?.name || '';

};

const HideSendMsgTypes = [
  'server_message_middle',
  'server_message_middle_weather',
  'server_message_middle_birthday',
  'server_pair_add',
  'server_group_update',
  'SportStepsEnabled',
  'SportPlanAllUpToPar',
  'SportStepsRanking',
  'SportPlanCreated',
  'PredefinedDayReminder',
  'DayReminder',
  'client_blacklist',
];

const getSessionPreviewPrefix = (session: ConversationToSave) => {
  // 没有编辑框助手类不展示草稿。
  if (session.draft && (session?.conversationType !== 6 || session.localSessionId === 'assistant8app8file8helper') && session?.inSession) {
    let draft = session.draft ? JSON.parse(session.draft)?.html : session.draft;
    try {
      if (session.draft) {
        draft = getRichText(session.draft, true);
      }
    } catch (error) {
      console.error('getRichText error', error);
    }
    return `<span class="session_list_editor_draft"><span class='draft_span'>[草稿]</span>${draft}</span>`;
  }

  const msg = session.latestMessage;
  if (!msg) {
    return '';
  }

  const isSender = getIsSender(msg, session);
  if (isSender && msg?.sentStatus !== SentStatus.SENT) {
    return getSessionPreviewText(session);
  }

  const previews = [] as string[];
  // 已读消息后，不展示特别关注
  if (session.unreadCount > 0 && getIsFollow(msg, isSender)) {
    previews.push(`<span class='follow_span'>[${t('im.msg.specialAttention')}]</span>`);
  }

  if (session.unreadMentionedCount > 0) {
    previews.push(`<span class='mentioned_span'>[${t('im.public.atMe')}]</span>`);
  }

  // 仅单聊，且同事关系，显示已读、未读
  if (msg?.contentExtra?.contentType && ['CO_WORKER', 'PLATFORM_FRIEND', 'IDLE_TEMPORARY'].includes(session.relation) && ![2101, 111].includes(msg.messageType) && !['meeting', 'server_message_middle'].includes(msg.contentExtra.contentType) && isSender) {
    let text;
    if (!msg.hasOwnProperty('isRead') || msg.isRead || msg.receiptTime > 0) {
      text = `[${t('im.msg.read')}]`;
    } else {
      text = `<span class='unread_span'>[${t('im.msg.unread')}]</span>`;
    }
    previews.push(text);
  }

  if (['0', '1', '2', '3', '10', '15', '20', '22', '23'].includes(session.relation)
    && session.myCardId !== msg?.contentExtra?.senderId
    && HideSendMsgTypes.includes(msg?.contentExtra?.contentType) === false
  ) {
    const sender = getMsgSenderName(session.latestMessage);
    if (sender) {
      previews.push(sender);
      previews.push(': ');
    }
  }

  const msgPreview = getSessionPreviewText(session);
  msgPreview && previews.push(msgPreview);
  return previews.join('');
};

const getIsFollow = (msg: MessageToSave, isSender:boolean) => {
  if (!isSender && contactStore.isFollow(msg?.contentExtra?.senderId)) {
    return true;
  }
  return false;
};

const getSessionPreviewText = (session: ConversationToSave) => {
  if (session.latestMessage) {
    return getConversationMessagePreview(session.latestMessage, session.myCardId);
  }

  return '';
};

// 退出群聊
let alertQuitConversation: ConversationToSave = null;
const alertQuitGroup = ref(false);
const cleanMsg = ref(false);

const quitGroup = async () => {
  if (!alertQuitConversation) {
    return;
  }

  const groupId = alertQuitConversation.targetId;
  const openId = getOpenid();

  const groupRes = await getGroupInfo(groupId);
  const rid = groupRes.members.length > 1 ? '#' : openId;
  const res = await quitGroupApi(groupId, alertQuitConversation.myCardId, rid, !cleanMsg.value);
  if (res.status === 200) {
    useMessageStore().onQuitGroupConversation(alertQuitConversation, rid, !cleanMsg.value);
  }
  alertQuitGroup.value = false;
  alertQuitConversation = null;

};

// 双击左侧菜单，定位会话
const conversationsRef = ref();
let lastLocalId: string = null;

onBeforeUnmount(() => {
  console.log('====>onBeforeUnmount');
  msgEmit.off('locate');
  window.removeEventListener('online', () => netStore.onOnline());
  window.removeEventListener('offline', () => netStore.onOffline());
});

onMounted(() => {
  contactStore.setFollowList();
  window.addEventListener('online', () => netStore.onOnline());
  window.addEventListener('offline', () => netStore.onOffline());
  msgEmit.on('locate', (init = false) => {
    console.log('===>init', init, lastLocalId);
    if (commonChatActive.value && commonListCounts.value === 0) {
      // 常用列表定位
      commonChatClick(false);
    }
    nextTick(() => {
      scrolLocateTo(init);
    });
  });
});

const scrolLocateTo = (init) => {
  if (init) {
    lastLocalId = null;
    return locateToLatestMsgSession(true);
  }
  if (!lastLocalId) {
    return locateToLatestMsgSession(false);
  }
  const findedSessionList = commonChatActive.value ? commonSessionList.value : sessionListActive.value;
  const lastItemIndex = findedSessionList.findIndex((item) => item.localSessionId === lastLocalId);
  let scrollIndex = 0;
  for (let i = lastItemIndex + 1; i < findedSessionList.length; i++) {
    const item = findedSessionList[i];
    const msg = item.latestMessage;
    // 找到下一个
    if (item.unreadCount && msg) {
      scrollIndex = i;
      break;
    }
  }
  scrollToSession(scrollIndex);
};
// 滚动到指定会话
const scrollToSession = (index: number) => {
  const findedSessionList = commonChatActive.value ? commonSessionList.value : sessionListActive.value;
  console.log('===>scrollToSession', index, lastLocalId);
  if (index === 0 && !findedSessionList[index].unreadCount) {
    lastLocalId = null;
    let ele: HTMLElement = null
    if(commonChatActive.value){
       ele = conversationsRef.value.querySelectorAll('.common-chat-active')?.item(0);
    }else{
     ele = conversationsRef.value.querySelectorAll('.common-conversation')?.item(0);
    }
    ele?.scrollIntoView({ behavior: 'smooth', block: 'start' });
    return;
  }
  const item = findedSessionList[index];
  lastLocalId = item.localSessionId;
  const domId = commonChatActive.value ? '#conversationCommon' : '#conversationAll';
  const ele = conversationsRef.value.querySelector(domId).querySelectorAll('.sessionItem')?.item(index);
  ele?.scrollIntoView({ behavior: 'smooth', block: 'start' });
};

// 定位到最近消息的会话
const locateToLatestMsgSession = (init) => {
  if (init && commonListCounts.value > 0) {
    const ele = conversationsRef.value.querySelectorAll('.common-conversation')?.item(0);
    ele?.scrollIntoView({ behavior: 'smooth', block: 'start' });
    return;
  }
  const findedSessionList = commonChatActive.value ? commonSessionList.value : sessionListActive.value;
  const index = findedSessionList.findIndex((item) => item.unreadCount > 0);
  scrollToSession(index > -1 ? index : 0);
};

let timer;
const onScroll = (e) => {
  // 滚动15条后加载20条的摘要
  // if(conversationsRef.value.scrollTop >= (msgStore.scrollLength - 15) * 62){
  // msgStore.setScrollLength()
  // msgStore.loadSessionSummary()
  // msgStore.getConversationList({offset:21,count:20})

  // }

  e.target.dataset.scroll = true;
  if (timer) {
    clearTimeout(timer);
  }
  timer = setTimeout(() => {
    e.target.dataset.scroll = false;
  }, 250);
  onCancelRemove();
};

// 添加安全HTML指令
const vSafeHtml = {
  mounted(el: HTMLElement, binding: any) {
    // 创建一个临时的div来解析HTML
    const temp = document.createElement('div');
    temp.innerHTML = binding.value;

    // 移除所有script标签
    const scripts = temp.getElementsByTagName('script');
    while (scripts.length > 0) {
      scripts[0].parentNode?.removeChild(scripts[0]);
    }

    // 移除所有style标签
    const styles = temp.getElementsByTagName('style');
    while (styles.length > 0) {
      styles[0].parentNode?.removeChild(styles[0]);
    }

    // 移除所有style属性
    const elements = temp.getElementsByTagName('*');
    for (let i = 0; i < elements.length; i++) {
      elements[i].removeAttribute('style');
    }

    // 设置处理后的内容
    el.innerHTML = temp.innerHTML;
  },
  updated(el: HTMLElement, binding: any) {
    // 更新时也执行相同的处理
    const temp = document.createElement('div');
    temp.innerHTML = binding.value;

    const scripts = temp.getElementsByTagName('script');
    while (scripts.length > 0) {
      scripts[0].parentNode?.removeChild(scripts[0]);
    }

    const styles = temp.getElementsByTagName('style');
    while (styles.length > 0) {
      styles[0].parentNode?.removeChild(styles[0]);
    }

    const elements = temp.getElementsByTagName('*');
    for (let i = 0; i < elements.length; i++) {
      elements[i].removeAttribute('style');
    }

    el.innerHTML = temp.innerHTML;
  },
};

</script>

<style lang="less">
.session_list_editor_draft{
  .draft_span{
    color:#2069E3
  }
  p,div,h1,h2,h3,h4,h5,h6{
    max-height: 22px;
  }
  img {
    max-width: 18px;
    max-height: 18px;
  }
}
.text-truncate {

  .unread_span {
    color:#4D5EFF;
  }
  .mentioned_span {
    color:#FC7C14;
  }
  .follow_span {
    color:#e66800;
  }
}
</style>

<style lang="less" scoped>
@import "../style/variable.less";

.list {
    // flex: 0 0 288px;
    flex: 1;
    padding: 8px 4px;
    padding-top: 0;
    display: flex;
    align-items: stretch;
    flex-direction: column;
    gap: 2px;
    max-width: 288px;
    max-height: 100%;
    overflow: auto;
    position: relative;
}

.list::-webkit-scrollbar {
  width: 2px;
}

.list[data-scroll="false"]::-webkit-scrollbar-thumb {
  background-color: transparent;
}
.status-tips{
  text-align: center;
  display: flex;
  padding:8px 24px;
  align-items: center;
  justify-content: start;
  gap:8px;
  color: var(--kyy_color_alert_text, #1A2139);
  /* kyy_fontSize_2/regular */
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  border-radius: 8px 0px 0px 0px;
  background: var(--tagBG-kyy_color_tagBg_brand, #EAECFF);
  ::v-deep(.t-loading__text){
    margin-left: 8px;
    color: var(--kyy_color_alert_text, #1A2139);
  }
}
.disconnect{
  background: var(--kyy_color_alert_bg_error, #F7D5DB);
}
.item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 8px;
    border-radius: 8px;
    position: relative;
    cursor: default;
    &:hover {
        background-color: @bg-kyy-color-bg-list-hover;
    }
}

.active {
    background-color: @bg-kyy-color-bg-list-foucs;
    &:hover {
        background-color: @bg-kyy-color-bg-list-foucs;
    }
}

.conversation-info {
  flex: 1;
  margin-left: 12px;
  overflow: hidden;
}

.conversation-row1 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
  font-size: 14px;
  color: @text-kyy-color-text-1;
  line-height: 24px;
}

.conversation-row2 {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.badge {
  position: absolute;
  left: 36px;
  top: 12px;
  padding: 0 5px;
  min-width: 16px;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  background-color: @kyy-color-badge-bg;
  color: @kyy-color-badge-text;
  border-radius: 8px;
}

.badge-dot {
  position: absolute;
  left: 40px;
  top: 16px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.name {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    font-size: @font-size4;
}

.preview {
    font-size: 14px;
    line-height: 22px;
    height:22px;
    color: @text-kyy-color-text-2;
    flex: 1;
}

.time {
    font-size: @font-size6;
    color: @text-kyy-color-text-2;
    min-width: 36px;
    min-height: 22px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 20px;
    text-align: right;
}

.text-truncate {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.sending {
  animation: chat-sending 1s linear infinite;
}

@keyframes chat-sending {
  from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.error {
  color: #da2d19;
}
.remove-confirm {
  z-index: -1;
  position: absolute;
  left:0;
  height: 0;
  top: 0;
  right: 0;
}

.mute-icon {
  color: #828DA5;
}

.group-bar {
  padding: 4px;
  padding-bottom: 0;
  .group-list {
    display: flex;
    align-items: center;
    // justify-content: center;
    gap: 4px;
  }

  .group-item {
    flex-shrink: 0;
    display: flex;
    padding: 4px 10px;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: var(--text-kyy_color_text_3, #828DA5);
    cursor: pointer;
    user-select: none;

    &.active {
      background: linear-gradient(180deg, #DBDFFF 0%, #FAFAFF 100%);
      color: var(--brand-kyy_color_brand_default, #4D5EFF);
    }
    &:hover {
      background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
    }

    .icon-wrap {
      position: relative;
      height: 28px;
    }
    .icon {
      width: 28px;
      height: 28px;
    }
    .red-dot {
      position: absolute;
      top: -4px;
      right: -4px;
      width: 8px;
      height: 8px;
      flex-shrink: 0;
      border-radius: var(--kyy_radius_badge_full, 999px);
      border: 1px solid var(--kyy_color_badge_border, #FFF);
      background: var(--kyy_color_badge_bg, #FF4AA1);
    }
  }

  .divider {
    margin-top: 8px;
    height: 1px;
    background: var(--divider-kyy_color_divider_light, #ECEFF5);
  }
}

.common-chat-active {
  display: flex;
  padding: 5px 0px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  .btn-back {
    width: 28px;
    height: 28px;
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    .icon {
      font-size: 20px;
      color: #828DA5;
    }
  }
  .title {
    flex: 1;
    color: var(--text-kyy_color_text_1, #1A2139);
    text-align: center;
    text-overflow: ellipsis;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
  }
}

.session-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 24px;
  flex-grow: 1;
  color: var(--tagBG-kyy_color_tagBg_gray, #ECEFF5);
  text-align: center;
  height: 120px;
}
.group-more-btn {
  user-select: none;
  cursor: pointer;
  margin-top: 8px;
  display: flex;
  width: 20px;
  height: 36px;
  // padding: 8px 0;
  align-items: center;
  justify-content: center;
  gap: 4px;
  border-radius: 4px;
  background: var(--bg-kyy_color_bg_deep, #F5F8FE);
}
</style>
