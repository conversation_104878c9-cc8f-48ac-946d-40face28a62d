<template>
  <div class="left-mune-container">
    <icon class="icon-specials" :url="iconUrl" name="iconSpecial-graphics" />
    <div class="title" @click="test">
      {{ t("contacts.contacts") }}
      <div class="tricks">
        <Tricks :isDrag="false" size="small" uuid="通讯录-首页" />
      </div>
    </div>
    <div :class="['t-menu-container', isNotMac ? 'scrollbar' : '']">
      <t-menu theme="light" v-model="curRoute" v-model:expanded="menuExpand" style="width: 100%">
        <!-- <div class="split-line"></div> -->
        <div v-for="item in options" :key="item.routeName" style="margin-bottom: 4px">
          <t-menu-item :value="item.routeName" @click="goPages(item)">
            <template #icon>
              <img class="mr-icon" :src="item.icon" alt="" />
            </template>
            {{ item.name }}
            <div class="redpoint" v-if="item.routeName === 'contactsNew' && noticeCount">{{ noticeCount }}</div>
          </t-menu-item>
          <div v-if="item.routeName === 'contactsOrganize'" class="split-line" style="margin-bottom: 0"></div>
        </div>
        <!-- 工场1.7杨帆迭代隐藏掉内部组织、商协会 -->
        <div class="contacts-organization" v-if="true">
          <div class="menu-sub-title" v-if="organizationArray.length > 0">{{ t("contacts.internalOrganization") }}</div>
          <div v-for="(item, index) in organizationArray" :key="item.teamId">
            <!-- <div class="split-line" v-if="index !== 0"></div> -->
            <t-submenu :value="`contactsOrganization-${item.teamId}`" v-if="!item.isVerity">
              <template #icon>
                <img class="company-icon" :src="item.avatar || defaultLogo" alt="" />
                <!-- <avatar v-else style="margin-right: 4px" :userName="item.name"  avatarSize="38px"/> -->
              </template>
              <template #title>
                <div class="orgAuthIconBox">
                  <img v-if="orgAuthIcon(item)" :src="orgAuthIcon(item)" alt="" class="orgAuthIcon">
                  <div class="orgName line-1">{{ item.name }}</div>
                </div>
              </template>
              <t-menu-item v-if="item?.displayOrganize===1" :value="`contactsOrganization-${item.teamId}|${item.name}`" @click="goOrganize(item)">
                <template #icon>
                  <img
                    class="mr-icon"
                    style="margin-left: 16px; margin-right: 0;width: 25px"
                    src="@renderer/assets/img/xiaji_new0.png"
                    alt=""
                  />
                </template>

                {{ t("contacts.structure") }}
              </t-menu-item>
              <t-menu-item
                v-for="department in item.departments"
                :value="`contactsOrganization-${item.teamId}|${item.name}-${department.departmentId}`"
                @click="goOrganize(item, department)"
              >
                <template #icon>
                  <img
                    class="mr-icon mr-icon-xiaji"
                    src="@renderer/assets/img/xiaji_new1.png"
                    alt=""
                  />
                </template>
                {{
                  department.parentName
                    ? `${department.parentName}-${department.departmentName}`
                    : department.departmentName
                }}
              </t-menu-item>
              <template v-for="val in item?.platform">
                <t-menu-item v-if="val?.displayUserList === 1" :value="`contactsOrganization-${item.teamId}|${item.name}-PT`" @click="goOrganize({...item,type:val.type},{departmentId:'PT'})">
                  <template #icon>
                    <img
                      class="mr-icon mr-icon-xiaji"
                      src="@renderer/assets/img/xiaji_new1.png"
                      alt=""
                    />
                  </template>

                  {{ t("contacts.platformMember") }}
                </t-menu-item>
                <t-menu-item v-if="val?.displayAdminList === 1" :value="`contactsOrganization-${item.teamId}|${item.name}-DJR`" @click="goOrganize({...item,type:val.type},{departmentId:'DJR'})">
                  <template #icon>
                    <img
                      class="mr-icon mr-icon-xiaji"
                      src="@renderer/assets/img/xiaji_new1.png"
                      alt=""
                    />
                  </template>

                  {{ t("contacts.receiver") }}
                </t-menu-item>
              </template>
            </t-submenu>
            <t-menu-item value="mails" @click.capture.stop="cancelApply(item)" v-else>
              <template #icon>
                <img class="company-icon" :src="item.avatar" alt="" />
              </template>
              <div style="display: flex; align-items: center">
                <div style="display: flex;">
                  <img v-if="orgAuthIcon(item)" style="margin-right: 4px;width: 24px;height: 24px;" :src="orgAuthIcon(item)" alt="" />
                  <div class="verify-company">{{ item.name }}</div>
                </div>
                <div class="verify-mark">{{ t("contacts.verifying") }}</div>
              </div>
            </t-menu-item>
          </div>
          <div class="menu-sub-title" v-if="businessArray.length > 0">{{ t("contacts.businessAssociation") }}</div>
          <div v-for="(item, index) in businessArray" :key="item.teamId">
            <!-- <div class="split-line" v-if="index !== 0"></div> -->
            <t-submenu :value="`contactsOrganization-${item.teamId}`" v-if="!item.isVerity">
              <template #icon>
                <img class="company-icon" :src="item.avatar || defaultLogo" alt="" />
              </template>
              <template #title>
                <div class="orgAuthIconBox">
                  <img v-if="orgAuthIcon(item)" :src="orgAuthIcon(item)" alt="" class="orgAuthIcon">
                  <div class="orgName line-1">{{ item.name }}</div>
                </div>
              </template>
              <t-menu-item v-if="item?.displayOrganize===1" :value="`contactsOrganization-${item.teamId}|${item.name}`" @click="goOrganize(item)">
                <template #icon>
                  <img
                    class="mr-icon"
                    style="margin-left: 16px;margin-right: 0; width: 25px"
                    src="@renderer/assets/img/xiaji_new0.png"
                    alt=""
                  />
                </template>
                {{ t("contacts.structure") }}
              </t-menu-item>
              <t-menu-item
                v-for="department in item.departments"
                :value="`contactsOrganization-${item.teamId}|${item.name}-${department.departmentId}`"
                @click="goOrganize(item, department)"
              >
                <template #icon>
                  <img
                    class="mr-icon"
                    style="margin-left: 16px;margin-right: 0; margin-bottom: 45px; width: 25px"
                    src="@renderer/assets/img/xiaji_new1.png"
                    alt=""
                  />
                </template>
                {{
                  department.parentName
                    ? `${department.parentName}-${department.departmentName}`
                    : department.departmentName
                }}
              </t-menu-item>
              <template v-for="val in item?.platform">
                <t-menu-item v-if="val?.displayUserList === 1" :value="`contactsOrganization-${item.teamId}|${item.name}-PT`" @click="goOrganize({...item,type:val.type},{departmentId:'PT'})">
                  <template #icon>
                    <img
                      class="mr-icon mr-icon-xiaji"
                      src="@renderer/assets/img/xiaji_new1.png"
                      alt=""
                    />
                  </template>

                  {{ t("contacts.platformMember") }}
                </t-menu-item>
                <t-menu-item v-if="val?.displayAdminList === 1" :value="`contactsOrganization-${item.teamId}|${item.name}-DJR`" @click="goOrganize({...item,type:val.type},{departmentId:'DJR'})">
                  <template #icon>
                    <img
                      class="mr-icon mr-icon-xiaji"
                      src="@renderer/assets/img/xiaji_new1.png"
                      alt=""
                    />
                  </template>

                  {{ t("contacts.receiver") }}
                </t-menu-item>
              </template>
            </t-submenu>
            <t-menu-item value="mails" @click.capture.stop="cancelApply(item)" v-else>
              <template #icon>
                <img class="company-icon" :src="item.avatar" alt="" />
              </template>
              <div style="display: flex; align-items: center">
                <div style="display: flex;">
                  <img v-if="orgAuthIcon(item)" style="margin-right: 4px;width: 24px;height: 24px;" :src="orgAuthIcon(item)" alt="" />
                  <div class="verify-company">{{ item.name }}</div>
                </div>
                <div class="verify-mark">{{ t("contacts.verifying") }}</div>
              </div>
            </t-menu-item>
          </div>
        </div>
        <!-- <div class="menu-sub-title" v-if="applyList.length > 0">{{ t("contacts.verify") }}</div>
        <div v-for="item in applyList" :key="item.id">
          <div class="split-line"></div>
          <t-menu-item value="mails" @click="cancelApply(item)">
            <template #icon>
              <img class="company-icon" :src="item.team?.logo || defaultLogo" alt="" />
            </template>
            <div style="display: flex; align-items: center">
              <div class="verify-company">{{ item.team.fullName }}</div>
              <div class="verify-mark">{{ t("contacts.verifying") }}</div>
            </div>
          </t-menu-item>
        </div> -->
      </t-menu>
    </div>
    <!-- 数智工场1.7迭代：通讯录的左侧隐藏底部【创建组织】、【加入组织】的入口。整个通讯录左侧菜单栏通栏展示。 -->
    <div class="footer-button" v-if="true">
      <t-button theme="default" variant="text" @click="createOrg"
        ><template #icon
          ><img
            style="width: 20px; height: 20px; margin-right: 3px"
            src="@renderer/assets/svg/icon_add.svg"
            alt="" /></template
        >{{ t("contacts.createOrganize") }}</t-button
      >
      <div class="footer-line"></div>
      <t-button theme="default" variant="text" @click="joinOrg"
        ><template #icon
          ><img
            style="width: 20px; height: 20px; margin-right: 3px"
            src="@renderer/assets/svg/icon_add.svg"
            alt="" /></template
        >{{ t("contacts.joinOrganize") }}</t-button
      >
    </div>

    <create-dialog ref="createDialoR" @sucess="organizeList" />
    <join-dialog ref="joinDialoR" @sucess="organizeList" />
    <apply-cancel ref="applyDialog" @sucess="organizeList" />

    <business v-model:visible="visi" teamId="628232057527746560" orgType="INDIVIDUAL" visibleType="governmentVisible"></business>
    <tip
      v-model:visible="tipVisible"
      :tip="t('zx.contacts.createErrTip')"
      :btn-confirm="t('identity.confirm')"
      @onconfirm="tipVisible = false"
    />
  </div>
</template>

<script lang="ts" setup>
import tip from "@renderer/views/setting/dialog/tip.vue";
import business from "@renderer/components/orgAuth/orgAuth.vue";
import { AddIcon, Icon } from "tdesign-icons-vue-next";
import { onMounted, ref, nextTick, watch } from "vue";
import defaultLogo from "@renderer/assets/defaultOrgLogo.svg";
import recentContact from "@renderer/assets/svg/recentContact_new.svg";
import iconstar from "@renderer/assets/svg/iconstar_new.svg";
import peoplePlus from "@renderer/assets/svg/peoplePlus_new.svg";
import iconpeople from "@renderer/assets/svg/iconpeople_new.svg";
import iconpeopless from "@renderer/assets/svg/iconpeopless_new.svg";
import iconpeoples from "@renderer/assets/svg/iconpeoples_new.svg";
import icontag from "@renderer/assets/svg/icontag.svg";
import avatar from "@renderer/components/kyy-avatar/index.vue";
import createDialog from "@renderer/components/contacts/dialog/createOrganize.vue";
import joinDialog from "@renderer/components/contacts/dialog/joinOrganize.vue";
import applyCancel from "@renderer/components/contacts/dialog/applyCancel.vue";
import { useRouter, useRoute } from "vue-router";
import { getOrganizeList, getApplyList, checkCreate, teamCount } from "@renderer/api/contacts/api/organize";
import { useContactsStore } from "@renderer/store/modules/contacts";
import { isNotMac } from "@renderer/views/zhixing/util";
import { useI18n } from "vue-i18n";
import { iconUrl } from "@renderer/plugins/KyyComponents";
import { orgTypeMap } from '@renderer/components/orgAuth/utils';
import LynkerSDK from '@renderer/_jssdk';
const { t } = useI18n();
const route = useRoute();
const contactsStore = useContactsStore();
const curRoute = ref("contactsRecent");
const options = ref([
  {
    routeName: "contactsNew",
    name: t("contacts.new"),
    icon: peoplePlus,
  },
  {
    routeName: "contactsRecent",
    name: t("contacts.recent"),
    icon: recentContact,
  },
  {
    routeName: "contactsFriend",
    name: t("contacts.friend"),
    icon: iconpeople,
  },
  {
    routeName: "contactsFollow",
    name: t("contacts.follow"),
    icon: iconstar,
  },

  {
    routeName: "contactsGroup",
    name: t("contacts.groups"),
    icon: iconpeopless,
  },
  {
    routeName: "contactsOrganize",
    name: t("contacts.organizePerson"),
    icon: iconpeoples,
  },
  {
    routeName: "contactsTag",
    name: t("zx.contacts.menuTag"),
    icon: icontag,
  },
]);
const organizationList = ref([]);
const applyList = ref([]);
const businessArray = ref([]);
const organizationArray = ref([]);
const router = useRouter();
const createDialoR = ref(null);
const joinDialoR = ref(null);
const applyDialog = ref(null);
const noticeCount = ref(0);
const menuExpand = ref([]);
const tipVisible = ref(false);

let autoJumpId = "";

const goPages = (item) => {
  router.push({ name: item.routeName });
};
/**
 * 跳转到组织成员
 * @param item
 * @param department {departmentId:部门组织ID，PT 平台，DJR 接收人}
 */
const goOrganize = (item, department?, autoJump?:string) => {
  const departmentId = department?.departmentId || -1;
  let type = item.type;
  if (item?.platform?.length) {
    type = item.platform[0].type;
  }
  console.error('type', item, type);

  const info = {
    teamId: item.teamId,
    team: item.name,
    avatar: item.avatar,
    cardId: item.cardId,
    isAdmin: item.isAdmin,
    canAdmin: item.canAdmin,
    type,
    departmentId
  };
  contactsStore.setOrganizationInfo(info);
  console.log('=====>',info,route.query,`/main/contactsIndex/organization/${item.teamId}/${departmentId}`, curRoute.value);
  // 有部门id但是菜单找不到，那么右边部门查找
  if (department?.departmentId === -1 && route.query?.departmentId) {
    router.replace({ path: `/main/contactsIndex/organization/${item.teamId}/${departmentId}`, query: { departmentId: route.query?.departmentId } });
  } else {
    router.replace({ path: `/main/contactsIndex/organization/${item.teamId}/${departmentId}` });
  }
  nextTick(() => {
    if (autoJump) {
      document.querySelector(`.t-is-opened`)?.scrollIntoView(true);
    }
  });
};
onMounted(() => {
  isMounted.value = true;
  nextTick(() => {
    autoJumpId = route.query?.teamId as string;
    organizeList();

    setTimeout(() => {
      isMounted.value = false;
    }, 300);
  });
});

const orgAuthIcon = (item) => {
  // 认证，0：未认证，1：已认证，2：认证审核中，3：认证已过期，4：已驳回
  let res = null;
  switch (item.auth) {
    case 0: {
      break;
    }
    case 1: {
      res = orgTypeMap.get(item?.type)?.icon;
      break;
    }
    case 2: {
      break;
    }
    case 3: {
      res = orgTypeMap.get(item?.type)?.icon_disabled;
      break;
    }
    case 4: {
      break;
    }
    default: {
      break;
    }
  }
  return res;
};

// 重新进入通讯录模块，获取最新组织数据，目前是多窗口，后期修改成一个窗口到通讯录模块做watch监听路由
const isMounted = ref(false);
LynkerSDK.ipcRenderer.removeAllListeners('refresh-page-data');
LynkerSDK.ipcRenderer.on('refresh-page-data', (event, value) => {
  if (!isMounted.value && value?.page === 'address_book') {
    // 默认最近联系人
    curRoute.value = 'contactsRecent'
    organizeList();
  }
});

const organizeList = () => {
  businessArray.value = [];
  organizationArray.value = [];
  getOrganizeList().then((res) => {
    console.log(res, "getOrganizeList");
    if (res.data.code === 0) {
      organizationList.value = res.data.data;
      res.data.data.map((item) => {
        if (item.type === 2) {
          businessArray.value.push(item);
        } else {
          organizationArray.value.push(item);
        }
      });
      jumpOrg();
      apiApplyList();
    }
  });
};
const jumpOrg = () => {
  nextTick(() => {
    if (autoJumpId) {
      const item = organizationList.value.find((v) => v.teamId == autoJumpId);
      // 筛选组织是否有对应部门，没有则跳转到默认组织架构 -1
      const departmentId = item.departments.find((v)=>v.departmentId ==  route.query?.departmentId) ? route.query?.departmentId : -1
      item &&
        ((curRoute.value = `contactsOrganization-${item.teamId}|${item.name}${departmentId !== -1 ?'-'+route.query?.departmentId:''}`),
        (menuExpand.value = [`contactsOrganization-${item.teamId}`]),
        goOrganize(item,{departmentId},autoJumpId));
        autoJumpId = "";
    }
  });
};
const apiApplyList = () => {
  applyList.value = [];
  getApplyList().then((res) => {
    console.log(res, "getApplyList");
    if (res.data.code === 0) {
      applyList.value = res.data.data.list;
      res.data.data.list.map(item =>{
        let val = {
          id: item.id,
          avatar: item.team?.logo || defaultLogo,
          name: item.team?.fullName,
          isVerity: true,
          type: item.team?.type,
          auth: item.team?.auth
        }
        if(item.team?.type === 2){
          businessArray.value.push(val);
        }else {
          organizationArray.value.push(val);
        }
      })
    }
  });
};
const createOrg = () => {
  checkCreate().then(({ data }) => {
    data.data ? createDialoR.value.beginCreate() : (tipVisible.value = true);
  });
};
const joinOrg = () => {
  joinDialoR.value.beginJoin();
};
const cancelApply = (item) => {
  applyDialog.value.show(item);
};
watch(
  () => contactsStore.newContactsNum,
  (newValue) => {
    noticeCount.value = newValue;
  },
  {
    immediate: true,
  },
);
watch(
  () => contactsStore.contactsOrgJumpId,
  (newValue) => {
    autoJumpId = newValue;
    // setTimeout(()=>{
    //   const item = organizationList.value.find(v => v.teamId === newValue);
    //   if (item) {
    //    curRoute.value = `contactsOrganization-${item.teamId}|${item.name}`
    //    goOrganize(item);
    //    contactsStore.setOrgId('')
    //   }
    // },2000)
  },
  {
    immediate: true,
  },
);
watch(
  ()=>route.query,
  (newValue)=>{
    // 在通讯录页面时，身份卡跳转部门
    if(newValue?.from ==='mainCard' &&  organizationList.value.length >  0){
      nextTick(()=>{
        autoJumpId = route.query?.teamId as string;
      jumpOrg()
      })
    }
  }
)

const visi = ref(false);
const test = () => {
  // visi.value = true;
};
</script>

<style lang="less" scoped>
.icon-specials {
  width: 8px;
  height: 8px;
  position: absolute;
  top: 0;
  left: 0;
  color: var(--bg-kyy-color-bg-software-foucs, #272b4f);
}
.left-mune-container {
  height: 100%;
  width: 368px;
  padding: 20px 0 20px 16px;
  border-right: 1px solid #e3e6eb;
  position: relative;
  .t-menu-container {
    height: calc(100% - 78px);
    // overflow-y: scroll;
    // overflow-x: hidden;
  }
  .title {
    position: relative;
    display: flex;
    align-items: center;
    padding-left: 2px;
    font-size: 16px;

    font-weight: 700;
    color: #13161b;
    line-height: 24px;
    margin-bottom: 20px;
    .tricks {
      display: inline-flex;
      margin-left: 8px;
    }
  }
  :deep(.t-default-menu__inner .t-menu) {
    padding: 0;
    padding-right: 16px;
  }
  :deep(.t-default-menu:not(.t-menu--dark) .t-menu__item.t-is-active:not(.t-is-opened)) {
    border-radius: 4px;
    background: var(--bg-kyy_color_bg_list_foucs, #e1eaff) !important;
    color: #13161b !important;
  }
  :deep(.t-default-menu .t-menu__item) {
    height: 52px !important;
    font-size: 16px;
    font-family: PingFang SC;
    color: var(--text-kyy-color-text-1, #1a2139);
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    margin-bottom: 2px;
  }

  :deep(.t-default-menu .t-menu__sub){
    .t-menu__item {
      background: none !important;
      .t-menu__content:hover{
        background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
      }
    }
    .t-menu__item.t-is-active:not(.t-is-opened){
      background: none !important;
      .t-menu__content{
        background: var(--bg-kyy_color_bg_list_foucs, #e1eaff) !important;
      }
    }
  }
  :deep(.t-default-menu .t-menu__sub .t-menu__item) {
    height: 40px !important;
    margin-bottom: 2px;
  }
  :deep(.t-default-menu .t-menu__item:hover) {
    background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
  }
  :deep(.t-default-menu .t-menu__sub .t-submenu__item .t-menu__content) {
    font-weight: 400;
    display: block;
    line-height: 40px;
    width: 100%;
    border-radius: 4px;
    padding:0 12px;
  }
  .split-line {
    max-width: 320px;
    margin: auto;
    height: 1px;
    background: var(--divider-kyy_color_divider_light, #eceff5);
    border-radius: 3px;
    margin-bottom: 5px;
    margin-left: -12px;
  }
  .verify-company {
    width: 138px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .verify-mark {
    width: 52px;
    height: 20px;
    padding: 0px 8px;
    border-radius: var(--kyy_radius_tag_full, 999px);
    background: var(--kyy_color_tag_bg_brand, #EAECFF);
    color: var(--kyy_color_tag_text_brand, #4D5EFF);
    text-align: center;
    /* kyy_fontSize_1/regular */
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
    margin-left: 8px;
  }
  .mr-icon {
    margin-right: 12px;
    margin-left: 4px;
  }
  .mr-icon-xiaji{
    margin-left: 16px;margin-right: 0; margin-bottom: 45px; width: 25px
  }
  .company-icon {
    width: 28px;
    height: 28px;
    margin-right: 12px;
    margin-left: 4px;
    border-radius: 50%;
    object-fit: cover;
  }
  .footer-button {
    //width: calc(100% - 30px);
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: space-around;

    width: 100%;
    border-top: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
    padding-top: 5px;
    .t-button {
      border: none !important;
      color: var(--color-button-text-secondray-kyy-color-button-text-secondray-font-default, #516082) !important;
      text-align: center;

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      :deep(.t-button__text) {
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
    }
    .footer-line {
      margin: auto 0;
      width: 1px;
      height: 12px;
      background-color: var(--divider-kyy_color_divider_light, #eceff5);
    }
  }
  .t-button--variant-outline {
    padding: 0 25px;
  }
}
.redpoint {
  position: absolute;
  right: 13px;
  top: 50%;
  transform: translateY(-50%);
  padding: 0 4px;
  background-color: #da2d19;
  border-radius: 8px;
  color: #fff;
  font-size: 14px;
  line-height: 16px;
}
.menu-sub-title {
  font-size: 14px;
  color: var(--text-kyy-color-text-3, #828da5);
  font-weight: 400;
  line-height: 22px;
  padding: 12px;
  background: var(--bg-kyy-color-bg-deep, #f5f8fe);
  margin-bottom: 4px;
}
.orgAuthIconBox{
  display: flex;
  align-items: center;
  .orgName{
    flex: 1;
  }
  .orgAuthIcon{
    width: 24px;
    height: 24px;
    margin-left: -3px;
    margin-right: 4px;
  }
}
</style>
