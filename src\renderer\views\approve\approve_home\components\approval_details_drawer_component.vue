<template>
  <div class="approval_details_drawer_component select-text">
    <div v-if="!isWhitePage">
      <t-drawer
        :visible="dialogVisble"
        :show-in-attached-element="props.showInAttachedElement"
        close-btn
        size="472px"
        class="approval_details_drawer"
        @overlay-click="close"
        @close="close"
      >
        <template #header>
          <div v-if="!showEmpty" class="header-title">
            <span>{{ t("approve.details") }}</span>
            <icon class="icon" name="iconerror" color="#828DA5" size="20" :url="iconUrl" @click="close" />
          </div>
        </template>
        <template #footer>
          <div v-if="!showEmpty" class="flex-align ddddd-root">
            <div class="drawer-footer">
              <div
                class="text-center"
                @click="
                  showApprovalMakeCopyDialogComponent = true;
                  conId();
                "
              >
                <icon class="icon" name="icontongan" size="20" :url="iconUrl" />
                <div>{{ t("approve.makeCopy") }}</div>
              </div>
              <div class="text-center" @click="showApprovalCommentDialogComponent = true">
                <icon class="icon" name="iconcomment" size="20" style="color: #516082" :url="iconUrl" />
                <div>{{ t("approve.comment") }}</div>
              </div>
              <t-popup v-model:visible="popupVisible" overlay-class-name="more-btn-list" placement="top" :destroy-on-close="true" trigger="click"
              >
                <div
                  v-if="
                    moreBtnList.length > 0 &&
                    !(approvalData.status == 1 && approvalData.cancel_type == 1 && !approvalData.is_handle_approval)
                  "
                  class="text-center"
                >
                  <icon class="icon" name="iconmore-a961a3l6" size="20" style="color: #516082" :url="iconUrl" />
                  <div>{{ t("approve.more") }}</div>
                </div>
                <template #content>
                  <div v-for="(item, index) in moreBtnList" :key="index" class="more-btn-item" @click="moreFn(item)">
                    <div class="flex-align">
                      <icon class="icon" :name="item.icon" size="20" color="#828DA5" :url="iconUrl" />
                      <div style="padding-left: 12px">{{ item.text }}</div>
                    </div>
                  </div>
                </template>
              </t-popup>
            </div>
            <div class="drawer-footer-button">
              <t-button
                v-if="approvalData.is_handle_approval"
                variant="outline"
                class="refuse-button"
                @click="
                  showApprovalOpinionsDialogComponent = true;
                  type = 2;
                "
              >
                <icon class="icon" name="iconerror" size="24" style="color: #d54941" :url="iconUrl" />
                {{ t("approve.refuse") }}
              </t-button>
              <t-button v-if="approvalData.is_handle_approval" class="agree-button" @click="agree">
                <icon class="icon" name="iconcorrect" size="24" :url="iconUrl" />
                {{ t("approve.agree") }}
              </t-button>
              <t-button
                v-if="approvalData.status == 2 && approvalData.can_urge_staff && approvalData.can_urge_staff.length > 0"
                variant="outline"
                class="min-80"
                @click="showSelectReminderDialogComponent = true"
                >催办
              </t-button>
              <t-button
                v-if="
                  approvalData.status == 1 &&
                  approvalData.can_urge_staff &&
                  approvalData.can_urge_staff.length > 0 &&
                  !approvalData.is_handle_approval
                "
                variant="outline"
                class="min-80"
                @click="showSelectReminderDialogComponent = true"
                >催办
              </t-button>
              <t-button
                v-if="
                  approvalData.status == 1 &&
                  approvalData.is_handle_cancel &&
                  approvalData.cancel_type == 1 &&
                  !approvalData.is_handle_approval
                "
                variant="outline"
                class="min-80"
                @click="revokeTwo"
                >{{ t("approve.revoke") }}
              </t-button>
              <t-button
                v-if="
                  (approvalData.status == 3 || approvalData.status == 1) &&
                  approvalData.cancel_type == 1 &&
                  !approvalData.is_handle_approval
                "
                variant="outline"
                theme="primary"
                style="background: #e6e9fe !important"
                @click="pushInitApproval"
                >{{ t("approve.Resubmit") }}
              </t-button>

              <t-button
                v-if="approvalData.status == 2 && approvalData.is_handle_cancel && approvalData.cancel_type == 1"
                variant="outline"
                class="min-80"
                @click="revokeTwo"
                >{{ t("approve.revoke") }}
              </t-button>
              <t-button
                v-if="approvalData.status != 2 && approvalData.is_handle_cancel && approvalData.cancel_type == 2"
                variant="outline"
                @click="stopCancel"
                >{{ t("approve.suspensionRevocation") }}
              </t-button>
              <t-button
                v-if="(approvalData.status == 2 && !approvalData.is_handle_approval) || approvalData.status == 4"
                variant="outline"
                style="background: #e6e9fe !important"
                theme="primary"
                @click="pushInitApproval"
                >{{ t("approve.Resubmit") }}
              </t-button>
            </div>
          </div>
        </template>
        <div v-if="!showEmpty && !loading" id="approval_details_drawer_component">
          <div v-if="approvalData" class="p12-24" style="background: #4c5eff">
            <div
              v-if="props.approvalDetailItem.member_name"
              class="flex-align approve-back"
              style="padding-bottom: 32px; cursor: pointer"
              @click="$emit('visibleFn', false)"
            >
              <icon class="icon" name="iconarrowlift" size="16" color="#fff" :url="iconUrl" />
              <div style="margin: 0">返回</div>
              <div>{{ props.approvalDetailItem.member_name }}{{ props.approvalDetailItem.set_name }}</div>
            </div>
            <div class="flexbox-j-s approve-no-item">
              <div class="approve-no">{{ t("approval.handover.num") }}：{{ approvalData?.approval_number }}</div>
              <div class="approve-icon">
                <div
                  style="display: flex; align-items: center; color: #fff; margin-right: 16px; cursor: pointer"
                  @click="shareFn"
                >
                  <icon class="icon" name="iconshare" color="#fff" size="20" :url="iconUrl" style="margin-right: 4px" />
                  分享
                </div>
                <div style="display: flex; align-items: center; color: #fff; cursor: pointer" @click="printApproval">
                  <icon
                    class="icon"
                    name="iconprinter"
                    color="#777"
                    size="20"
                    :url="iconUrl"
                    style="margin-right: 4px"
                  />
                  打印
                </div>
              </div>
            </div>
            <div class="flex-align approve-title-item">
              <div class="approve-title">
                {{ approvalData.member_name }}{{ t("approval.handover.submitted") }}{{ approvalData.set_name }}
              </div>
              <div
                v-if="approvalData.status == 1"
                class="status-1 line-1"
                style="max-width: 120px; font-weight: 600; font-size: 14px"
              >
                {{ approvalData.status_name }}
              </div>
              <div
                v-if="approvalData.status == 2"
                class="status-2 line-1"
                style="max-width: 120px; font-weight: 600; font-size: 14px"
              >
                {{ approvalData.status_name }}
              </div>
              <div
                v-if="approvalData.status == 3"
                class="status-3 line-1"
                style="max-width: 120px; font-weight: 600; font-size: 14px"
              >
                {{ approvalData.status_name }}
              </div>
              <div
                v-if="approvalData.status == 4"
                class="status-4 line-1"
                style="max-width: 120px; font-weight: 600; font-size: 14px"
              >
                {{ approvalData.status_name }}
              </div>
            </div>
            <div class="approve-department-item">
              <div>{{ approvalData.department }}</div>
              <div class="divider-1" />
              <div>{{ approvalData.project_name }}</div>
            </div>
            <div class="flex-align approve-company-item">
              <div class="approve-company-img">
                <img
                  v-if="approvalData.team_logo && approvalData.team_logo.length > 0"
                  :src="approvalData.team_logo"
                  alt=""
                />
                <img v-else src="@/assets/svg/clouddisk/temaavatar.svg" />
              </div>
              <div class="approve-company-name">
                {{ approvalData.team_name }}
              </div>
            </div>
          </div>

          <approvalDetailsDrawerFormComponent
            v-if="approvalData.free_form.length"
            :approval-data="approvalData"
            :form-data="formData"
          />
          <div class="divider-16" />
          <approvalDetailsDrawerFlowPathComponent v-if="approvalData.free_form.length" @visible-fn="resetInit" />
        </div>

        <Empty v-if="showEmpty && !loading" :tip="$t('approve.noPermission')">
          <img style="margin-top: 30vh" src="@/assets/approval/no-auth-data.svg" alt="" />
        </Empty>

        <div v-if="loading" class="loading-box"><t-loading /></div>

        <t-dialog
          v-model:visible="revokeVisible"
          theme="info"
          :header="t('approve.prompt')"
          :body="t('approve.cancelApplication')"
          attach="body"
          :cancel-btn="{
            content: t('approve.cancel'),
            variant: 'outline',
          }"
          @confirm="revokeApproveWithdrawDialog"
        />
      </t-drawer>
    </div>
    <!-- 进来白屏-->
    <div v-if="isWhitePage">
      <t-drawer
        v-model:visible="dialogVisble"
        close-btn
        size="472px"
        :footer="false"
        :header="false"
        @overlay-click="close"
        @close="close"
      >
        <div style="display: flex; align-items: center; justify-content: center; height: 100%">当前网络不可用</div>
      </t-drawer>
    </div>
    <!--    :menu="shareMenu"-->
    <!--    :team-id="shareTeamId"-->

    <div>
      <!-- type  1: 同意 2: 拒绝 -->
      <approvalOpinionsDialogComponent
        v-if="showApprovalOpinionsDialogComponent"
        :approval_id="props_approval_id"
        :visible="showApprovalOpinionsDialogComponent"
        :cloneArray="formCloneData"
        :type="type"
        :approval-data="approvalData"
        :is-zhixing="props.isZhixing"
        @visible-fn="approvalOpinionsDialogComponentFn"
      />

      <!--      催办-->
      <selectReminderDialogComponent
        v-if="showSelectReminderDialogComponent"
        :approval_id="props_approval_id"
        :approval-data="approvalData"
        :is-zhixing="props.isZhixing"
        :visible="showSelectReminderDialogComponent"
        @visible-fn="selectReminderDialogComponentFn"
      />

      <approvalTransferredDialogComponent
        v-if="showApprovalTransferredDialogComponent"
        :approval_id="props_approval_id"
        :visible="showApprovalTransferredDialogComponent"
        :current-handle-id="currentHandleId"
        :approval-data="approvalData"
        :is-zhixing="props.isZhixing"
        @visible-fn="approvalTransferredDialogComponentFn"
      />

      <!--加签 1:只有一个人  2：多人会签/或签-->
      <approvalCountersignDialogComponent
        v-if="showApprovalCountersignDialogComponent"
        :approval_id="props_approval_id"
        :visible="showApprovalCountersignDialogComponent"
        :approval-data="approvalData"
        :is-zhixing="props.isZhixing"
        :current-handle-id="currentHandleId"
        @visible-fn="approvalCountersignDialogComponentFn"
      />

      <approvalRebackDialogComponent
        v-if="showApprovalRebackDialogComponent"
        :approval_id="props_approval_id"
        :approval-data="approvalData"
        :is-zhixing="props.isZhixing"
        :visible="showApprovalRebackDialogComponent"
        @visible-fn="approvalRebackDialogComponentFn"
      />

      <approvalCommentDialogComponent
        v-if="showApprovalCommentDialogComponent"
        :visible="showApprovalCommentDialogComponent"
        :approval_id="props_approval_id"
        :approval-data="approvalData"
        :is-zhixing="props.isZhixing"
        @visible-fn="approvalCommentDialogComponentFn"
        @cancel="showApprovalCommentDialogComponent = false"
      />

      <!--      抄送-->
      <approvalMakeCopyDialogComponent
        v-if="showApprovalMakeCopyDialogComponent"
        :visible="showApprovalMakeCopyDialogComponent"
        :approval_id="props_approval_id"
        :current-handle-id="currentHandleId"
        :approval-data="approvalData"
        :is-zhixing="props.isZhixing"
        @visible-fn="approvalMakeCopyDialogComponentFn"
        @cancel="showApprovalMakeCopyDialogComponent = false"
      />

      <!--      <reportDetailsDrawerComponent-->
      <!--        :visible="showApprovalMakeCopyDialogComponent"-->
      <!--        :approval_id="props_approval_id"-->
      <!--        :current-handle-id="currentHandleId"-->
      <!--        @visibleFn="approvalMakeCopyDialogComponentFn"-->
      <!--      />-->

      <select-member
        v-if="shareVisible"
        v-model:visible="shareVisible"
        :menu="['recent', 'organize', 'groups']"
        :team-id="[approvalteamid]"
        :card-id="getStaffId()"
        @confirm="shareList"
      />

      <SelectDepartmentDialog
        v-if="selectDepartmentVisible"
        :visible="selectDepartmentVisible"
        :positions="positions"
        @visible-fn="selectDepartmentFn"
      ></SelectDepartmentDialog>
    </div>
  </div>
</template>

<script setup lang="ts" name="approval_details_drawer_component">
import { onMounted, ref, provide, onDeactivated, onBeforeMount, PropType, computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { iconUrl } from "@renderer/plugins/KyyComponents";
import { Icon } from "tdesign-icons-vue-next";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import {
  approveWithdraw,
  OAApproveDetail,
  OAInitiateApproval,
  approvePrint,
  approveSkip,
  approveJudgeSubmitAgain,
  approveReturnNodeId,
  approveDeviceDetailById,
  approveCenterCount,
  approveCommonDetailOpenid,
  approveForeignCount,
} from "@renderer/api/approve.ts";
import {
  setFreeForm,
  setProceduresReadOnly,
  setCalculator,
  fieldListUpdate,
  initApprovalData,
  resubmitFreeformDisp,
} from "@renderer/views/approve/init_apply/formComponents/types.js";
import lodash, { debounce } from "lodash";
import { sendApplicationMsg, MsgShareType } from "@renderer/utils/share";
import { baseURL } from "@renderer/api/contacts/api/recent";
import selectMember from "@renderer/components/rk-business-component/select-member/simple-add-member.vue";
import { useI18n } from "vue-i18n";
import { useChatExtendStore } from '@renderer/views/message/service/extend';
import { getBaseUrl } from "@/utils/apiRequest";
import approvalDetailsDrawerFormComponent from "./approval_details_drawer_form_component.vue";
import approvalDetailsDrawerFlowPathComponent from "./approval_details_drawer_flow_path_component.vue";
import approvalOpinionsDialogComponent from "./approval_opinions_dialog_component.vue";
import selectReminderDialogComponent from "./select_reminder_dialog_component.vue";
import approvalTransferredDialogComponent from "./approval_transferred_dialog_component.vue";
import approvalCountersignDialogComponent from "./approval_countersign_dialog_component.vue";
import approvalRebackDialogComponent from "./approval_reback_dialog_component.vue";
import approvalCommentDialogComponent from "./approval_comment_dialog_component.vue";
import approvalMakeCopyDialogComponent from "./approval_make_copy_dialog_component.vue";
import SelectDepartmentDialog from "./select_department_dialog.vue";
import { useApprovalStore } from "@/store/modules/approval";
import { msgJumpToApprove } from "@/views/message/service/msgUtils";
import Empty from "@/components/common/Empty.vue";
import reportDetailsDrawerComponent from "@/views/square/phone-album/components/report_details_drawer_component.vue";
import { configDataStore } from "@/store/modules/configData";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();
const props = defineProps({
  visible: {
    default: false,
  },
  isCheckDetail: {
    default: false,
  },
  approvalDetailItem: {
    type: Object,
    default: () => {},
  },
  showInAttachedElement: {
    default: false,
  },
  autoAction: {
    type: String as PropType<'' | "agree" | "reject">,
    required: false,
  },
  isZhixing: {
    default: false,
  },
});



const route = useRoute();
const router = useRouter();

const approvalteamid = ref();
const isWhitePage = ref(false);
const loading = ref(true);

const dialogVisble = computed(() => props.visible);

const approvalStore = useApprovalStore();
const approvalItem = ref(approvalStore.approvalItem);
const props_approval_id = ref(props.approvalDetailItem.approval_id || props.approvalDetailItem.approvalId);

const approvalData = ref({
  free_form: [],
  opinion: [],
  operation_permission: {},
  process_set_name: "",
  streams: [[]],
  team_id: '',
  project_id: '',
  initiates_info: {
    approval_number: "string",
    name: "string",
    department: "string",
    initiate_time: "string",
    project_name: "string",
  },
});
const formData = ref({});
const depId = ref(1);
const jobId = ref("0");
const projectId = ref("0");
const reSubmitFirst = ref(true);
const resubmit = ref(false);
const approvalInfoVisible = ref(false);

const getStaffId = () => [`$${approvalItem.value.staffId}`];

const showEmpty = ref(false);
const currentHandleId = ref(null);
const formCloneData = ref({});
const init = async (queryDataId?: number) => {
  const { departmentId, dataid, id, record } = route.query;
  showEmpty.value = false;
  const teamId = props?.approvalDetailItem?.team_id || props?.approvalDetailItem?.teamId || route?.query?.teamId;
  if (teamId) {
    approvalteamid.value = teamId;
    localStorage.setItem("approvalteamid-im", approvalteamid.value);
  } else {
    approvalteamid.value = localStorage.getItem("approvalteamid");
  }
  console.log("打印一下是否有teamId", route.query, props.approvalDetailItem);
  if (departmentId) {
    depId.value = departmentId;
  }
  if (route.query.jobId) {
    jobId.value = route.query.jobId;
  }
  if (record) {
    record.value = JSON.parse(record);
  }
  projectId.value = route.query.projectId;
  let res = {};
  reSubmitFirst.value = false;

  if (queryDataId || dataid) {
    // 再次发起传递过来的
    resubmit.value = true;
    res = await OAApproveDetail({
      approve_id: queryDataId || dataid,
      project_id: projectId.value,
    });
    debounceSearch();
  } else {
    try {
      res = await OAApproveDetail({
        approve_id: props.approvalDetailItem.approval_id,
      });
    } catch (err) {
      console.log(err, "测试");
      if (err.code == "ERR_NETWORK") {
        isWhitePage.value = true;
      }
    }
    console.log("再次发起OAInitiateApproval", res);

    projectId.value = res.data.data.project_id;
    console.log(props.approvalDetailItem.approval_id);
  }
  if (!res) {
    showEmpty.value = true;
    console.log("再次发起OAInitiateApproval123", res);
  }
  console.log(res, "res.data");

  if (res.data.code !== 0) {
    MessagePlugin.warning(res.data.message);
    return;
  }

  // 再次发起进这里
  if (dataid) {
    setProceduresReadOnly(res.data.data.original_streams);
  } else {
    setProceduresReadOnly(res.data.data.streams);
  }
  console.log("res.data.data.streams", res.data.data.streams);
  let rule = setFreeForm(res);
  setCalculator(res.data.data.free_form);
  approvalData.value = res.data.data;
  initApprovalData(approvalData.value);

  approvalData.value.free_form.forEach((item) => {
    for (const ruleKey in rule) {
      if (item.id === ruleKey) {
        item = rule[ruleKey];
        formData.value[ruleKey] = rule[ruleKey];
      }
      console.log("rule111", ruleKey);
    }

    if (!approvalData.value.is_handle_approval) {
      item.editable = false;
      approvalData.value.free_form.forEach((subItem) => {
        if (Array.isArray(subItem.value)) {
          subItem.value.forEach((ssubItem) => {
            console.log("ssubItem", ssubItem);
            if (Array.isArray(ssubItem)) {
              ssubItem.forEach((sssubItem) => {
                sssubItem.editable = false;
              });
            }
          });
        }
      });
    }

    console.log(approvalData.value.free_form, " approvalData.value.free_form");

    console.log("12312312321", approvalData.value);
    if (approvalData.value.record_widgets && approvalData.value.record_widgets.length) {
      approvalData.value.record_widgets.forEach((subItem) => {
        if (item.id == subItem && item.editable) {
          item.editable = false;
        }
      });

      if (Array.isArray(item.value) && item.value.length) {
        item.value.forEach((subItem) => {
          if (Array.isArray(item.subItem)) {
            subItem.forEach((ssubItem) => {
              console.log(ssubItem, "item.value");

              approvalData.value.record_widgets.forEach((subItem) => {
                if (ssubItem.associatedId == subItem && ssubItem.editable) {
                  ssubItem.editable = false;
                }
                if (ssubItem.associatedId != subItem && ssubItem.editable) {
                  item.editable = true;
                }
              });
            });
          }
        });
      }
      console.log(approvalData.value, "item.origin");
    }
  });
  console.log(approvalData.value.free_form, "item.id == subItem");
  console.log("formData", formData.value);

  // 再次发起进这里
  if (dataid) {
    await resubmitFreeformInit(id);
  }
  console.log(approvalData.value.free_form, "item.id == subItem");
  console.log("formData", formData.value);
  // 再次发起进这里
  if (dataid) {
    // this.$store.commit('global/setHeadMes', {
    //   headTitle: res.data.institution_member + '提交的' + res.data.process_set_name,
    //   headType: 1
    // })
    approvalData.value.initiates_info = {};
    approvalData.value.initiates_info.name = approvalData.value.institution_member;
    approvalData.value.initiates_info.initiate_time = approvalData.value.initiate_time;
    approvalData.value.initiates_info.approval_number = approvalData.value.approval_number;
    approvalData.value.initiates_info.post = approvalData.value.post;
    approvalData.value.initiates_info.project_name = approvalData.value.project_name;
    approvalData.value.initiates_info.department = approvalData.value.department;
  } else {
    // this.$store.commit('global/setHeadMes', {
    //   headTitle: res.data.initiates_info.name + '提交的' + res.data.name,
    //   headType: 1
    // })
  }
  const allFields = approvalData.value.free_form.concat(
    approvalData.value.free_form
      .filter((f) => f.type === "FieldList")
      .map((f) => f.value)
      .flat()
      .flat(),
  );
  if (!allFields.find((item) => item.required)) {
    approvalInfoVisible.value = true;
  }
  console.log(dataid, "dataiddataid");
  if (dataid) {
    // 再次提交处理editable readable
    const quotation = approvalData.value.free_form.find((f) => f.type === "Quotation");
    if (quotation) {
      quotation.value = null;
    }
    const project = approvalData.value.free_form.find((f) => f.type === "Project");
    const projectDetail = localStorage.getItem("project_detail");
    if (project) {
      if (projectDetail != null && projectDetail !== "null") {
        project.value = JSON.parse(projectDetail);
      }
    }
    console.log(approvalData.value.original_streams, "this.approvalData.original_streams");
    approvalInfoVisible.value = true;
    setTimeout(() => {
      // 异步问题临时解决办法
      reSubmitFirst.value = false;
    }, 5000);
  } else {
    reSubmitFirst.value = false;
  }
  approvalData.value.streams.forEach((item) => {
    item.forEach((subItem) => {
      if (subItem.id == approvalData.value.current_node_id) {
        if (subItem.approvers.length > 0) {
          currentHandleId.value = subItem.approvers[0].member_id || subItem.approvers[0].id;
        }
      }
    });
  });
  let contactIds = [];
  approvalData.value.free_form.forEach((item) => {
    if (item.type == "Contact") {
      item.value.forEach((subItem) => {
        if (subItem.staff_id) {
          contactIds.push(subItem.staff_id);
        }
      });
    }
  });
  fieldListUpdate(approvalData.value);
  if (contactIds.length && contactIds.length > 0) {
    approveDeviceDetailByIdFn(contactIds);
  }

  approveCenterCount(approvalData.value.team_id).then((res) => {
    if (res.data.code === 0) {
      const approvalStore = useApprovalStore();
      approvalStore.pendingApprovalCountData = res.data.data.pending_approval;
    }
  });

  approveForeignCount().then((res) => {
    if (res.data.code === 0) {
      const approvalStore = useApprovalStore();
      approvalStore.approveCenterCountData = res.data.data.pending_approval;
    }
  });

  ipcRenderer.invoke("update-approve");

  console.log(contactIds, approvalData.value, "contactIds");

  console.log(currentHandleId.value, "currentHandleId.value");

  formCloneData.value=JSON.parse(JSON.stringify(approvalData.value.free_form))
};


const initBefore = async (id?) => {
  // loading.value = true;
  approvalData.value = {
    free_form: [],
    opinion: [],
    operation_permission: {},
    process_set_name: "",
    streams: [[]],
    team_id: '',
    project_id: '',
    initiates_info: {
      approval_number: "string",
      name: "string",
      department: "string",
      initiate_time: "string",
      project_name: "string",
    },
  };
  console.log(props.visible, props.approvalDetailItem, "props.visible");
  await init(id);
  loading.value = false;
  await initContolBtn();
  if (props.autoAction && (props.autoAction == "agree" || props.autoAction == "reject")) {
    if (props.autoAction == "agree") {
      type.value = 1;
    }
    if (props.autoAction == "reject") {
      type.value = 2;
    }
    showApprovalOpinionsDialogComponent.value = true;
    console.log(approvalData.value, props.autoAction == "reject", type.value, "approvalData.value");
  }
  isWhitePage.value = false;
  approvalStore.skip_approve_id = null;
};

watch(
  () => props.visible,
  debounce(async (newValue) => {
    console.log('====>oldValue', newValue);
    if (newValue) {
      loading.value = true;
      if (approvalStore.skip_approve_id) {
        props_approval_id.value = approvalStore.skip_approve_id;
      } else {
        props_approval_id.value = props.approvalDetailItem.approval_id || props.approvalDetailItem.approvalId;
      }
      approvalStore.setPropsApprovalId(props_approval_id.value);
      await initBefore(props_approval_id.value);
    }
  }, 1000),
  {
    immediate: true,
  },
);
watch(
  ()=> props.approvalDetailItem.approvalId,
  (newValue,oldValue) => {
    props_approval_id.value = props.approvalDetailItem.approval_id || props.approvalDetailItem.approvalId;
    approvalStore.setPropsApprovalId(props_approval_id.value);
    initBefore(props_approval_id.value);
  }
)
onMounted(async () => {
  console.log('====>onMounted');
  // if (props.visible) {
  //   showEmpty.value = true;

  //   await initBefore();
  // }
});

const initContolBtn = async () => {
  moreBtnList.value = [];
  const nodeList = await approveReturnNodeIdFn();
  if (approvalData.value.is_handle_approval && approvalData.value.skip_approve_id != 0) {
    moreBtnList.value.splice(0, 0, {
      id: 0,
      icon: "iconskip",
      text: t("approve.skip"),
    });
  }
  if (approvalData.value.can_urge_staff && approvalData.value.can_urge_staff.length > 0) {
    moreBtnList.value.splice(1, 0, {
      id: 1,
      icon: "iconurge",
      text: t("approve.urge"),
    });
  }
  if (approvalData.value.is_handle_approval && approvalData.value.operation_permission.goto) {
    moreBtnList.value.splice(1, 0, {
      id: 2,
      icon: "icondelive",
      text: t("approve.deliver"),
    });
  }
  if (approvalData.value.is_handle_approval && approvalData.value.operation_permission.add) {
    moreBtnList.value.splice(3, 0, {
      id: 3,
      icon: "iconsign",
      text: t("approve.sign"),
    });
  }
  if (approvalData.value.status !== 2 && approvalData.value.is_handle_cancel && approvalData.value.cancel_type == 1) {
    moreBtnList.value.splice(3, 0, {
      id: 5,
      icon: "iconreturn",
      text: t("approve.revoke"),
    });
  }
  if (
    (approvalData.value.status !== 2 && approvalData.value.status !== 4) ||
    (approvalData.value.is_handle_approval && approvalData.value.status == 2)
  ) {
    const data = [
      {
        id: 6,
        icon: "iconsubmit",
        text: t("approve.Resubmit"),
      },
    ];
    moreBtnList.value.push(...data);
  }
  if (approvalData.value.is_handle_cancel && approvalData.value.cancel_type == 2) {
    const data = [
      {
        id: 7,
        icon: "return",
        text: t("approve.suspensionRevocation"),
      },
    ];
    moreBtnList.value.push(...data);
  }

  console.log(moreBtnList.value, "长度是多少");

  // 最后处理 如果按钮只剩下一个再次提交的时候 并且状态是3 就隐藏
  if (moreBtnList.value.length == 1 && moreBtnList.value[0].id == 6) {
    if (
      approvalData.value.status == 3 &&
      approvalData.value.cancel_type == 1 &&
      !approvalData.value.is_handle_approval
    ) {
      moreBtnList.value.splice(0, 1);
    }
  }
};

const approveReturnNodeIdFn = () => {
  approveReturnNodeId(props.approvalDetailItem.approval_id, props.approvalDetailItem.team_id).then((res) => {
    console.log(res.data.data, "approveAddSign");
    if (res.data.code === 0) {
      let nodeList = [];
      nodeList = res.data.data.node_list;
      if (
        approvalData.value.is_handle_approval &&
        approvalData.value.operation_permission.fallback &&
        nodeList.length > 0
      ) {
        moreBtnList.value.splice(2, 0, {
          id: 4,
          icon: "iconreject",
          text: t("approve.return"),
        });
      }
    }
    return null;
  });
};

const approveDeviceDetailByIdFn = async (data) => {
  console.log('====>approveDeviceDetailByIdFn', approveDeviceDetailByIdFn);
  const res = await approveDeviceDetailById({
    ids: data,
  });
  if (res.data.code == 0) {
    res.data.data.forEach((dataItem) => {
      if (dataItem.deleted_at) {
        approvalData.value.free_form.forEach((item) => {
          if (item.type == "Contact") {
            item.value.forEach((subItem) => {
              if (subItem.staff_id == dataItem.staffId) {
                subItem.is_delete = 1;
              }
            });
          }
        });
      }
    });
  }
  console.log("获取离职情况", res);
};

const resubmitFreeformInit = async (id) => {
  const res = await OAInitiateApproval({
    department_id: depId.value,
    job_id: jobId.value,
    set_id: id,
    projectId: projectId.value,
  });
  if (res.data.code !== 0) {
    MessagePlugin.warning(res.data.message);
    // router.replace(
    //   route.query.mode === 'im'
    //     ? '/approve_home?mode=im&componentName=approve_center'
    //     : '/approve_home?componentName=approve_center',
    // );

    return;
  }
  approvalData.value.initiates_info.approval_number = res.data.data.initiates_info.approval_number; // 审批编号更换为最新的
  approvalData.value.initiates_info.post = res.data.data.initiates_info.post; // 审批编号更换为最新的
  approvalData.value.initiates_info.project_name = res.data.data.initiates_info.project_name; // 审批编号更换为最新的
  approvalData.value.initiates_info.department = res.data.data.initiates_info.department; // 审批编号更换为最新的

  resubmitFreeformDisp(res.data.data.free_form, approvalData.value.free_form);
};

const isRequiredFieldsFilled = () => {
  const fields = approvalData.value.free_form;
  const allRequiredFields = fields
    .concat(
      fields
        .filter((f) => f.type === "FieldList")
        .map((f) => f.value)
        .flat()
        .flat(),
    )
    .filter((f) => f.required && f.show);
  for (const field of allRequiredFields) {
    // 特殊的field
    if (field.type === "DateRangePicker") {
      if (field.dateType === 0 && (!field.startTimeValue || !field.endTimeValue || !field.durationValue)) {
        return false;
      }
      if (
        field.dateType !== 0 &&
        (!field.startTimeValue ||
          !field.startTimeValue2 ||
          !field.endTimeValue ||
          !field.endTimeValue2 ||
          !field.durationValue)
      ) {
        return false;
      }
    } else if (lodash.isArray(field.value)) {
      // value为数组的field
      if (field.value.length === 0) {
        return false;
      }
    } else if (field.value === null || field.value === undefined || field.value === "") {
      // 普通的field
      return false;
    }
  }
  return true;
};

const debounceSearch = lodash.debounce(
  () => {
    console.log("isRequiredFieldsFilled");
    if (isRequiredFieldsFilled()) {
      console.log(reSubmitFirst.value, "reSubmitFirstreSubmitFirstreSubmitFirst");
      if (!reSubmitFirst.value) {
        // getApprovalInfo();
      }
    }
  },
  500,
  {
    leading: false,
    trailing: true,
  },
);
const type = ref(1);
const showApprovalOpinionsDialogComponent = ref(false);
const showSelectReminderDialogComponent = ref(false);
const showApprovalTransferredDialogComponent = ref(false);
const showApprovalCountersignDialogComponent = ref(false);
const showApprovalRebackDialogComponent = ref(false);
const showApprovalCommentDialogComponent = ref(false);
const showApprovalMakeCopyDialogComponent = ref(false);

const revokeVisible = ref(false);

const approvalOpinionsDialogComponentFn = async (res) => {
  showApprovalOpinionsDialogComponent.value = false;
  if (res) {
    if (approvalData.value.skip_approve_id) {
      approvalData.value.free_form = [];
      console.log(approvalData.value.skip_approve_id, "approvalData.value.skip_approve_id");
      props_approval_id.value = approvalData.value.skip_approve_id;
      approvalStore.skip_approve_id = approvalData.value.skip_approve_id;
      approvalStore.setPropsApprovalId(props_approval_id.value);
      await init(approvalData.value.skip_approve_id);
      console.log(approvalData.value.skip_approve_id, "approvalData.value.skip_approve_id");
      console.log(props_approval_id.value, approvalData.value.approve_id, "      props_approval_id.value");
      props_approval_id.value = approvalData.value.approve_id;
      MessagePlugin.info({
        content: "进入下一条",
        duration: 3000,
        icon: false,
        // 层级控制：非当前场景自由控制开关的关键代码，仅用于测试 API 是否运行正常
        zIndex: *********,
        style: "background: #13161b;box-shadow: none;color: #ffffff;",
        // 挂载元素控制：非当前场景自由控制开关的关键代码，仅用于测试 API 是否运行正常
        attach: "#approval_details_drawer_component",
        offset: [0, "50vh"],
      });

      approveCenterCount(approvalData.value.team_id).then((res) => {
        if (res.data.code === 0) {
          const approvalStore = useApprovalStore();
          approvalStore.pendingApprovalCountData = res.data.data.pending_approval;
        }
      });

      approveForeignCount().then((res) => {
        if (res.data.code === 0) {
          const approvalStore = useApprovalStore();
          approvalStore.approveCenterCountData = res.data.data.pending_approval;
        }
      });
      ipcRenderer.invoke("update-approve");
    } else {
      if(props.isZhixing){
        close();
      } else {
        approvalData.value.free_form = [];
        useChatExtendStore().approvAutoAction = '';
        await init();
      }
    }
    emits("status", approvalStore.old_approve_id);
  }
};
const selectReminderDialogComponentFn = async (res) => {
  showSelectReminderDialogComponent.value = false;
  if (res) {
    approvalData.value.free_form = [];
    await init();
    await initContolBtn();
  }
};

const approvalTransferredDialogComponentFn = async (res) => {
  showApprovalTransferredDialogComponent.value = false;
  if (res) {
    approvalData.value.free_form = [];
    await init(approvalData.value.skip_approve_id);
    MessagePlugin.info({
      content: "进入下一条",
      duration: 3000,
      icon: false,
      // 层级控制：非当前场景自由控制开关的关键代码，仅用于测试 API 是否运行正常
      zIndex: *********,
      style: "background: #13161b;box-shadow: none;color: #ffffff;",
      // 挂载元素控制：非当前场景自由控制开关的关键代码，仅用于测试 API 是否运行正常
      attach: "#approval_details_drawer_component",
      offset: [0, "50vh"],
    });
  }
};

const approvalCountersignDialogComponentFn = async (res) => {
  showApprovalCountersignDialogComponent.value = false;
  if (res) {
    approvalData.value.free_form = [];
    await init(approvalData.value.skip_approve_id);
    MessagePlugin.info({
      content: "进入下一条",
      duration: 3000,
      icon: false,
      // 层级控制：非当前场景自由控制开关的关键代码，仅用于测试 API 是否运行正常
      zIndex: *********,
      style: "background: #13161b;box-shadow: none;color: #ffffff;",
      // 挂载元素控制：非当前场景自由控制开关的关键代码，仅用于测试 API 是否运行正常
      attach: "#approval_details_drawer_component",
      offset: [0, "50vh"],
    });
  }
};

const approvalRebackDialogComponentFn = async (res) => {
  showApprovalRebackDialogComponent.value = false;
  if (res) {
    approvalData.value.free_form = [];
    await init(approvalData.value.skip_approve_id);
    MessagePlugin.info({
      content: "进入下一条",
      duration: 3000,
      icon: false,
      // 层级控制：非当前场景自由控制开关的关键代码，仅用于测试 API 是否运行正常
      zIndex: *********,
      style: "background: #13161b;box-shadow: none;color: #ffffff;",
      // 挂载元素控制：非当前场景自由控制开关的关键代码，仅用于测试 API 是否运行正常
      attach: "#approval_details_drawer_component",
      offset: [0, "50vh"],
    });
  }
};

const approvalCommentDialogComponentFn = async (res) => {
  console.log(123123);
  showApprovalCommentDialogComponent.value = res;
  approvalData.value.free_form = [];
  init(approvalData.value.approve_id);
};

const approvalMakeCopyDialogComponentFn = async (res) => {
  showApprovalMakeCopyDialogComponent.value = res;
  approvalData.value.free_form = [];
  await init(approvalData.value.approve_id);
};

const popupVisible = ref(false);

const showSkip = ref(false);
const moreFn = lodash.debounce((item) => {
  popupVisible.value = false;
  if (item.id === 0) {
    showSkip.value = true;
    const data = {
      approve_id: props.approvalDetailItem.approval_id,
    };
    approveSkip(data).then((res) => {
      if (res.data.code === 0) {
        MessagePlugin.info({
          content: "进入下一条",
          duration: 3000,
          icon: false,
          // 层级控制：非当前场景自由控制开关的关键代码，仅用于测试 API 是否运行正常
          zIndex: *********,
          style: "background: #13161b;box-shadow: none;color: #ffffff;",
          // 挂载元素控制：非当前场景自由控制开关的关键代码，仅用于测试 API 是否运行正常
          attach: "#approval_details_drawer_component",
          offset: [0, "50vh"],
        });
      } else {
        MessagePlugin.error(res.data.message);
      }
    });
    approvalData.value.free_form = [];
    console.log(approvalData.value.skip_approve_id, "approvalData.value.skip_approve_id");
    init(approvalData.value.skip_approve_id);
    return;
  }
  if (item.id === 1) {
    showSelectReminderDialogComponent.value = true;
  }
  if (item.id === 2) {
    showApprovalTransferredDialogComponent.value = true;
  }

  if (item.id === 3) {
    showApprovalCountersignDialogComponent.value = true;
  }

  if (item.id === 4) {
    showApprovalRebackDialogComponent.value = true;
  }
  if (item.id === 5) {
    revokeVisible.value = true;
  }

  if (item.id === 6) {
    pushInitApproval();
  }

  if (item.id === 7) {
    stopCancel();
  }
}, 500);

const moreBtnList = ref([]);

const revokeApproveWithdrawDialog = async () => {
  await revokeApproveWithdraw();
  await initContolBtn();
};

const revokeApproveWithdraw = async () => {
  const teamId = props.isZhixing ? approvalData.value.team_id : null;

  await approveWithdraw(
    {
      approve_id: props.approvalDetailItem.approval_id,
    },
    teamId,
  ).then(async (res) => {
    if (res.data.code === 0) {
      MessagePlugin.success("撤销成功");
      revokeVisible.value = false;
      approvalData.value.free_form = [];
      await init();
    } else {
      MessagePlugin.error(res.data.message);
      revokeVisible.value = false;
    }
    console.log("revokeApproveWithdraw", res);
  });
};

provide("free_form", approvalData); // 变量

provide("approval_id", props_approval_id.value); // 变量

const printApproval = () => {
  window.open(`${getBaseUrl("client-organize")}/approval/print/${approvalData.value.print}`);
  // approvePrint(approvalData.value.print).then((res) => {
  //   console.log('打印', res);
  // });
};

const stopCancel = () => {
  const myDialog = DialogPlugin({
    header: "提示",
    theme: "info",
    body: "确定要中止撤销申请吗?",
    className: "delmode",
    confirmBtn: "确定",
    cancelBtn: "取消",
    onConfirm: async () => {
      await revokeApproveWithdraw();
      await initContolBtn();
      myDialog.hide();
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const pushInitApproval = () => {
  console.log(approvalData.value, "approvalData.value");
  // debugger;
  // return;
  if (approvalData.value.set_is_stop) {
    const myDialog = DialogPlugin.alert({
      header: "提示",
      theme: "info",
      body: t("approve.stopApprovalTemp", { name: approvalData.value.set_name }),
      className: "t-dialog-new-class1 t-dialog-new-class2",
      style: "color: rgba(0, 0, 0, 0.6)",
      onConfirm: async () => {
        myDialog.hide();
      },
    });
    return;
  }
  judgeSubmitAgain();

  // router.push({
  //   path: '/approvalIndex/init_apply',
  //   query: {
  //     dataid: props.approvalDetailItem.approval_id,
  //     departmentId: 1,
  //     staffId: projectId.value,
  //   },
  // });
};

const revokeTwo = () => {
  console.log(approvalData.value, "approvalData.value.is_handle_approval");
  if (approvalData.value.status == 2) {
    const myDialog = DialogPlugin({
      header: "提示",
      theme: "info",
      body: "审批已通过，撤销需要重新审批",
      className: "t-dialog-new-class1 t-dialog-new-class2",
      style: "color: rgba(0, 0, 0, 0.6)",
      onConfirm: async () => {
        await revokeApproveWithdraw();
        await initContolBtn();
        console.log(moreBtnList.value, "测试长度");
        myDialog.hide();
      },
      onCancel: () => {
        myDialog.hide();
      },
    });
  } else {
    revokeVisible.value = true;
  }
  // showApprovalOpinionsDialogComponent.value = true;
};

const approveSetId = ref(0);
const canSubmit = ref(true);

const judgeSubmitAgain = async () => {
  const OpenIdRes = await getApproveCommonDetailOpenId();
  console.log(OpenIdRes, "OpenIdRes");
  if (!OpenIdRes) {
    return;
  }
  approvalStore.setImPushInitApplyStep();
  console.log(approvalStore.imPushInitApplyStep, projectId.value, getStaffId(), "oldValue");
  const teamId = props.isZhixing ? approvalData.value.team_id : null;
  const res = await approveJudgeSubmitAgain(
    {
      approve_id: props.approvalDetailItem.approval_id,
    },
    teamId,
  );
  console.log("提示", res);
  if (res && res.data.code === 0) {
    approveSetId.value = res.data.data.approve_set_id;
    canSubmit.value = res.data.data.can_submit;
    if (res.data.data.can_submit) {
      console.log("再次发起", positions.value, res.data.data, props.approvalDetailItem.approval_id, route.path);
      if (positions.value.length > 1) {
        selectDepartmentVisible.value = true;
        return;
      }
      if (positions.value.length == 0) {
        MessagePlugin.info(t("approve.contactTheAdministrator"));
        return;
      }
      const isMessage = route.path.includes("main/message");
      // @ts-ignore
      const isWorkBench = window.__ELECTRON_WINDOW_MANAGER_NAME__ === "workBench";
      if (isMessage || !isWorkBench) {
        ipcRenderer.invoke("delect-ApprovalWinBV");
        console.log("内容会不会重制", positions.value);
        // const configData = configDataStore();
        // configData.$state.path_uuid = "approve";
        // console.log(configData.$state.path_uuid, 'configData.$state.path_uuid');
        msgJumpToApprove({
          dataid: props.approvalDetailItem.approval_id,
          id: approveSetId.value,
          departmentId: positions.value.length == 1 ? positions.value[0].departmentId : 1,
          staffId: approvalData.value.project_id,
          teamId: approvalData.value.team_id,
          projectId: approvalData.value.project_id,
          jobId: positions.value.length == 1 ? positions.value[0].jobId : 0,
        });
      } else {
        const baseUrl = route.path.includes("/workBenchIndex") ? `/workBenchIndex` : `/approvalIndex`;
        router.push({
          path: `${baseUrl}/init_apply`,
          query: {
            dataid: props.approvalDetailItem.approval_id,
            id: approveSetId.value,
            departmentId: positions.value.length == 1 ? positions.value[0].departmentId : 1,
            staffId: approvalData.value.project_id,
            projectId: approvalData.value.project_id,
            jobId: positions.value.length == 1 ? positions.value[0].jobId : 0,
          },
        });
      }

      emits("visibleFn", false);
      console.log("tiaozhuan");

      // this.dataid = approvalData.value.id;
      // this.getMemberDepartmentReq();
    } else {
      // this.showConfirm();
      canSubmit.value = false;
      const myDialog = DialogPlugin({
        header: "提示",
        theme: "info",
        body: "该条审批流程发生了变动，无法将表单内容进行复制，是否继续再次提交？",
        className: "t-dialog-new-class1 t-dialog-new-class2",
        style: "color: rgba(0, 0, 0, 0.6)",
        onConfirm: () => {
          console.log(positions.value, "该条审批流程发生了变动，无法将表单内容进行复制，是否继续再次");
          if (positions.value.length > 1) {
            selectDepartmentVisible.value = true;
            myDialog.hide();
            return;
          }
          againJm();
          myDialog.hide();
        },
        onCancel: () => {
          myDialog.hide();
        },
      });
    }
  } else {
    // this.$message.error(res.msg)
    MessagePlugin.error(res.data.message);
  }
};

const positions = ref([]);
const selectDepartmentVisible = ref(false);
const getApproveCommonDetailOpenId = async () => {
  console.log(
    approvalData.value,
    projectId.value,
    localStorage.getItem("approvalteamid"),
    "approvalData.value.team_id",
  );
  const data = {
    id: approvalData.value.project_id == 0 ? approvalData.value.team_id : approvalData.value.project_id, // 组织成员值为teamId，工程值为projectId
    type: approvalData.value.project_id == 0 ? 1 : 2, // 组织：1，工程：2
    teamId: approvalData.value.project_id == 0 ? localStorage.getItem("approvalteamid") : approvalData.value.project_id, // 组织成员值为teamId，工程值为projectId
  };
  const res = await approveCommonDetailOpenid(data);
  console.log(res, "报错");
  if (!res) {
    MessagePlugin.info(t("approve.noPermissionForApproval"));
    return res;
  }
  if (res.data.code === 0) {
    if (res.data.data.positions.length >= 1) {
      positions.value = res.data.data.positions;
      console.log(res.data.data.positions, "es.data.data.positions");
    } else {
      console.log("只有一个", res.data.data);
    }
  }
  return res;
};

const againJm = (departmentId?) => {
  const baseUrl = route.path.includes("/workBenchIndex") ? `/workBenchIndex` : `/approvalIndex`;
  router.push({
    path: `${baseUrl}/init_apply`,
    query: {
      dataid: props.approvalDetailItem.approval_id,
      id: approveSetId.value,
      departmentId: departmentId || positions.value[0].departmentId,
      staffId: projectId.value,
      projectId: projectId.value,
      jobId: positions.value.length === 1 ? positions.value[0].jobId : 0,
      canSubmit: "1",
    },
  });
};

const selectDepartmentFn = (item) => {
  selectDepartmentVisible.value = false;
  if (!item) return;
  if (!canSubmit.value) {
    againJm(item.departmentId);
    return;
  }

  if (route.path.includes("main/message")) {
    msgJumpToApprove({
      dataid: props.approvalDetailItem.approval_id,
      id: approveSetId.value,
      departmentId: item.departmentId || 1,
      staffId: projectId.value,
      projectId: projectId.value,
    });
  } else {
    const baseUrl = route.path.includes("/workBenchIndex") ? `/workBenchIndex` : `/approvalIndex`;
    router.push({
      path: `${baseUrl}/init_apply`,
      query: {
        dataid: props.approvalDetailItem.approval_id,
        id: approveSetId.value,
        departmentId: item.departmentId || 1,
        staffId: projectId.value,
        projectId: projectId.value,
      },
    });
  }
};

const resetInit = async () => {
  console.log(123123);
  approvalData.value.free_form = [];
  await init();
};

const manifestData = ref({
  openid: "",
  at_id: "",
  title: "",
  mission_at: null,
  knock_at: null,
  content: {
    attachments: [],
    images: [],
    events: [],
    customKnockAt: null, // 自定义时间
    remark: "",
  },
  done: false,
  draft: false,
  create_at: 0,
  annex_size: 0,
});
const shareVisible = ref(false);
const shareFn = () => {
  console.error("dgz===approvalData.value", approvalData.value, approvalteamid.value, approvalItem.value, getStaffId(), );
  shareVisible.value = true;
};

const shareList = async (v) => {
  const data = {
    approveId: approvalData.value.approve_id,
    staffId: approvalData.value.staff_id,
    teamLogo: approvalData.value.team_logo,
    teamName: approvalData.value.team_name,
    projectName: approvalData.value.project_name,
    formValue: approvalData.value.form_value,
    approving_log: approvalData.value.approving_log,
    teamId: approvalteamid.value,
    projectId: projectId.value,
    mustOpinion: approvalData.value.must_opinion,
    approveTitle: `${approvalData.value.member_name}${t("approve.submit")}${approvalData.value.set_name}`,
  };
  console.log(approvalData.value, data, v, "sssss");

  const res = await sendApplicationMsg(MsgShareType.approve, data, v);
  console.log(res, "分享成功");
  MessagePlugin.success("分享成功");
};

const agree = () => {
  type.value = 1;
  showApprovalOpinionsDialogComponent.value = true;
  console.log('====>123', 123);
};

const emits = defineEmits(["visibleFn", "status", "complete"]);
const close = () => {
  const status = approvalData.value.status;
  approvalData.value.status = 2;
  approvalData.value.status = status;

  loading.value = true;

  emits("visibleFn", false);
};

const conId = () => {
  console.log(props_approval_id.value, "   抄送  props_approval_id.value");
};
</script>

<style lang="less" scoped>
.flex-align {
  display: flex;
  align-items: center;
}

.approval_details_drawer_component {
  :deep(.t-drawer--open > .t-drawer__mask) {
    background: transparent !important;
  }

  .header-title {
    //margin-top: 48px;
    padding: 16px 0;
    font-size: 16px;
    font-weight: 700;
    text-align: left;
    color: #13161b;
    display: flex;
    align-items: center;
    width: 100%;

    span {
      display: inline-block;
      flex: 1;
    }

    .icon {
      max-width: 20px;
      min-width: 20px;
      min-height: 20px;
      max-height: 20px;
      box-sizing: content-box;
      padding: 2px;
    }
    .icon:hover {
      max-width: 20px;
      max-height: 20px;
      padding: 2px;
      font-size: 20px;
      background: #f1f1f1;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .drawer-footer {
    display: flex;
    //width: 130px;
    align-items: center;
    //justify-content: space-between;
    & > div {
      color: #516082;
      font-size: 12px;
      margin-right: 24px;
    }
  }

  .drawer-footer-button {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: flex-end;
  }

  .items:hover {
    background: #daecff !important;
    color: #2069e3 !important;
  }

  .flexbox-j-s {
    display: flex;
    justify-content: space-between;
  }

  .flexbox {
    display: flex;
  }

  .flexbox-1 {
    flex: 1;
  }

  .text-center {
    text-align: center;
  }

  .flex-align-jsb {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
  }

  .approve-back {
    font-size: 14px;

    font-weight: 400;
    text-align: left;
    color: #fff;
    line-height: 22px;

    div {
      margin-left: 8px;
    }
  }

  .approve-no-item {
    .approve-no {
      font-size: 12px;

      font-weight: 400;
      text-align: left;
      color: #ffffff;
      line-height: 22px;
    }

    .approve-icon {
      display: flex;
      font-size: 12px;

      .icon:first-child {
      }
    }
  }

  .approve-title-item {
    margin-top: 16px;

    .approve-title {
      //flex: 1;
      max-width: 300px;
      font-size: 18px;
      font-weight: 700;
      text-align: left;
      color: #fff;
      //line-height: 32px;
      margin-right: 8px;
    }
  }

  .approve-company-item {
    margin-top: 8px;

    .approve-company-img {
      display: flex;
      align-items: center;
      //background: #fff;
      img {
        width: 20px;
        height: 20px;
      }
    }

    .approve-company-name {
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      color: #fff;
      line-height: 22px;
      margin-left: 8px;
    }
  }

  .approve-department-item {
    display: flex;
    align-items: center;
    font-size: 12px;

    font-weight: 400;
    text-align: left;
    color: #fff;
    line-height: 22px;
    margin-top: 12px;

    .divider-1 {
      width: 1px;
      height: 10px;
      background: rgba(237, 239, 255, 0.5);
      margin: 0 12px;
    }
  }

  .content {
    .content-title {
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      color: #717376;
      line-height: 22px;
      margin-bottom: 4px;
    }

    .content-text {
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      color: #13161b;
      line-height: 22px;
      margin-bottom: 12px;
    }
  }

  .flow-path {
    .title {
      font-size: 16px;
      font-weight: 700;
      text-align: left;
      color: #13161b;
      line-height: 24px;
      margin-bottom: 12px;
    }

    //.flow-path-item:not(:last-child) .avatar::after {
    //	display: block;
    //	//position: absolute;
    //	content: '';
    //	width: 2px;
    //	height: 28px;
    //	background: #e3e6eb;
    //	margin: 2px auto;
    //}

    .flow-path-item {
      .avatar {
        .avatar-img {
          position: relative;
        }

        img,
        div {
          width: 40px;
          height: 40px;
          border-radius: 6px;
          display: block;
        }

        div {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 400;
          text-align: center;
          color: #ffffff;
          line-height: 20px;
          background: #488bf0;
        }

        .icon {
          position: absolute;
          width: 12px;
          height: 12px;
          bottom: -5px;
          right: -5px;
        }
      }

      .flow-path-item-name {
        flex: 1;
        margin-left: 10px;

        .cc-img {
          width: 24px;
          height: 24px;
          border-radius: 4px;
        }

        .name > div:first-child {
          font-size: 14px;
          font-weight: 400;
          text-align: left;
          color: #13161b;
          line-height: 22px;
        }

        .name > div:last-child {
          font-size: 12px;
          font-weight: 400;
          text-align: left;
          color: #717376;
          line-height: 20px;
        }

        .cc-name {
          font-size: 10px !important;

          font-weight: 400;
          text-align: center;
          color: #717376;
          line-height: 18px;
        }

        span {
          text-align: center;
          margin-right: 15px;
          margin-top: 12px;
        }

        .approve-idea {
          height: 32px;
          line-height: 32px;
          background: #f1f2f5;
          border-radius: 4px;
          font-size: 14px;
          font-weight: 400;
          text-align: left;
          color: #13161b;
          padding: 0 12px;
          margin-top: 12px;
        }
      }
    }
  }

  .color_tagBg_blue {
    background: #f0f8ff;
    color: #2069e3;
    border-radius: 4px;
    padding: 0 8px;
    font-size: 12px;
  }

  .color_tagBg_green {
    background: #eeffe8;
    color: #1c8710;
    border-radius: 4px;
    padding: 0 8px;
    font-size: 12px;
  }

  .color_tagBg_red {
    background: #ffeee8;
    color: #da2d19;
    border-radius: 4px;
    padding: 0 8px;
    font-size: 12px;
  }

  .status-1 {
    font-size: 12px;
    font-weight: 400;
    text-align: left;
    color: #e66800;
    line-height: 24px;
    border-radius: 100px;
    background: #ffe5d1;
    color: var(--lingke-yellow, #fc7c14);
    padding: 1px 8px;
    height: 24px;
  }

  .status-2 {
    font-size: 12px;
    font-weight: 400;
    text-align: left;
    color: #499d60;
    line-height: 24px;
    height: 24px;
    padding: 1px 8px;
    border-radius: 100px;
    background: #e0f2e5;
  }

  .status-3 {
    font-size: 12px;
    font-weight: 400;
    text-align: left;
    color: #d54941;
    line-height: 24px;
    height: 24px;
    padding: 1px 8px;
    border-radius: 100px;
    background: #fbdde3;
  }

  .status-4 {
    font-size: 12px;
    font-weight: 400;
    text-align: left;
    color: #828da5;
    line-height: 24px;
    height: 24px;
    padding: 1px 8px;
    border-radius: 100px;
    background: var(--lingke-gray-1, #eceff5);
  }

  .secondary_disabled {
    background: #f1f2f5;
    color: #13161b;
    border-radius: 4px;
    padding: 0 8px;
    font-size: 12px;
  }

  .divider-16 {
    width: 100%;
    height: 16px;
    background: #f5f8fe;
  }

  :deep(.t-drawer__body) {
    padding: 0;
    overflow-x: hidden;
  }

  .p16 {
    padding: 16px;
  }

  .p12-24 {
    padding: 12px 24px;
  }

  :deep(.t-timeline-item__wrapper .t-timeline-item__dot) {
    position: absolute;
    width: 40px;
    height: 40px;
    top: 2px;
    //top: 8px;
  }

  :deep(.t-timeline-item__wrapper .t-timeline-item__tail) {
    position: absolute;
    border-left: 1px solid transparent;
    left: 50%;
    height: calc(100% - (18px * 2 + 8px));
    bottom: 0;
    transform: translateX(-50%);
    padding-bottom: var(--td-comp-size-s);
    border-color: var(--td-component-border);
    box-sizing: border-box;
  }

  .t-timeline {
    margin-left: 16px;
  }
}

.more-btn-list {
  width: 140px;
  //height: 268px;
  background: #ffffff;
  border-radius: 4px;
  margin: 8px;

  .more-btn-item {
    display: flex;
    align-items: center;
    width: 123px;
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
    padding: 8px;
    padding-left: 16px;
    margin-bottom: 2px;

    div {
      font-size: 14px;

      font-weight: 400;
      text-align: left;
      color: #13161b;
      line-height: 22px;
      //margin-left: 8px;
    }
  }

  .more-btn-item:hover {
    background: #f3f6fa;
    border-radius: 4px;
    cursor: pointer;
    color: #1a2139;

    .icon {
      color: #4d5eff;
    }
  }

  :deep(.t-drawer__mask) {
    background: transparent !important;
  }
}

:deep(.t-drawer__mask) {
  background: transparent !important;
}

.refuse-button {
  display: flex;
  height: 32px;
  min-height: 32px;
  max-height: 32px;
  padding: 0px 16px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  color: var(--color-button-secondary-error-kyy-color-button-secondray-error-text-default, #d54941) !important;
  border-radius: var(--radius-kyy-radius-button-s, 4px) !important;
  border: 1px solid var(--color-button-secondary-error-kyy-color-button-secondary-error-border-dedault, #d54941) !important;
  background: var(--color-button-secondary-error-kyy-color-button-secondray-error-bg-default, #fdf5f6) !important;
}

.agree-button {
  display: flex;
  height: 32px;
  min-height: 32px;
  max-height: 32px;
  padding: 0px 16px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  color: var(--color-button-secondary-brand-kyy-color-button-secondray-border-text-default, #4d5eff);
  border-radius: var(--radius-kyy-radius-button-s, 4px) !important;
  border: 1px solid var(--color-button-secondary-brand-kyy-color-button-secondary-brand-border-dedault, #4d5eff) !important;
  //background: var(--color-button-secondary-brand-kyy-color-button-secondray-border-bg-default, #EAECFF) !important;
}
</style>

<style lang="less">
.approval_details_drawer {
  .t-drawer__header {
    padding-left: 24px !important;
    padding-right: 24px !important;
  }

  .t-drawer__footer {
    padding-left: 24px !important;
    padding-right: 24px !important;
  }
}
.loading-box{
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
