<template>
  <div class="module-square">
    <PageHeader
      tab-icon="square-fill"
      :account-list="accountList"
      :account-value="store.squareInfo?.square?.squareId"
      :account-count="accountCount"
      alone-win-name="square"
      @tab-change="tabChange"
      @tab-remove="tabRemove"
      @account-change="accountChange"
      @refresh="refresh(true)"
    />

    <div class="page-container">
      <div v-if="showLeft && showLeftMenu" :key="sidebarKey" class="sidebar">
        <div class="page-title">
          {{ $t('square.square.name') }}

          <t-popup
            :content="$t('square.post.publishPost')"
            hide-empty-popup
            destroy-on-close
            placement="top"
            overlay-inner-class-name="avatar-cert-pop"
          >
            <t-button class="w-24 h-24 p-0 text-14" @click="openPostDialog">
              <iconpark-icon name="iconadd" />
            </t-button>
          </t-popup>
        </div>

        <PostPublishDialog
          v-model="postDialogVisible"
          :show-article="!store.isPersonal"
          @submit="submitPost"
          @update-draft="onUpdateDraft"
        />

        <div class="search-box">
          <t-input
            ref="searchRef"
            v-model="keyword"
            :maxlength="50"
            :placeholder="keywordFocus ? '': $t('square.search')"
            clearable
            @focus="keywordFocus = true"
            @blur="keywordFocus = false"
            @change="onChange"
            @enter="onSearch"
          >
            <template #prefix-icon>
              <iconpark-icon name="iconsearch" />
            </template>
          </t-input>
        </div>

        <ExpireAlert v-if="!store.isPersonal" @success="sidebarKey++" />

        <div class="menu">
          <div
            v-for="(item, index) in getMenuList"
            :key="item.name"
            :class="['menu-item', { active: store.activeMenuIdx === index }, { 'square-promote-menu-item': item.name === 'square-promote' }]"
            @click="onMenuItemClick(item, index)"
          >
            <iconpark-icon class="menu-icon" :name="item.icon" :style="menuIconStyle(item.icon)" />
            <div class="menu-title">
              {{ item.title }}

              <!--好友圈通知数量-->
              <template v-if="item.name === 'FriendCircle'">
                <t-badge :count="store.unreadPostCount" dot size="small">&nbsp;</t-badge>
              </template>
            </div>

            <!--通知数量-->
            <span v-if="item.name === 'SquareNotification' && store.newsStats.total" class="color-[#E5398C] font-600">
              {{ store.newsStats.total }}
            </span>

            <!--草稿-->
            <span v-if="item.name === 'drafts' && store.squareStats?.drafts" style="color: #516082;font-weight: 600">{{ store.squareStats.drafts }}</span>

            <!--组织账号信息-->
            <span v-if="item.name === 'account-info' && expiredNameTip" class="flex">
              <t-tooltip :content="expiredNameTip"><iconpark-icon name="iconattention" style="color: #D54941" /></t-tooltip>
            </span>
          </div>
        </div>

        <div v-if="!store.isPersonal" class="view-count">
          <div>
            <span class="view-count--title">{{ $t('square.square.viewPageNum') }}：</span>
            <span class="view-count--number">{{ totalVisitNumber }}</span>
          </div>
          <span class="curp" @click="toPromotion">推广</span>
        </div>

        <UpgradeEntry v-if="!store.isPersonal" ref="upgradeEntryRef" />

        <AnnualFeeDialog
          v-if="annulFeeVisible"
          v-model="annulFeeVisible"
          :square-id="store.squareId"
          :team-id="store.squareInfo?.organizationProfile?.teamId"
          :upgrade="annulFeeUpgrade"
          @success="refresh"
        />

        <div v-if="!store.isPersonal && store.squareSelected.isAdmin" class="footer" @click="footerClick">
          <div class="setting-wrap" :class="{ active: route.path === '/square-admin/square-manager' }">
            <iconpark-icon name="iconcomputer" class="icon" />
            <span class="title">{{ $t('square.admin.title') }}</span>
          </div>
        </div>
      </div>

      <router-view v-slot="{ Component }" class="square-content">
        <keep-alive :include="componentTick.include">
          <component :is="Component" v-if="componentTick.showComponent" :key="$route.fullPath" />
        </keep-alive>
      </router-view>

      <slot name="right">
        <div v-if="showRightBar" class="right">
          <SquareHall :key="pageKey" />
        </div>
      </slot>
    </div>

    <!-- 百度地图初始化组件（不可见） -->
    <BMap
      :api-url="BAIDU_API_URL"
      :ak="BAIDU_AK"
      :height="0"
      @initd="onMapInit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, provide, nextTick, computed, onUnmounted, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useNetwork } from '@vueuse/core';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { useCommonStore } from '@renderer/store/modules/common';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import LynkerSDK from '@renderer/_jssdk';
import { destroyNode, getShowNode, refreshNode } from '@renderer/_jssdk/components/iframe/iframePool';
import qs from 'qs';
import { useLocationFlow, BAIDU_API_URL, BAIDU_AK } from '@rk/unitPark';
import { useSquareStore } from '@/views/square/store/square';
import { RouteItem } from './types';
import PostPublishDialog from '@/views/square/components/post/PostPublishDialog.vue';
import './common.less';
import useRouterHelper from '@/views/square/hooks/routerHelper';
import { POST_REFRESH_KEY, ROUTE_REFRESH_INJECT_KEY, REFRESH_MENU_INJECT_KEY } from '@/views/square/constant';
import ExpireAlert from '@/views/square/components/annual-fee/ExpireAlert.vue';
import SquareHall from '@/views/square/components/SquareHall.vue';
import UpgradeEntry from '@/views/square/components/annual-fee/UpgradeEntry.vue';
import { useExpiredNameTip } from '@/views/square/utils/business';
import PageHeader from '@/components/page-header/index.vue';
import { useTabsStore } from '@/components/page-header/store';
import { TabItem } from '@/components/page-header/type';
import { useSquareAccount } from '@/views/square/hooks/squareAccount';
import AnnualFeeDialog from '@/views/square/components/annual-fee/AnnualFeeDialog.vue';
import { useProvideCheckTrial } from './hooks/checkTrial';
import { useEventHandling } from './hooks/eventHandling';
import { useStateStore } from './store/state';

const props = defineProps({
  showRight: {
    type: Boolean,
    default: true,
  },
  showLeft: {
    type: Boolean,
    default: true,
  },
});

const router = useRouter();
const route = useRoute();
const store = useSquareStore();
const squareStore = useSquareStore();
const commonStore = useCommonStore();
const stateStore = useStateStore();

const individualSetting = computed({
  get() {
    return store.squareInfo.individualSetting;
  },
  set(val) {
    store.squareInfo.individualSetting.showNearbyPosts = val.showNearbyPosts;
  },
});
provide(REFRESH_MENU_INJECT_KEY, (val) => {
  individualSetting.value = val;
});

const { annulFeeVisible, annulFeeUpgrade, checkExpired } = useProvideCheckTrial();

const { t } = useI18n();
const { expiredNameTip } = useExpiredNameTip();

const tabStore = useTabsStore();
if ((!tabStore.findTab('/square/friend-circle') && !tabStore.findTab('/square/publish-records')) || tabStore.tabs.length === 0) {
  tabStore.setHome({
    fullPath: store.homePage,
    label: '广场',
  });
}

const destroy = router.beforeEach((to, from) => {
  const toPath = to.path;
  const fromPath = from.path;
  if (toPath.includes('/square/info') && fromPath) {
    squareStore.initVisiteNumber(to.query.id as string);
  }
});

const totalVisitNumber = computed(() => store?.squareInfo?.totalVisits);
const showLeftMenu = computed(() => !['publish-article', 'square_webview'].includes(route.name as string));
const showRightBar = computed(() => {
  const noRightPages = ['/square/publish-records', '/square/account-info', '/square/publish-article', '/square/square_webview', '/square/square-promote'];
  return props.showRight && !noRightPages.some((v) => route.fullPath.startsWith(v));
});

const { menuList, routeList } = useRouterHelper(['squareIndex']);
watch(() => menuList.value, (val) => {
  store.menuList = val;
}, { deep: true, immediate: true });

// 菜单过滤
const getMenuList = computed(() => menuList.value.filter((item) => {
  // 处理附近入口
  if (item.name === 'nearbyList') {
    return store.isPersonal ? individualSetting.value?.showNearbyPosts : true;
  }
  return !!item.title;
}));

// 搜索关键字
const keyword = ref('');
const keywordFocus = ref(false);
const searchRef = ref();

const onChange = (val) => {
  squareStore.searchKeyword = val;
};

// 切换组织清空搜索关键字
watch(() => store.squareId, () => {
  keyword.value = '';
});

const onSearch = async () => {
  searchRef.value.blur();
  if (!keyword.value) {
    // searchRef.value.blur();
    return;
  }

  const fullPath = `/square/search?keyword=${keyword.value}`;
  await router.push(fullPath);

  tabStore.addTab({
    label: `${t('square.search')}: ${keyword.value}`,
    fullPath,
  }, '/square/search?');
};

// 发布动态
const postDialogVisible = ref(false);
const openPostDialog = () => {
  if (checkExpired()) return;
  postDialogVisible.value = true;
};

const postSubmitKey = ref(1);
provide(POST_REFRESH_KEY, postSubmitKey);
const submitPost = () => {
  postDialogVisible.value = false;
  postSubmitKey.value++;
};

const { isOnline } = useNetwork();

/**
 * 当保存了草稿内容的回调事件
 */
const onUpdateDraft = async () => {
  const path = route.path;
  if (path === '/square/drafts') {
    await nextTick();
    pageKey.value++;
  }
};

// 独立菜单 TODO 改为在 router meta 中声明方式控制
const standaloneMenus = ['/assemble/index', '/square/phone-album', '/square/phone-album/time-line', '/square/friend-ablum/'];

// 左侧菜单
const onMenuItemClick = async (item: RouteItem, index: number) => {
  const cachePages = ['/square/friend-circle', '/square/homepage'];
  if (!isOnline.value && !cachePages.includes(item.fullPath)) {
    await MessagePlugin.error('网络连接失败，请检查网络后重试');
    return;
  }

  commonStore.setLoading(true);
  store.activeMenuIdx = index;

  const pathIconMap = {
    '/square/phone-album/time-line': 'photoalbum',
  };
  const icon = pathIconMap[item.fullPath];

  // 独立菜单
  if (standaloneMenus.includes(item.fullPath)) {
    tabStore.addTab({
      label: item.title,
      fullPath: item.fullPath,
      ...(icon && { icon }),
    });
  } else {
    tabStore.activeIndex = 0;

    // 更新首页地址
    tabStore.setHomePath(item.fullPath);
  }

  setTimeout(async () => {
    await router.push(item.fullPath);
    await nextTick();
    await doRefresh();
  }, 200);
};

// 菜单图标样式
const menuIconStyle = (icon: string) => {
  const iconColors = {
    'iconnote-a961a3n6': '#828da5',
    iconpositioning: '#CA48EB',
    iconrelease: '#62BF7C',
    iconnotice: '#2596FF',
    iconsetting: '#4093E0',
  };
  return { color: iconColors[icon] || '' };
};

// 根据路径设置当前菜单选中索引
const setActiveIndex = (current) => {
  if (standaloneMenus.includes(current.path)) {
    store.activeMenuIdx = 0;
    return;
  }

  let idx = getMenuList.value.findIndex((v) => v.fullPath === current.path);
  if (idx > -1) store.activeMenuIdx = idx;

  if (current.path.includes('/square/info')) {
    store.activeMenuIdx = 0;
  }

  if (current.path.includes('/square/local-post')) {
    store.activeMenuIdx = 1;
  }
};

// 地理定位
const locationFlow = useLocationFlow({
  autoStart: false,
  onSuccess: (result) => {
    store.location = result.data;
  },
  onError: (error) => {
    console.error('定位失败:', error);
  },
});

const onMapInit = () => {
  locationFlow.start();
};

// 隐藏的页面在新标签中打开
watch(() => route.fullPath, async () => {
  tabStore.resetActiveIndex(route.fullPath);
  setActiveIndex(route);
}, { immediate: true });

const footerClick = () => {
  store.activeMenuIdx = -1;
  if (store.isPersonal) {
    router.push('/square/setting');
  } else {
    router.push('/square-admin/square-manager');
  }
};

const componentTick = reactive({ include: [], showComponent: true });

// 获取需要缓存的组件名称列表
const getIncludeComponents = () => {
  const includeComponents: string[] = [];

  // 从路由配置中获取设置了 keepAlive: true 的组件
  const routes = router.options.routes;
  const findKeepAliveComponents = (routeList: readonly any[]) => {
    routeList.forEach((route) => {
      if (route.children) {
        findKeepAliveComponents(route.children);
      } else if (route.meta?.keepAlive && route.name) {
        includeComponents.push(route.name);
      }
    });
  };

  findKeepAliveComponents(routes);
  return includeComponents;
};

// 刷新当前页面
const refreshView = () => {
  const match = route.matched.find((v) => v.path === route.path);
  if (match) {
    const { name } = match.components.default;
    const componentName = name;
    if (componentName) {
      // 临时移除当前组件，然后重新添加
      const currentIndex = componentTick.include.indexOf(componentName);
      if (currentIndex > -1) {
        componentTick.include.splice(currentIndex, 1);
      }
      componentTick.showComponent = false;
    }
  }

  nextTick(() => {
    componentTick.include = getIncludeComponents();
    componentTick.showComponent = true;
  });
};

const pageKey = ref(1);
const sidebarKey = ref(1);
const upgradeEntryRef = ref();

const doRefresh = () => {
  refreshView();
  pageKey.value++;
  store.getSquareInfo();

  upgradeEntryRef.value?.refresh?.();

  if (['/square/friend-circle', '/square/notifications'].includes(route.fullPath)) {
    store.getSquareStats();
    store.fetchNewsStats();
  }
};

const refresh = async (isRefresh = false) => {
  // 刷新iframe
  let iframeNode = null;
  getShowNode().forEach((node) => {
    iframeNode = node;
    refreshNode(node);
  });
  if (iframeNode) return;

  if (isRefresh) {
    await store.getSquaresList();

    // 管理后台从组织中删除当前账号，跳转个人广场
    if (store.isPersonal && route.fullPath === '/square/publish-records') {
      router.push('/square/friend-circle');
    }
  }

  sidebarKey.value++;
  doRefresh();
  // store.getIp(true);
};

// 刷新页面
provide(ROUTE_REFRESH_INJECT_KEY, refresh);

// 解决 【【生产】【广场】广场独立弹窗时，工场中点击跳转切换组织广场，发布记录没有更新】，临时方案
// https://www.tapd.cn/********/bugtrace/bugs/view/11********001046438
watch(() => stateStore.isSwitchAccount, (val) => {
  if (val) {
    setTimeout(() => {
      refresh();
    }, 1000);
  }
});
// 解析 fullPath，返回 { path, query }
const parseFullPath = (fullPath: string) => {
  const [path, search] = fullPath?.split('?') || [];
  const query = search ? qs.parse(search) : {};
  return { path, query };
};
const tabChange = (tab: TabItem) => {
  let idx = menuList.value.findIndex((v) => v.fullPath === tab.fullPath);
  if (idx > -1) store.activeMenuIdx = idx;
  const { query } = parseFullPath(tabStore.getActiveTab().fullPath);
  router.replace({
    path: tabStore.getActiveTab().fullPath,
    query: {
      ...(query || {}),
      ...(tabStore.getActiveTab()?.query || {}),
      __tabs_id__: tabStore.getActiveTab()?.path_uuid,
      __tabs_title__: tabStore.getActiveTab()?.label,
      __tabs_icon__: tabStore.getActiveTab()?.icon,
      __tabs_active_icon__: tabStore.getActiveTab()?.activeIcon,
    },
  });
};

const tabRemove = () => {
  stateStore.setTabRemoved(true);

  const standalone = standaloneMenus.some((v) => route.fullPath.includes(v));
  getShowNode()?.forEach((i) => {
    destroyNode(i.id);
  });

  if (standalone) {
    const { query } = parseFullPath(tabStore.getActiveTab().fullPath);
    router.replace({
      path: tabStore.getActiveTab().fullPath,
      query: {
        ...(query || {}),
        ...(tabStore.getActiveTab()?.query || {}),
        __tabs_id__: tabStore.getActiveTab()?.path_uuid,
        __tabs_title__: tabStore.getActiveTab()?.label,
        __tabs_icon__: tabStore.getActiveTab()?.icon,
        __tabs_active_icon__: tabStore.getActiveTab()?.activeIcon,
      },
    });
    squareStore.isRemoveTag = true;
    return;
  }
  const activeTab = tabStore.tabs[tabStore.activeIndex];

  // 优化角色校验与跳转逻辑
  const matchRoute = routeList.find((r) => r.fullPath === activeTab.fullPath);
  const tabRole = matchRoute?.role;
  const isPersonal = store.isPersonal;

  if (
    tabRole
    && ((tabRole === 'personal' && !isPersonal)
      || (tabRole === 'office' && isPersonal))
  ) {
    // 角色不符，自动跳转到对应角色首页
    const redirectPath = isPersonal ? '/square/friend-circle' : '/square/publish-records';
    router.push(redirectPath);
    squareStore.isRemoveTag = true;
    return;
  }

  const { query } = parseFullPath(activeTab.fullPath);
  router.push({
    path: activeTab.fullPath,
    query: {
      ...(query || {}),
      ...(activeTab?.query || {}),
      __tabs_id__: activeTab?.path_uuid,
      __tabs_title__: activeTab?.label,
      __tabs_icon__: activeTab?.icon,
      __tabs_active_icon__: activeTab?.activeIcon,
    },
  });

  setTimeout(() => {
    tabStore.resetActiveIndex(route.fullPath);
  }, 400);
  squareStore.isRemoveTag = true;
};
const toPromotion = () => {
  document.querySelector('.menu-item.square-promote-menu-item')?.click();
};
const { accountList, accountCount, accountChange } = useSquareAccount(refresh);

// electron 事件监听器处理
const removeEventListeners = useEventHandling(setActiveIndex, accountChange);

onUnmounted(() => {
  // 在组件卸载时移除监听器
  removeEventListeners();
  destroy();
});

onMountedOrActivated(() => {
  // store.getIp();
  store.fetchNewsStats(!store.isPersonal);
  store.getSquareStats();

  // 初始化需要缓存的组件列表
  if (componentTick.include.length === 0) {
    componentTick.include = getIncludeComponents();
  }
});

const handleBeforeClose = (title: string, content: string): Promise<boolean> => new Promise((resolve) => { const confirmDia = DialogPlugin.confirm({ theme: 'info', header: title, body: content, confirmBtn: '确认关闭', cancelBtn: { content: '取消', theme: 'default', variant: 'outline' }, onConfirm: async () => { confirmDia.hide(); resolve(true); }, onClose: () => { confirmDia.destroy(); resolve(false); } }); });

onMounted(() => {
  LynkerSDK.ipc.handleRenderer('square-is-inited', async () => {
    console.log('square-is-inited');
    return true;
  });
  LynkerSDK.ipc.handleRenderer('square-reload', async () => {
    console.log('square-reload');
    return true;
  });
  LynkerSDK.ipc.handleRenderer('square-get-active-team-id', async () => {
    console.log('square-get-active-team-id');
    return '123';
  });
  LynkerSDK.ipc.handleRenderer('square-set-active-team-id', async (query) => {
    console.log('quare-set-active-team-id', query);
  });
  LynkerSDK.ipc.handleRenderer('get-square-tab-list', async () => {
    console.log('get-square-tab-list');
    const tabs = tabStore.tabs.map((i) => JSON.parse(JSON.stringify(i)));
    return tabs;
  });
  LynkerSDK.ipc.handleRenderer('open-square-tab-item', async (query) => {
    console.log('open-square-tab-item', query);
    const tab = tabStore.tabList.findIndex((i) => i.path_uuid === query.path_uuid);
    if (query.path_uuid && tab > -1) {
      tabStore.tabList[tab] = {
        ...tabStore.tabList[tab],
        ...query,
        path_uuid: query.path_uuid,
        title: query.title || tabStore.tabList?.[tab]?.title,
        url: query.url || tabStore.tabList?.[tab]?.url,
        icon: query.icon || tabStore.tabList?.[tab]?.icon,
        activeIcon: query.activeIcon || tabStore.tabList?.[tab]?.activeIcon,
      };
      tabStore.activeIndex = tab;
    } else {
      tabStore.addTab(query);
      setTimeout(() => {
        const tab = tabStore.tabList.findIndex((i) => i.path_uuid === query.path_uuid);
        tabStore.activeIndex = tab;
      }, 0);
    }
    setTimeout(() => {
      router.replace({
        path: tabStore.getActiveTab().fullPath,
        query: {
          ...(query.query || {}),
          __tabs_id__: query.path_uuid,
          __tabs_title__: query.title || tabStore.tabList?.[tab]?.label,
          __tabs_icon__: query.icon || tabStore.tabList?.[tab]?.icon,
          __tabs_active_icon__: query.activeIcon || tabStore.tabList?.[tab]?.activeIcon,
        },
      });
    }, 0);
    return true;
  });
  LynkerSDK.ipc.handleRenderer('close-square-tab-item', async (query) => {
    console.log('close-square-tab-item', query);
    const tab = tabStore.tabList.find((i) => i.path_uuid === query.path_uuid);
    if (tab?.beforeCloseOptions) {
      const res = await handleBeforeClose(tab.beforeCloseOptions.title, tab.beforeCloseOptions.content);
      if (!res) {
        return false;
      }
    }
    if (tab?.path_uuid) {
      getShowNode()?.forEach((i) => {
        if (i.getAttribute('data-id') === tab?.path_uuid) {
          destroyNode(i.getAttribute('data-id'));
        }
      });
    } else {
      getShowNode()?.forEach((node) => {
        destroyNode(node.getAttribute('id') || '');
      });
    }
    tabStore.removeTab(query);
    setTimeout(() => {
      router.replace({
        path: tabStore.getActiveTab().fullPath,
        query: {
          ...(tabStore.getActiveTab()?.query || {}),
        },
      });
    }, 0);
    return true;
  });
  LynkerSDK.ipc.handleRenderer('update-square-tab-item', async (query) => {
    console.log('update-square-tab-item', query);
    const index = tabStore.tabList.findIndex((i) => i.path_uuid === query.path_uuid);
    if (index > -1) {
      tabStore.tabList[index] = {
        ...tabStore.tabList[index],
        path_uuid: query.path_uuid,
        ...query,
      };
    }
    return true;
  });
});
</script>

<style lang="less" scoped>
.page-container {
  display: flex;
  height: 100%;
  background: var(--bg-kyy-color-bg-deep, #f5f8fe);
}

.page-content {
  position: relative;
  overflow-y: auto;
}

.sidebar {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 240px;
  height: 100%;
  padding-top: 16px;
  border-right: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);
  background: var(--bg-kyy-color-bg-light, #FFF);

  .page-title {
    color: @kyy_color_text_1;
    margin-bottom: 16px;
    margin-left: 16px;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-right: 16px;
  }

  .search-box {
    padding: 0 16px;
    margin-bottom: 16px;
  }

  .view-count {
    display: flex;
    width: 208px;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    border: 1px solid var(--border-kyy_color_border_default, #D5DBE4);
    background: var(--bg-kyy_color_bg_light, #FFF);
    margin: 0 auto 8px auto;
    justify-content: space-between;
    &--title {
      color: #516082;
    }
    &--number {
      color: #1A2139;
    }
    .curp{
      cursor: pointer;
      color: #4D5EFF
    }
  }
  .footer {
    position: relative;
    padding: 8px 8px;
    height: 48px;
    color: var(--text-kyy-color-text-2, #516082);
    cursor: pointer;
    .setting-wrap {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 8px;
      border-radius: 4px;

      &.active {
        background: #daecff;
        color: #2069e3;
        .title {
          color: #2069e3;
        }
      }
    }
    &::before {
      content: ' ';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 1px;
      background-color: var(--lingke-gray-1, #ECEFF5);;
    }
    &:empty::before {
      display: none;
    }

    .icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      margin-right: 12px;
    }

    .title {
      font-size: 14px;
    }
  }
}

.menu {
  flex: 1;
  padding: 0 16px;
  margin-bottom: 16px;
  overflow: auto;

  &-item {
    display: flex;
    align-items: center;
    padding: 12px;
    margin-bottom: 9px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-kyy_color_text_1, #1A2139);
    cursor: pointer;
    &:hover {
      background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
    }
    &.active {
      background: var(--bg-kyy-color-bg-list-foucs, #E1EAFF);
    }
  }

  &-title {
    flex: 1;
    color: var(--text-kyy-color-text-1, #1A2139);
    font-size: 16px;
    font-weight: 600;
  }

  :deep(.t-badge) {
    margin-left: auto;
    margin-right: 8px;
  }
  :deep(.t-badge--circle) {
    right: 0;
  }

  &-icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    &[name="icon20drafts"]  {
      color: #62bf7c;
    }
  }
}

.right {
  width: 360px;
  height: 100%;
  border-left: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);
  background: var(--bg-kyy-color-bg-light, #FFF);
  .ad-poster {
    margin: 12px;
    border-radius: 8px;
    background-color: transparent;
  }
}
</style>

<style>
.avatar-cert-pop {
  display: flex;
  width: 91px;
  height: 37px;
  padding: 4px 8px;
  justify-content: center;
  align-items: center;
  align-content: center;
  color: #fff;
  background-color: #000;
  color: #FFF;
  font-size: 14px;
  font-weight: 400;
}

.square-unfollow-dialog {
  .t-dialog {
    width: 336px;
  }
  .t-dialog__body__icon {
    color: var(--kyy_color_modal_title, #1A2139);
    font-size: 16px;
    font-weight: 600;
    line-height: 24px; /* 150% */
  }
}
</style>
