import { computed, ref } from 'vue';

import { imSearch } from '../service/request'
import { IRelationGroup, IRelationPrivate, MyAssistants } from '@renderer/api/im/model/relation';
import { getCards, getStaff } from '@/utils/auth'
import { searchMessages } from '@/views/message/service/msgHistory';

import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;

export const useGlobalSearch = () => {

    const searchPairsResult = ref<SearchContactRowModel[]>([])
    const searchAssistantsResult = ref<MyAssistants[]>([])

    const searchMsgResult = ref<MessageToSave[]>([])
    const searchGroupResult = ref<{group: GroupToSave, name: string, info?: string}[]>([])

    const groups = ref<GroupToSave[]>([]);
    const members = ref<ConversationMemberToSave[]>([]);
    const conversations = ref<ConversationToSave[]>([]);
    const highlightMap = ref(new Map());

    const twoPairs = computed(() => {
        if(searchPairsResult.value?.length) {
            return searchPairsResult.value?.slice(0, 3);
        }
        return []
    })

    const twoGroups = computed(() => {
        if(searchGroupResult.value?.length) {
            return searchGroupResult.value?.slice(0, 3);
        }
        return []
    })

    const twoMessages = computed(() => {
        if(searchMsgResult.value?.length) {
            return searchMsgResult.value?.slice(0, 3);
        }
        return []
    })
    /**
     *
     * @param text
     * 严谨一点的处理应该是
       1、内部身份卡（$xxx）,平台身份卡（PTxxx）取值用internal_team_id、internal_team_name
       2、外部身份卡（#xxx）场景需求取值取的是外部组织的id和名字就取team_id、team_name，场景需要取内部组织的id和名字就取internal_team_id、internal_team_name
     */

    const twoAssistants = computed(() => {
        if(searchAssistantsResult.value?.length) {
            return searchAssistantsResult.value?.slice(0, 3);
        }
        return []
    })

    // 搜素替换高亮
    const textChange = (text: string) => {
      if (text.length) {
        const originalText = highlightMap.value.get(text);
        if (originalText) {
          const regex = /<span>(?=[^<]*<\/span>)/g;
          // 替换为带样式的标签
          return originalText.replace(regex, '<span style="color:#4D5EFF;">') || '';
        }
      }
      return '';
    }

    const searchServerData = async (text: string) => {
        if (text.length) {
            const data = (await imSearch(text, { params: { highlightTag: 'span' } })).data;
            searchPairsResult.value = [];
            searchGroupResult.value = [];
            searchAssistantsResult.value = [];
            const result: SearchContactRowModel[] = [];
            // 把highlightMap对象的值放到 map 中
            if (Object.keys(data.highlightMap).length) {
                highlightMap.value = new Map(Object.entries(data.highlightMap) || []);
            }
            if (data.pairs?.length) {
                data.pairs.forEach((item: IRelationPrivate) => {

                    const peerMember = item.card
                    const nameText = item.comment ? `${item.comment}(${peerMember?.cardName})` : peerMember?.cardName
                    const nameWithTag = textChange(nameText) || '';
                    console.log('nameText', nameText, nameWithTag)

                    result.push({
                        teamID: peerMember?.teamId || peerMember?.internalTeamId, // 专属名称增加
                        main: item.main,
                        peer: item.peer,
                        avatar: peerMember?.avatar,
                        nameWithTag,
                        staffName: peerMember?.cardName,
                        nickname: nameText,
                        teamName: peerMember?.teamName,
                        departmentId: peerMember?.internalTeamId,
                        departmentName: peerMember?.internalTeamName,
                        jobId: peerMember?.departments?.[0]?.jobId,
                        jobName: peerMember?.departments?.[0]?.jobName,
                        cardId: peerMember?.cardId,
                        recent: item.origin === 'IDLE_TEMPORARY' ? false : true, // 临时关系不展示最近联系人
                        relation: item.origin,
                        itemID:`${item.main}${item.peer}`
                    });
                });
            }
            if (data.assistants?.length) {
                searchAssistantsResult.value =  data.assistants.map((item: MyAssistants) => {
                  const nameWithTag = textChange(item.name) || '';
                  return {
                    avatar: item.avatar,
                    name: nameWithTag ? nameWithTag : item.name,
                    openid: item.openid,
                    conversationId: item.conversationId,
                    assistantId: item.assistantId,
                  }
                });
            }
            if(data.cards?.length) {

                const localCardsAndStaffs = [...getCards(), ...getStaff(true)]
                console.log('====>localCardsAndStaffs', localCardsAndStaffs)
                data.cards?.forEach((item: any) => {
                    const locard =  localCardsAndStaffs.find((x) => +x.teamId === +item.teamId || x.teamId === item.internalTeamId);
                    const main = locard?.uuid || ''

                    const nameWithTag = textChange(item.cardName || item.staffName) || '';

                    result.push({
                        teamID: item?.teamId, // 专属名称增加
                        avatar: item.avatar,
                        staffName: item.staffName,
                        nickname: item.cardName,
                        nameWithTag,
                        teamName: item.teamName,
                        departmentId: item?.departments?.[0]?.id,
                        departmentName: item?.departments?.[0]?.name,
                        jobId: item?.departments?.[0]?.jobId,
                        jobName: item?.departments?.[0]?.jobName,
                        cardId: item.cardId,
                        peer: item.cardId,
                        main,
                        // itemID:`${main}${item.cardId}`
                    })
                })
            }
            if(data.groups?.length){
              searchGroupResult.value = data.groups.map(item => {
                const nameWithTag = textChange(item.name) || '';
                const name = nameWithTag ? nameWithTag : item.name.replace(text, `<span style="color: #4d5eff">${text}</span>`)
                const info = item.members?.length ? (item.members[0].card?.cardName || item.members[0].card?.nickname || '').replace(text, `<span style="color: #4d5eff">${text}</span>`) : '';
                return {name, info, group: item, avatar: item.avatar || item.attachment?.avatar};
            })
            }
            console.log('====>result', data, result, searchAssistantsResult.value);
            // 【【搜索】app端联系人搜索出现搜索结果重复】https://www.tapd.cn/69781318/bugtrace/bugs/view/1169781318001048781 取消去重
            // const result2 = [...new Map(result.map(item => [item.itemID, item])).values()];
            // console.log('===>result2',result2)
            searchPairsResult.value = result;

        } else {
            searchPairsResult.value = [];
            searchGroupResult.value = []
            searchAssistantsResult.value = []
        }
    }

    const searchDbData = async (text: string) => {
        if (text.length) {
           const searchParams = {
                conversationID: '',
                keywordList: [text],
                senderUserIDList:[],
                subContentTypeList:['file','link','text','location','richText'],
                messageTypeList:[101,114],
                pageIndex: 1,
                count: 20
            }
            const res = (await searchMessages(searchParams, 'golabel'))?.result;
            console.log('=====>searchDbData',res);
            searchMsgResult.value = res
        } else {
            searchMsgResult.value = []
        }
    }

    const loadData = async () => {
        const [dbGroups, dbMembers ] = (await Promise.all([
            ipcRenderer.invoke("im.group.query"),
            ipcRenderer.invoke("im.session.member.query"),
        ]));

        groups.value = dbGroups;
        // 只查询群聊的成员
        members.value  = dbMembers;
    }

    return {
        loadData,
        groups,
        members,
        conversations,

        searchPairsResult,
        searchMsgResult,
        searchGroupResult,
        searchAssistantsResult,

        twoPairs,
        twoGroups,
        twoMessages,
        twoAssistants,

        searchDbData,
        // searchGroup,
        searchServerData,
    }
};
