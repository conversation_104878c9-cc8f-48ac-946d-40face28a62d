<template>
  <div class="digital-campus-container containerPc">
    <!-- 顶部背景及内容区域 -->
    <div class="header-bg">
      <div class="header-title">
        <iconpark-icon name="iconarrowlift" class="header-icon" @click="goBack"></iconpark-icon>
        <div class="search-input search-inputw2">
          <iconpark-icon name="iconsearch" class="search-icon"></iconpark-icon>
          <t-input
            v-model="keyword"
            ref="searchInput"
            autofocus
            borderless
            type="text"
            style="border: none; outline: none; padding-right: 20px"
            placeholder="搜索学校名称"
            @change="handleSearch"
          >
          </t-input>
          <iconpark-icon
            v-if="keyword"
            @click.stop="clearSearch"
            name="icon20delet"
            class="icon20delet"
          ></iconpark-icon>
        </div>
      </div>
    </div>

    <!-- 学校列表区域 -->
    <div ref="scrollContainer" class="school-list" @scroll="handleScroll">
      <div v-for="(item, index) in displaySchools" :key="item.id || index" class="school-item">
        <div class="school-info">
          <img :src="item.team_logo || deflogo" alt="school logo" class="school-logo" />
        </div>
        <!-- <div class="school-name">
          {{ item.exclusive_name || item.team_name }}
        </div> -->
        <div class="school-name">
          <div class="school-name">
            {{ item.exclusive_name || item.team_name }}
          </div>
          <div class="school-address" v-if="item.distance_extend">
            <iconpark-icon name="iconpositioning" class="iconpositioning"></iconpark-icon>
            <span style="margin-top: 2px">{{ distanceCalc(item.distance_extend?.distance) }}</span>
          </div>
        </div>
        <button class="join-btn" @click="toapply(item)">申请加入</button>
      </div>
      <!-- 加载状态提示 -->
      <div class="loading-tip" v-if="isLoading && !searchLoading">
        111
        <t-loading size="14px" text="加载中..."></t-loading>
      </div>
      <div v-if="!hasMore && !isLoading && displaySchools.length > 0" class="loading-tip">已经到底啦~</div>
    </div>
    <div v-if="displaySchools.length === 0 && !isLoading" class="empty" :class="keyword ? 'empty-c' : 'empty-top'">
      <img
        v-if="!keyword"
        class="empty-img"
        src="http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/slogo.png"
        alt=""
      />
      <Empty v-else name="no-result" />
    </div>

    <div class="loading-tip2" v-if="searchLoading">
      <t-loading size="14px" text="加载中..."></t-loading>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onActivated } from 'vue';
import { useRouter } from 'vue-router';
import Empty from '@/components/Empty.vue';
import { teamListByUni } from '@/api/order';
import { debounce } from 'lodash';
import { MessagePlugin } from 'tdesign-vue-next';

// 数据相关变量
const displaySchools: any = ref([]); // 展示的学校列表，改为普通变量
const currentPage = ref(1); // 当前页码
const totalVal = ref(0); // 当前页码
const isLoading = ref(false); // 是否正在加载
const hasMore = ref(true); // 是否还有更多数据
const keyword = ref(''); // 搜索关键词
const emit = defineEmits(['close']);
const deflogo = 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/defaultOrgLogo.svg';
const searchInput = ref(null);
const scrollContainer = ref(null);
const searchLoading = ref(false);

const goBack = () => {
  //   router.back();
  // history.go(-1);
  searchInput.value?.blur();
  emit('close');
  totalVal.value = 0;
  keyword.value = '';
  displaySchools.value = [];
};
const props = defineProps({
  markerInfo: {
    type: Object,
    default: () => ({}),
  },
});
// 初始化加载第一页数据
onMounted(() => {
  //   fetchSchools();
  searchInput.value.focus();
  // 监听页面滚动让searchInput.value失去焦点
  window.addEventListener('scroll', () => {
    searchInput.value.blur();
  });
});
const scrollPosition = ref(0);
onActivated(() => {
  // 恢复滚动位置
  setTimeout(() => {
    if (scrollContainer.value && scrollPosition.value > 0) {
      scrollContainer.value.scrollTop = scrollPosition.value;
    }
  }, 100);
});
const focusrun = () => {
  searchInput.value.focus();
};
// 从接口获取学校数据
const fetchSchools = async () => {
  if (isLoading.value || !hasMore.value) return;

  isLoading.value = true;

  try {
    const params = {
      keyword: keyword.value,
      page: currentPage.value,
      pageSize: 20,
      longitude: props.markerInfo.location.lng,
      latitude: props.markerInfo.location.lat,
    };
    teamListByUni(params)
      .then((res) => {
        console.log(res);
        const { team_list, total } = res.data;
        if (currentPage.value === 1) {
          displaySchools.value = team_list;
        } else {
          displaySchools.value = [...displaySchools.value, ...team_list];
        }
        totalVal.value = total;
        if (displaySchools.value.length >= total) {
          hasMore.value = false;
        }
        setTimeout(() => {
          searchLoading.value = false;
          isLoading.value = false;
        }, 500);
      })
      .catch((err) => {
        console.error('获取学校数据失败:', err);
        if (err?.code === 'ERR_NETWORK' || error?.message === 'Network Error') {
          MessagePlugin.error('网络连接失败，请检查网络后重试');
        }
        setTimeout(() => {
          searchLoading.value = false;
          isLoading.value = false;
        }, 500);
      });
  } catch (error) {
    searchLoading.value = false;
    console.error('获取学校数据失败:', error);
    if (error?.code === 'ERR_NETWORK' || error?.message === 'Network Error') {
      MessagePlugin.error('网络连接失败，请检查网络后重试');
    }
  } finally {
  }
};
const distanceCalc = (distance) => {
  if (distance) {
    distance = distance > 999 ? `${(distance / 1000).toFixed(1)}km` : `${distance}m`;
  }
  return distance;
};
// 滚动事件处理函数，判断是否触底
const handleScroll = (e) => {
  const container = e.target;
  // 触底判断，留10px误差
  searchInput.value.blur();
  if (
    container.scrollTop + container.clientHeight >= container.scrollHeight - 400 &&
    !isLoading.value &&
    hasMore.value
  ) {
    loadMore();
  }
  scrollPosition.value = container.scrollTop;
};

// 加载下一页数据
const loadMore = () => {
  currentPage.value++;
  fetchSchools();
};

const handleSearch = () => {
  if (!keyword.value) {
    totalVal.value = 0;
    displaySchools.value = [];
    return;
  }
  currentPage.value = 1;
  hasMore.value = true;
  searchLoading.value = true;

  fetchSchools();
};

// const handleSearch = debounce(() => {
//   // 重置分页，重新加载第一页数据
//   currentPage.value = 1;
//   hasMore.value = true;
//   fetchSchools();
// }, 300);
const router = useRouter();

const toapply = (item) => {
  // router.push({
  //   path: `${location.origin}/account/jump?to=uniInvite&link=${item.team_link}`,
  // });
  router.push({
    path: `/account/jump?to=uniInvite&link=${item.team_link}`,
    query: {
      to: 'uniInvite',
      link: item.team_link,
      orgkey: 'uniJoin',
    },
  });
};
const clearSearch = () => {
  keyword.value = '';
  handleSearch();
};
defineExpose({
  focusrun,
});
</script>

<style lang="less" scoped>
.digital-campus-container {
  width: 100%;
  height: 100vh;
  background-color: #eaf8f9;
  background: url('http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/uni_s_bg.png') no-repeat;
  background-size: 100% 300px;
  overflow-y: hidden;
}

/* 顶部背景及内容样式 */
.header-bg {
  width: 100%;
  .header-title {
    display: flex;
    width: 100%;  
    height: 64px;
    padding: 12px 16px;
    align-items: center;
    gap: 8px;
    position: relative;

    .header-icon {
      font-size: 28px;
      color: #1a2139;
    }
  }
}
.header-content {
  .title {
    color: var(--text-kyy_color_text_white, #fff);
    font-feature-settings: 'liga' off, 'clig' off;
    text-shadow: 0 0 12px #1697b5;
    font-family: 'PingFang SC';
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 36px; /* 150% */
  }

  .sub-title {
    color: #fff;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: 'PingFang SC';
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 36px;
  }

  .desc {
    color: #fff;
    font-feature-settings: 'liga' off, 'clig' off;

    /* kyy_fontSize_3/regular */
    font-family: 'PingFang SC';
    font-size: 17px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px; /* 152.941% */
    margin-top: 8px;
    margin-bottom: 20px;
  }

  .search-box {
    width: 100%;
    max-width: 400px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    padding: 5px 10px;
    display: flex;
    align-items: center;

    .search-input {
      width: 100%;
      border: none;
      outline: none;
      background: transparent;
      font-size: 14px;
      padding: 8px 0;
    }
  }
}
/* 学校列表滚动容器样式 */
.school-list {
  width: 100%;
  max-height: calc(100vh - 80px);
  overflow-y: auto;
  box-sizing: border-box;
  padding: 0px 16px 12px 16px;
  /* 滚动条样式 */
  &::-webkit-scrollbar {
    width: 0px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-track {
    background-color: #f5f5f5;
  }
}

/* 学校列表项样式 */
.school-item {
  display: flex;
  width: 100%;
  height: 76px;
  padding: 12px;
  align-items: center;
  gap: 12px;
  border-radius: 12px;
  border: 1px solid #fff;
  background: rgba(255, 255, 255, 0.9);
  margin-bottom: 12px;
  .school-info {
    display: flex;
    align-items: center;

    .school-logo {
      width: 52px;
      height: 52px;
      margin-right: 10px;
      border-radius: 50%;
      object-fit: cover;
    }
  }
  .school-name {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    flex: 1 0 0;
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1a2139);
    font-feature-settings: 'liga' off, 'clig' off;
    text-overflow: ellipsis;
    font-family: 'PingFang SC';
    font-size: 17px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px; /* 152.941% */
    .school-address {
      .iconpositioning {
        font-size: 20px;
      }
      color: var(--text-kyy_color_text_3, #828da5);
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      display: flex;
      // align-items: center;
      margin-top: 2px;
    }
  }
  .join-btn {
    display: flex;
    height: 28px;
    padding: 10px 8px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 16px;
    border: 1px solid #0ea9d8;
    color: #0ea9d8;
    font-feature-settings: 'liga' off, 'clig' off;
    background-color: transparent;
    /* kyy_fontSize_2/regular */
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}

/* 加载提示样式 */
.loading-tip {
  text-align: center;
  color: #828da5;
  padding: 15px 0;
  font-size: 14px;
}
.search-input {
  display: flex;
  height: 40px;
  padding: 0px 8px;
  align-items: center;
  gap: 4px;
  border-radius: 24px;
  border: 2px solid #347298;
  background: #fff;
  backdrop-filter: blur(10px);
  position: relative;
  .search-icon {
    font-size: 24px;
    color: #828da5;
  }
  .search-i {
    // 去除输入框边框 hover样式
    border: none;
    outline: none;
    width: 100%;
    height: 28px;
    color: #1a2139;
    font-size: 17px;
    padding-right: 23px;
  }
  .search-i::placeholder {
    color: var(--text-kyy_color_text_5, #acb3c0);
    font-feature-settings: 'liga' off, 'clig' off;

    /* kyy_fontSize_3/regular */
    font-family: 'PingFang SC';
    font-size: 17px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px; /* 152.941% */
  }
}
.search-inputw1 {
  width: 100%;
}
.search-inputw2 {
  flex: 1;
}

.empty {
  width: 100%;
  height: calc(100% - 78px);

  display: flex;
  flex-direction: column;
  align-items: center;
  .empty-img {
    width: 204px;
    height: 48px;
  }
}
.empty-c {
  justify-content: center;
}
.empty-top {
  margin-top: 120px;
}
.icon20delet {
  font-size: 20px;
  color: #fff;
  position: absolute;
  top: 8px;
  right: 10px;
}
:deep(.loading-tip .t-loading) {
  color: #fff;
}
:deep(.t-loading__gradient-conic) {
  transform: scale(1) !important;
}
.containerPc {
  margin: 0 auto;
}
:deep(.t-input--borderless:not(.t-input--focused):hover) {
  border: none;
  background-color: transparent;
}
:deep(.t-input--borderless:not(.t-input--focused)) {
  border: none;
  background-color: transparent;
}
:deep(.t-input:focus) {
  border: none;
  background-color: transparent;
  box-shadow: none;
}
:deep(.t-input:hover) {
  border: none;
  background-color: transparent;
}
:deep(.t-input--focused) {
  border: none;
  box-shadow: none;
}
.loading-tip2 {
  height: 50vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
