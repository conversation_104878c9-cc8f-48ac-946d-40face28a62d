import kill from 'tree-kill';
import { app, session, powerMonitor, crashReporter, nativeImage, globalShortcut, protocol as pl, BrowserWindow } from "electron";
import { join } from "path";
import { URL } from 'url';
import { protocol } from "@main/config/const";
import { dbDestory } from "./message/dbManager";
import { initSceenshoots } from './services/screenshoots';
import {
  initShortcut,
  removeAllShortcut
} from './services/shortcut';
/** ====================================== */
/** 引入electron-sdk并初始化 esm 打包 问题，暂时以require引入 */
// eslint-disable-next-line import/order
import { getSDK, initialize } from '@lynker-desktop/electron-sdk/main';
const preloadPath = join(__dirname, "../main/preload.js");
const rendererPath = join(__dirname, "../renderer");
initialize({
  protocol,
  loadingViewUrl: loadingURL,
  errorViewUrl: errorURL,
  preloadWebContentsConfig: {
    url: `${winURL}#/loading`,
    // url: preloadHtmlURL,
    // url: 'about:blank',
    enableBW: false,
    enableBW_FramelessWithButtons: true,
    enableBW_FramelessNoButtons: true,
    enableBV: true,
    customLoadURL: async (url, originURL, webContents) => {
      // 常量配置
      const ROUTE_CONFIG = {
        MAX_RETRIES: 6,
        RETRY_DELAY: 300, // 每次重试间隔 300ms
        TIMEOUT: 1000, // 总超时时间 1s
        VUE_APP_CHECK_INTERVAL: 100 // Vue 应用检查间隔
      };

      // 检查 Vue 应用是否可用
      const getVueApp = async (): Promise<boolean> => {
        try {
          const result = await webContents.executeJavaScript(`
            (function() {
              const app = window?.__RINGKOL_VUE_APP__;
              return app ? true : false;
            })();
          `);
          return Boolean(result);
        } catch (error) {
          console.error('检查 Vue 应用状态失败:', error);
          return false;
        }
      };

      // 执行路由跳转
      const executeRoute = async (path: string): Promise<boolean> => {
        try {
          const result = await webContents.executeJavaScript(`
            (async function() {
              try {
                console.log('开始执行路由跳转脚本');
                const app = window?.__RINGKOL_VUE_APP__;
                if (!app) {
                  console.error('Vue 实例未找到');
                  return false;
                }

                const router = app.config.globalProperties.$router;
                if (!router) {
                  console.error('Router 实例未找到');
                  return false;
                }

                console.log('当前路由:', router.currentRoute.value.fullPath);
                console.log('目标路由:', '${path}');

                const startTime = performance.now();

                await new Promise((resolve) => {
                  router.push('${path}').then(() => {
                    setTimeout(() => {
                      requestAnimationFrame(() => {
                        const endTime = performance.now();
                        console.log('路由完整加载完成, 耗时:', Math.round(endTime - startTime), 'ms');
                        resolve(true);
                      });
                    }, 0);
                  });
                });

                return true;
              } catch (err) {
                console.error('路由跳转出错:', err);
                return false;
              }
            })();
          `);
          return Boolean(result);
        } catch (error) {
          console.error('执行路由跳转脚本失败:', error);
          return false;
        }
      };

      // 处理路由推送逻辑
      const handlePush = async (path: string): Promise<boolean> => {
        const startTime = performance.now();
        let attempt = 0;

        while (attempt < ROUTE_CONFIG.MAX_RETRIES) {
          // 检查超时
          if (performance.now() - startTime > ROUTE_CONFIG.TIMEOUT) {
            console.warn('路由跳转总时间超过 1s，执行兜底逻辑');
            try {
              await webContents.loadURL(url);
              return true;
            } catch (error) {
              console.error('兜底逻辑执行失败:', error);
              return false;
            }
          }

          try {
            const hasApp = await getVueApp();
            if (hasApp) {
              const success = await executeRoute(path);
              if (success) {
                console.log(`路由跳转成功，尝试次数: ${attempt + 1}`);
                return true;
              }
            }
          } catch (error) {
            console.error(`第 ${attempt + 1} 次执行失败:`, error);
          }

          attempt++;
          if (attempt < ROUTE_CONFIG.MAX_RETRIES) {
            await new Promise(resolve => setTimeout(resolve, ROUTE_CONFIG.RETRY_DELAY));
          }
        }

        console.warn(`路由跳转失败，已尝试 ${ROUTE_CONFIG.MAX_RETRIES} 次`);
        return false;
      };

      // 主逻辑处理
      try {
        if (url.startsWith('app://-/index.html') || url.startsWith('app://-/') || (!app.isPackaged && url.startsWith('http://localhost'))) {
          const result = await getVueApp();
          if (result) {
            const urlObj = new URL(url);
            const path = urlObj.hash.replace('#', '');
            if (path) {
              const pushResult = await handlePush(path);
              if (!pushResult) {
                console.warn('路由跳转失败，使用原始加载方式');
                originURL(url);
              }
            } else {
              console.warn('无效的路由路径，使用原始加载方式');
              originURL(url);
            }
          } else {
            console.warn('Vue 应用未就绪，使用原始加载方式');
            originURL(url);
          }
        } else {
          originURL(url);
        }

        console.log('customLoadURL 处理完成:', url);
        return url;
      } catch (error) {
        console.error('customLoadURL 执行出错:', error);
        // 发生错误时使用原始加载方式作为兜底
        originURL(url);
        return url;
      }


    }
  },
  webviewDomainWhiteList: [
    '-',
    'localhost',
  ],
  preload: preloadPath,
  serveOptions: [{ scheme: 'app', directory: rendererPath }],
  // 资源缓存
  resourceCacheOptions: {
    // 缓存目录
    cacheDir: join(app.getPath('temp'), 'resources'),
    // 缓存有效期
    cacheTTL: 24 * 60 * 60 * 1000, // 24小时缓存
    // 缓存资源类型
    match: /\.(png|jpe?g|webp|gif|svg|woff2?|ttf|eot|otf|mp4|webm|ogg|mp3|wav|json|ico|bmp|m4a|m3u8)(\\?.*)?$/i,
    // 限定域名
    allowedOrigins: [
      'https://image.ringkol.com',
      'https://web-qa.ringkol.com',
      'https://web-dev.ringkol.com',
      'https://web-pre.ringkol.com',
      'https://web.ringkol.com',
    ],
  }
});
console.info('preloadPath: ', preloadPath, loadingURL, errorURL);
app.setMaxListeners(100);
/** 手动添加垃圾回收
 * 32位系统：默认分配约 700MB 内存上限。
 * 64位系统：默认分配约 1.4GB 内存上限。
 * 手动设置内存上限
 */
app.commandLine.appendSwitch("js-flags", "--expose-gc --max-old-space-size=4096");


// 判断app是否激活
function isAppActive() {
  try {
    const focusedWindow = BrowserWindow.getFocusedWindow();
    if (focusedWindow) {
      return true;
    }
    const allWindows = BrowserWindow.getAllWindows();
    return allWindows.find(win => win.isFocused()) || false;
  } catch (error) {
    return false;
  }
}

app.on('browser-window-blur', () => {
  console.log('window-all-closed');
  // 注销快捷键
  globalShortcut.unregister('CmdOrCtrl+Shift+I');
});
app.on('browser-window-focus', () => {
  globalShortcut.unregister('CmdOrCtrl+Shift+I');
  const ret = globalShortcut.register('CmdOrCtrl+Shift+I', () => {
    const _isAppActive = isAppActive();
    console.log('isAppActive: ', _isAppActive);
    // 如果app未激活，则不打开调试工具
    if (!_isAppActive) {
      return;
    }
    getSDK().windowManager.close(`debugtools`);
    getSDK().windowManager.create({
      name: `debugtools`,
      url: `${winURL}#/sdk/debugtools`,
      browserWindow: {
        show: true,
        minHeight: 100,
        minWidth: 100,
        width: 450,
        height: 600,
        alwaysOnTop: true,
      }
    });
  });
  console.log(`CmdOrCtrl+Shift+I: `, globalShortcut.isRegistered('CmdOrCtrl+Shift+I'));
});


// import { devtron } from '@lynker-desktop/devtron'
// import { monitorMain } from '@lynker-desktop/devtron/monitorMain'
// console.log('devtron: ', devtron, monitorMain);
// monitorMain()
app.whenReady().then(async () => {
  // try {
  //   await devtron.install()
  //   console.log('devtron install success');
  // } catch (error) {
  //   console.log('devtron install error: ', error);
  // }
  initShortcut();
  initSceenshoots();
});

app.on('will-quit', () => {
  // 注销快捷键
  globalShortcut.unregister('CmdOrCtrl+Shift+I');
  // 注销所有快捷键
  removeAllShortcut();
  kill(process?.pid, () => {
    process.exit(1);
  });
});
/** 引入electron-sdk并初始化 */
/** ====================================== */

import IpcMainHandler from "./services/ipcMain";
import { initWindow, onSecondInstance } from "./services/windowMain";
import { lib, loadingURL, errorURL, getUrl, winURL, preloadHtmlURL } from "../main/config/StaticPath";
import DisableButton from "./config/DisableButton";
import { registerProtocol } from "./services/protocol";
import { windowManager } from "./services/windowManager";
import { updateDevMenuList } from "./config/devMenu";
import { packUpdateInit } from "./services/packUpdate";
const path = require("path");
import zhiXingDb from "./zhixing/db";
registerProtocol();
global.invokeParams = {};


const setAppGlobalData = () => {
  const electronDistPath = join(__dirname, "../");
  const distPath = join(electronDistPath, "../dist");
  const publicPath = process.env.NODE_ENV === 'development' ? join(electronDistPath, "../public") : distPath;
  global.pathConfig = {
    electronDistPath,
    distPath,
    publicPath,
    indexHtml: join(distPath, "index.html"),
    preload: join(__dirname, "../preload/index.js"),
  };
};
// setAppGlobalData()
// Mac 端协议唤起
app.on("open-url", (event, url) => {
  // global.invokeParams = { ...global.invokeParams, ...handleSchemeWakeup(url) };
  // dialog.showMessageBox({ message: `open-url: ${url}` });
  if (app.isReady()) {
    onSecondInstance(event, url);
  }
});

// 禁用硬件加速/ 禁用会照成windows身份卡弹窗问题
// app.disableHardwareAcceleration();

function onAppReady() {

  IpcMainHandler.Mainfunc();

  initWindow(false);


  packUpdateInit();

  DisableButton.Disablef12();
  // if (showDevTool) {
    try {
      const { VUEJS_DEVTOOLS } = require("electron-devtools-vendor");
      session.defaultSession.loadExtension(VUEJS_DEVTOOLS, {
        allowFileAccess: true,
      });
    } catch (error) {
      console.error("installExtension: ", error);
    }
    app.on("web-contents-created", (event, contents) => {
      updateDevMenuList();
      contents.on('destroyed', () => {
        console.log('webContents destroyed:', contents.id);
        updateDevMenuList();
      });
      contents.on('did-finish-load', () => {
        console.log('webContents finished loading:', contents.getURL());
      });
    });


    try {
      session.defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
        if (permission === 'geolocation') {
          callback(true); // 允许地理位置访问
        } else {
          callback(true);
        }
      });
    } catch (error) {
      console.error("setPermissionRequestHandler: ", error);
    }

  // }

  // setTimeout(() => {
  //   // 初始化浏览器视图
  //   initBrowserView(defaultConfig['default'])
  // }, 400);
}
app.on("will-finish-launching", () => {
  // mac dock
  if (process.platform === "darwin") {
    const icon = nativeImage.createFromPath(path.join(lib, "docker.png"));
    app.dock.setIcon(icon);
  }
});

function crashReport() {
  crashReporter.start({
    // 要上报的服务器地址
    // submitURL: 'http://127.0.0.1:3000/api/crash/createCrashOne',
    submitURL: "https://apm.lynker.cn/api/crash/createCrashOne",
    // 产品名称
    productName: process.env.NODE_ENV,
    compress: false,

    // 是否上传到服务器，默认为true，如果关闭只在本地生成crash文件
    uploadToServer: true,

    // 携带的参数，globalExtra中的参数所有进程崩溃都会携带
    // globalExtra: {
    //   userId: 'xxx'
    // },
  });
}

app.on("ready", () => {


  // 监听进程崩溃
  crashReport();
  // 监听Electron的系统恢复事件（从睡眠、休眠中恢复，或者从锁屏状态恢复
  // 设置电源状态消息提醒
  powerMonitor.on("resume", () => {
    console.log("=====>powerMonitor-resume2", e);
    if(windowManager.mainWindow && !windowManager.mainWindow.isDestroyed()) {
      windowManager.mainWindow.webContents.send("system-lock-screen", { type: "resume" });
    }
  })
  // 系统挂起
  powerMonitor.on("suspend", () => {
    if(windowManager.mainWindow && !windowManager.mainWindow.isDestroyed()) {
      windowManager.mainWindow.webContents.send("system-lock-screen", { type: "suspend" });
    }
  });
  powerMonitor.on("lock-screen", () => {
    if(windowManager.mainWindow && !windowManager.mainWindow.isDestroyed()) {
      windowManager.mainWindow.webContents.send("system-lock-screen", { type: "lock-screen" });
    }
  });
  powerMonitor.on("unlock-screen", () => {
    if(windowManager.mainWindow && !windowManager.mainWindow.isDestroyed()) {
      windowManager.mainWindow.webContents.send("system-lock-screen", { type: "unlock-screen" });
    }
  });

  // 初始化窗口-主要针对个人信息设置这一块
  // createSettingWindow("init");
  // createHelpAndServiceWindow("{}", "init");
  // createInvoiceCenterWindow("init");
  // createMyOrderWindow("{}", "init");
  // active
  // 注册知行数据库
  zhiXingDb();
});

// 清理所有资源和进程的函数
export const cleanupAllProcesses = () => {
  // 关闭所有窗口
  // BrowserWindow.getAllWindows().forEach(window => {
  //   if (!window.isDestroyed()) {
  //     window.destroy(); // 使用 destroy 而不是 close
  //   }
  // });

  // // 关闭所有 webContents
  // webContents.getAllWebContents().forEach(content => {
  //   if (!content.isDestroyed()) {
  //     content.destroy();
  //   }
  // });
  // 注销所有快捷键
  globalShortcut.unregisterAll();

  // 清理数据库连接
  zhiXingDb().close();
  dbDestory();
  // 清理会话数据
  session.defaultSession.clearCache();
  session.defaultSession.clearStorageData();

  // 强制结束所有子进程
  // process.getConnections((err, count) => {
  //   if (!err && count > 0) {
  //     process.disconnect();
  //   }
  // });

  // 如果还有其他进程，强制退出
  // app.exit(0);
};
app.on("activate", () => {
  windowManager.showWindow();
  // windowManager.mainWindow.webContents.send("system-activate");
});

// before-quite
app.on("before-quit", () => {
  if (process.platform === "darwin") {
    app.exit();
  }
  try {
    cleanupAllProcesses();
  } catch (error) {

  }
});

if (!process.mas && !app.requestSingleInstanceLock()) {
  app.quit();
} else {
  app.whenReady().then(onAppReady);

  // 限制多开
  app.on("second-instance", onSecondInstance);

  // 由于9.x版本问题，需要加入该配置关闭跨域问题
  app.commandLine.appendSwitch("disable-features", "OutOfBlinkCors");

  app.on("window-all-closed", () => {
    // 所有平台均为所有窗口关闭就退出软件
    try {
      cleanupAllProcesses();
    } catch (error) {

    }
    app.quit();
  });
  app.on("browser-window-created", () => {
    console.log("window-created");
  });
}

// resume事件在用户系统从睡眠、休眠中恢复时触发
// unlock-screen事件在用户锁屏状态恢复时触发
// 在resume事件发生时，系统的一些状态并没有完全恢复，如果在这个事件的回调函数中马上去访问网络、读写文件，可能会导致不可预知的异常。
// powerMonitor.on("resume", (e) => {
//   console.log("=====>powerMonitor-resume", e);
//   powerMonitor.once("unlock-screen", () => {
//     //执行你的逻辑
//     if (windowManager.mainWindow && !windowManager.mainWindow.isDestroyed()) {
//       windowManager.mainWindow.webContents.send("system-lock-screen", { type: "unlock-screen" });
//     }
//   });
// });
