<template>
  <div>
    <t-dialog v-model:visible="visible" :z-index="2601" :header="props.header" attach="body" width="672px"
      @close="onClose(true)" @close-btn-click="onClose(true)">
      <template #body>
        <div v-if="radioFlag" class="radios-group">
          <div class="font-name">字段名称：</div>
          <t-radio-group :value="chengRange" style="margin-bottom: 16px" :default-value="1" @change="changeRadio">
            <t-radio :value="1"> 可见所有人 </t-radio>
            <t-radio :value="2"> 可见本部门 </t-radio>
            <t-radio :value="3"> 指定部门或成员 </t-radio>
          </t-radio-group>
        </div>
        <div v-show="chengRange === 3 || !radioFlag" class="toBody">
          <div class="toBody-left">
            <t-input v-model="searchKey" class="input" placeholder="搜索" @keyup="onListenSearch">
              <template #prefix-icon>
                <svg class="iconpark-icon" style="width: 16px">
                  <use href="#search" />
                </svg>
              </template>
            </t-input>
            <div v-show="searchKey" class="content">
              <div class="group" style="margin-top: 0; overflow: auto; height: 258px">
                <span v-for="staff in searchStaffs" :key="staff.idStaff" class="group-item">
                  <t-checkbox :checked="staff.isChecked" @change="setCheckboxChange(staff)">
                    <template #label>
                      <div class="selectLabel">
                        <img v-if="staff.avatar" :src="staff.avatar" class="image" />
                        <div v-else class="mock">
                          {{ getNameContent(staff.name) }}
                        </div>
                        <span class="text line-1"> {{ staff.name }} </span>
                      </div>
                    </template>
                  </t-checkbox>
                  <!-- <span class="tips"> 下级 </span> -->
                </span>
              </div>
            </div>
            <div v-show="!searchKey" class="content">
              <div class="bcrumb">
                <span v-for="levelItem in levelDatas" :key="levelItem.id" class="bcrumb-item"
                  @click="goOrganiseLevel(levelItem)">
                  <span class="name line-1">{{ levelItem.name }}</span>
                  <svg class="iconpark-icon" style="color: #a1a2a4; width: 16px; height: 16px">
                    <use href="#right" />
                  </svg>
                </span>
                <!-- <span class="bcrumb-item">
                  <span class="name line-1">产研的地方水电费中心心</span>
                  <svg class="iconpark-icon" style="color: #a1a2a4; width: 16px; height: 16px">
                    <use href="#right"></use>
                  </svg>
                </span> -->
              </div>

              <div v-show="!props.isOnly" class="group" style="width: auto">
                <span class="group-item">
                  <!-- :indeterminate="indeterminate" -->
                  <t-checkbox :checked="checkAll" :label="t('activity.activity.allChange') "
                    @change="setCheckboxAllChange" />
                </span>
              </div>
              <!-- <t-checkbox-group v-model="value" @change="onChange"> -->
              <div class="group" style="margin-top: 0; overflow: auto; height: 285px">
                <span v-for="child in currentMembersAndDepartments.children" :key="child.departmentId"
                  :class="{ 'group-item': true, disabled: child.isChecked }">
                  <t-checkbox :checked="child.isChecked" :disabled="isOnly" @change="setCheckboxChange(child)">
                    <template #label>
                      <div class="selectLabel">
                        <!-- <img src="@/assets/<EMAIL>" class="image" /> -->
                        <div class="icon">
                                    <iconpark-icon name="iconorganize-a961a3m4" class="iconp" />

                        </div>
                        <span class="text line-1">
                          {{ child.name }} ({{ child.staffsCount }})</span>
                      </div>
                    </template>
                  </t-checkbox>
                  <span class="tips" @click="setCurrentChild(child)">
                    {{ t("approve.subordinate") }}
                  </span>
                </span>
                <span v-for="staff in currentMembersAndDepartments.staffs" :key="staff.idStaff" class="group-item">
                  <t-checkbox :checked="staff.isChecked" @change="setCheckboxChange(staff)">
                    <template #label>
                      <div class="selectLabel">
                        <img v-if="staff.avatar" :src="staff.avatar" class="image" />
                        <div v-else class="mock">
                          {{ getNameContent(staff.name) }}
                        </div>
                        <span class="text"> {{ staff.name }} </span>
                      </div>
                    </template>
                  </t-checkbox>
                  <!-- <span class="tips"> 下级 </span> -->
                </span>
              </div>
              <!-- </t-checkbox-group> -->
            </div>
          </div>
          <div class="toBody-line" />
          <div class="toBody-right">
            <div class="behaver">{{ t("approval.desgin.baseinfo.sed") }}{{ isCheckedItems.length }}</div>
            <div class="selectGroup">
              <span v-for="(isCheckedItem, isCheckedItemIndex) in isCheckedItems" :key="
                  isCheckedItem.hasOwnProperty('idStaff')
                    ? isCheckedItem.idStaff
                    : isCheckedItem.departmentId
                " class="selectGroup-item">
                <div v-if="isCheckedItem.hasOwnProperty('idStaff')" class="selectLabel">
                  <img v-if="isCheckedItem.avatar" :src="isCheckedItem.avatar" class="image" />
                  <div v-else class="mock">
                    {{ getNameContent(isCheckedItem.name) }}
                  </div>
                  <span class="text"> {{ isCheckedItem.name }}</span>
                </div>
                <div v-else class="selectLabel">
                  <div class="icon">
                              <iconpark-icon name="iconorganize-a961a3m4" class="iconp" />

                  </div>
                  <span class="text"> {{ isCheckedItem.name }}</span>
                </div>

                <div class="close" @click="
                    onRemoveSelectedItem(isCheckedItem, isCheckedItemIndex)
                  ">
                  <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
                    <use href="#close" />
                  </svg> -->
                  <iconpark-icon name="iconerror" style="font-size: 16px"></iconpark-icon>
                </div>
              </span>
            </div>
          </div>
        </div>
      </template>
      <template #closeBtn>
        <!-- <iconpark-icon name="close" size="16"></iconpark-icon> -->
        <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
          <use href="#close" />
        </svg> -->
        <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
      </template>
      <template #footer>
        <div class="footer">
          <t-button theme="default" variant="outline" @click="onClose">
            取消
          </t-button>
          <t-button theme="primary" @click="onSave"> 确定 </t-button>
        </div>
      </template>
    </t-dialog>
  </div>
</template>

<script lang="ts" setup>
  /**
   * @description 组织架构选人
   */
  import { ref, watch, onMounted } from "vue";

  import { MessagePlugin } from "tdesign-vue-next";
  import { memberInfoListSelect } from "@renderer/api/member/api/memberApi";
  import { getNameContent, getResponseResult } from "@renderer/utils/myUtils";
  import { useStashStore } from "@renderer/store/modules/stash";
  import { useI18n } from "vue-i18n";
  import { iconUrl } from "@renderer/plugins/KyyComponents";

  const { t } = useI18n();

  const visible = ref < Boolean > (false);
  const searchKey = ref < string > ("");
  const value = ref < Array < Number >> ([]);
  const options = ref([]);
  // const checkAll = computed(() => !!(options.value.length === value.value.length && options.value.length));
  // const indeterminate = computed(() => !!(options.value.length > value.value.length && value.value.length));
  const checkAll = ref(false);

  const props = defineProps({
    teamId: {
      type: String,
      default: undefined
    },
    // datas: {
    //   type: Array,
    //   default: () => [],
    // },
    radioFlag: {
      // 默認不顯示單選框
      type: Boolean,
      default: false
    },
    chengRange: {
      type: Number,
      default: 1
    },
    isOnly: {
      // 是否只选择一个，默认不限制
      type: Boolean,
      default: false
    },
    isFilter: {
      // 当isOnly为true有效，是否过滤去重人员，返回所选择部门和成员的集合（去重）
      type: Boolean,
      default: false
    },
    // 左边需要过滤的人不显示在列表
    delArryId: {
      type: Array,
      default: () => []
    },
    header: {
      type: String,
      default: "选择直接上级"
    },
    // 已选人员数组id Array<string>
    selected: {
      type: Array,
      default: () => []
    },
    defaultParent: {
      type: Boolean,
      default: false
    }
  });
  // onFilterSelectPersons 该方法返回人员列表，去重处理的
  const emits = defineEmits([
    "onSelectItem",
    "onSelectItems",
    "onClose",
    "changeRadio",
    "onFilterSelectPersons"
  ]);
  const membersDataObj = ref({ children: [] }); // 存储所有数据

  const membersDataQueryList = ref([]); // 只存储所有人员 用于搜索
  const searchStaffs = ref([]); // 通过搜索条件，模糊查询得出结果

  const currentMembersAndDepartments = ref({ children: [], staffs: [] });
  const levelDatas = ref([]); // 存储层级菜单面包屑
  // 将已选中的对象全部加入
  const isCheckedItems = ref([]);

  watch(
    () => currentMembersAndDepartments.value,
    (newisCheckedItems) => {
      // 塞入已选

      isCheckedItems.value.forEach((ele) => {
        if (ele.idStaff) {
          currentMembersAndDepartments.value.staffs.forEach((es) => {
            if (es.idStaff === ele.idStaff) {
              console.log(es, "eseseseseses");
              console.log(newisCheckedItems);
              es.isChecked = true;
            }
          });
        } else {
          currentMembersAndDepartments.value.children.forEach((es) => {
            if (es.id === ele.id) {
              console.log(es, "eseseseseses");
              console.log(newisCheckedItems);
              es.isChecked = true;
            }
          });
        }
      });
    }
  );
  // 初始化数据
  const initData = () => {
    membersDataObj.value = { children: [] };
    membersDataQueryList.value = [];
    searchStaffs.value = [];
    currentMembersAndDepartments.value = { children: [], staffs: [] };
    levelDatas.value = [];
    isCheckedItems.value = [];
  };
  // 塞入数据进入复选框
  const setSelectItem = (val, flag) => {
    console.log(val, "valvalvalval");
    isCheckedItems.value = flag ? val.rangeData : val.member;
    currentMembersAndDepartments.value.children.forEach((ele) => {
      isCheckedItems.value.forEach((es) => {
        if (es.id && ele.id === es.id) {
          ele.isChecked = true;
        }
      });
    });

    currentMembersAndDepartments.value.staffs.forEach((ele) => {
      isCheckedItems.value.forEach((es) => {
        if (es.idStaff && ele.idStaff === es.idStaff) {
          ele.isChecked = true;
        }
      });
    });
  };

  const { departmentsState } = useStashStore();
  // 获取成员列表树状组件
  const getMemberInfoListSelectDatas = async () => {
    let res = null;
    let params = {};
    console.log(props.teamId, " props.teamId  props.teamId ");

    try {
      if (departmentsState && departmentsState.length > 0) {
        // 组织id
        params = {
          teamId: props.teamId,
          defaultParent: props.defaultParent ? 1 : undefined
        };
        // params = { teamId: 484044634096406528, parent: 59 };
      } else {
        // res = await memberInfoListSelect({ parent: 0 });
        // res = getResponseResult(res);
        // if (!res) return;
        params = {
          teamId: props.teamId,
          defaultParent: props.defaultParent ? 1 : undefined
        };
        // params = { teamId: 484044634096406528, parent: 59 };
      }
      console.log(params, props.defaultParent);

      res = await memberInfoListSelect(params);
      res = getResponseResult(res);
      if (!res) return;
    } catch (error) {
      MessagePlugin.error(error.message);
      return;
    }
    console.log(res);

    // 转换数据，加装属性 isChecked
    res.data.name = t('contacts.structure');
    // 设置根节点pid为0
    res.data.departmentId = 0;
    res.data.id = 0;
    switchOrganiseData(res.data, 0);
    console.log(res.data);

    membersDataObj.value = res.data;

    // 存储所有的人
    membersDataQueryList.value = getAllPerson(res.data);
    if (props.selected && props.selected.length) {
      selectedDataHandle(membersDataQueryList.value);
    }

    console.log(membersDataQueryList.value);
    // 赋予当前对象
    currentMembersAndDepartments.value = res.data;
    if (props.delArryId.length > 0 && currentMembersAndDepartments.value.staffs) {
      currentMembersAndDepartments.value.staffs =
        currentMembersAndDepartments.value.staffs.filter(
          (es) => !props.delArryId.includes(es.idStaff)
        );
    }
    // 存储层级菜单面包屑
    levelDatas.value = [membersDataObj.value];
    // 获取staff部分的数据
    let staffArr = [];
    if (res.data.staffs && res.data.staffs.length > 0) {
      staffArr = staffArr.concat(res.data.staffs);
      staffArr = getStaffs(res.data.children, staffArr);
      console.log(staffArr);

      // 去重
      staffArr = arrayNonRepeatfy(staffArr);
      console.log(staffArr);
    }
  };

  /**
   * 已选人员勾选处理,id.toString是为了类型统一为string
   * @param data
   */
  const selectedDataHandle = (data) => {
    const ids = props.selected.map((item) => item.toString());
    for (const merber of data) {
      if (ids.includes(merber.idStaff.toString())) {
        merber.checked = true;
        if (!isCheckedItems.value.length) {
          isCheckedItems.value.push(merber);
        }
        for (const item of isCheckedItems.value) {
          if (item.idStaff !== merber.idStaff) {
            isCheckedItems.value.push(merber);
          }
        }
      }
    }
  };

  // 解剖树状，将staffs的人员全部取出
  const getAllPerson = (obj) => {
    let arr = [];
    if (obj.staffs && obj.staffs.length > 0) {
      arr = arr.concat(obj.staffs);
    }
    if (obj.children && obj.children.length > 0) {
      obj.children.forEach((child) => {
        arr = arr.concat(getAllPerson(child));
      });
    }
    return arr;
  };

  // 监听查询结果
  const onListenSearch = (e) => {
    searchStaffs.value = membersDataQueryList.value.filter((v) =>
      v.name.includes(e));
  };
  const changeRadio = (val) => {
    emits("changeRadio", val);

    console.log(val, "vallllllllllll");
  };
  // 转换数据，加装属性 isChecked, 层级level
  const switchOrganiseData = (obj, level) => {
    obj.level += 1;
    if (obj.staffs && obj.staffs.length > 0) {
      obj.staffs.forEach((staff) => {
        // staff['isChecked'] = false;
        staff = Object.assign(staff, { isChecked: false });
      });
    }
    if (obj.children && obj.children.length > 0) {
      obj.children.forEach((child) => {
        // child['isChecked'] = false;
        child = Object.assign(child, { isChecked: false });

        switchOrganiseData(child, obj.level);
      });
    }
  };

  // 去重人员
  const arrayNonRepeatfy = (staffArr) => {
    const map = new Map();
    staffArr.forEach((element) => {
      if (!map.has(element.idStaff)) {
        map.set(element.idStaff, element);
      }
    });
    return Array.from(map.values());
  };

  // 递归获取所有成员
  const getStaffs = (arr, staffArr) => {
    arr.map((item) => {
      if (item.staffs && item.staffs.length > 0) {
        staffArr = staffArr.concat(item.staffs);
        if (item.children && item.children.length > 0) {
          staffArr = staffArr.concat(getStaffs(item.children, staffArr));
        }
      }
    });
    return staffArr;
  };

  // 设置当前所在的层级对象
  const goOrganiseLevel = (child) => {
    currentMembersAndDepartments.value = child;
    if (props.delArryId.length > 0 && currentMembersAndDepartments.value.staffs) {
      currentMembersAndDepartments.value.staffs =
        currentMembersAndDepartments.value.staffs.filter(
          (es) => !props.delArryId.includes(es.idStaff)
        );
    }
    const index = levelDatas.value.findIndex((e) => e.id === child.id);
    levelDatas.value = levelDatas.value.splice(0, index + 1);
    console.log(
      isCheckedItems.value,
      "isCheckedItemsisCheckedItemsgoOrganiseLevel"
    );
  };

  // 设置当前层级, 只针对部门，如果部门被选中，该方法禁用
  const setCurrentChild = (child) => {
    if (child.isChecked) {
      return;
    }
    currentMembersAndDepartments.value = child;
    if (props.delArryId.length > 0 && currentMembersAndDepartments.value.staffs) {
      currentMembersAndDepartments.value.staffs =
        currentMembersAndDepartments.value.staffs.filter(
          (es) => !props.delArryId.includes(es.idStaff)
        );
    }
    // 设置层级菜单面包屑
    let arr = [];
    arr = getParent(membersDataObj.value.children, child.id);
    arr.unshift(membersDataObj.value);
    console.log(arr);
    levelDatas.value = arr;
  };

  const getParent = (data2, nodeID2) => {
    let arrRes = [];
    if (data2.length === 0) {
      if (nodeID2) {
        arrRes.unshift(data2);
      }
      return arrRes;
    }
    const rev = (data, nodeID) => {
      for (let i = 0, { length } = data; i < length; i++) {
        const node = data[i];
        if (node.id == nodeID) {
          arrRes.unshift(node);
          // 查找到当前id,继续追随父级id
          rev(data2, node.parent); // 注意这里是传入的tree，不要写成data了，不然遍历的时候一直都是node.children,不是从最顶层开始遍历的
          break;
        } else {
          // 如果当前节点没有对应id,则追溯该子类是否有匹配项
          if (node.children && node.children.length > 0) {
            rev(node.children, nodeID);
          }
        }
      }
      return arrRes;
    };
    arrRes = rev(data2, nodeID2);
    return arrRes;
  };

  onMounted(() => {
    value.value = [5];
  });

  // const onChange = (e) => {
  //   console.log(checkAll.value);
  //   console.log(e);
  // };
  // 全选
  const setCheckboxAllChange = (e) => {
    checkAll.value = e;
    if (props.isOnly) {
      if (currentMembersAndDepartments.value.staffs.length > 0) {
        currentMembersAndDepartments.value.staffs.map((v) => {
          v.isChecked = e;
        });
      }
    } else {
      setCheckboxAll(e);
    }
  };

  /**
   * @description 设置全选和取消,
   * @param  bool
   * 需要一个设置值
   */
  const setCheckboxAll = (bool) => {
    // 部门
    currentMembersAndDepartments.value.children.map((ev) => {
      ev.isChecked = bool;
      // 部门
      const index = isCheckedItems.value.findIndex(
        (v) =>
          v.hasOwnProperty("departmentId") && v.departmentId === ev.departmentId
      );
      console.log(index);
      if (index > -1) {
        if (!bool) {
          isCheckedItems.value.splice(index, 1);
        }
      } else if (bool) {
        isCheckedItems.value.push(ev);
      }
    });

    // 人员
    currentMembersAndDepartments.value.staffs.map((ev) => {
      ev.isChecked = bool;

      const index = isCheckedItems.value.findIndex(
        (v) => v.hasOwnProperty("idStaff") && v.idStaff === ev.idStaff
      );
      if (index > -1) {
        if (!bool) {
          isCheckedItems.value.splice(index, 1);
        }
      } else if (bool) {
        isCheckedItems.value.push(ev);
      }
    });
  };

  // 多选的情况下使用
  const isCheckedAllHandle = (bool) => {
    if (bool) {
      let isAll = true;
      if (currentMembersAndDepartments.value.staffs.length > 0) {
        const staffBool = currentMembersAndDepartments.value.staffs.every(
          (v) => v.isChecked === true
        );
        if (!staffBool) {
          isAll = false;
        }
      }

      if (currentMembersAndDepartments.value.children.length > 0) {
        const childBool = currentMembersAndDepartments.value.children.every(
          (v) => v.isChecked === true
        );
        if (!childBool) {
          isAll = false;
        }
      }
      checkAll.value = isAll;
    } else {
      checkAll.value = bool;
    }
  };

  // 选中，和取消
  const setCheckboxChange = (e) => {
    if (props.isOnly && isCheckedItems.value.length > 0 && !e.isChecked) {
      MessagePlugin.error({
        content: "只能选择一个"
      });
      return;
    }

    e.isChecked = !e.isChecked;
    console.log(e.isChecked);
    // 分两种情况，人员、部门
    if (e.hasOwnProperty("idStaff")) {
      const index = isCheckedItems.value.findIndex(
        (v) => v.hasOwnProperty("idStaff") && v.idStaff === e.idStaff
      );
      if (index > -1) {
        if (!e.isChecked) {
          isCheckedItems.value.splice(index, 1);
        }
      } else if (e.isChecked) {
        isCheckedItems.value.push(e);
      }
    } else {
      // 部门
      const index = isCheckedItems.value.findIndex(
        (v: any) =>
          v.hasOwnProperty("departmentId") && v.departmentId === e.departmentId
      );
      console.log(index);
      if (index > -1) {
        if (!e.isChecked) {
          isCheckedItems.value.splice(index, 1);
        }
      } else if (e.isChecked) {
        isCheckedItems.value.push(e);
      }
    }
    console.log(isCheckedItems.value);
    isCheckedAllHandle(e.isChecked);
  };

  // 移除已选中项
  const onRemoveSelectedItem = (e, eindex) => {
    isCheckedItems.value.splice(eindex, 1);
    if (e.idStaff) {
      currentMembersAndDepartments.value.staffs.forEach((ele) => {
        if (ele.idStaff === e.idStaff) {
          ele.isChecked = false;
        }
      });
    } else {
      currentMembersAndDepartments.value.children.forEach((ele) => {
        if (ele.id === e.id) {
          ele.isChecked = false;
        }
      });
    }
  };

  // isCheckedItems // 递归将其全部选择
  const onSelectIsCheckedItem = (dataObj) => { };

  const onSave = () => {
    // 提交
    //  props.radioFlag.value
    //  props.chengRange.value
    if (
      isCheckedItems.value.length < 1 &&
      !props.radioFlag &&
      props.chengRange === 2
    ) {
      MessagePlugin.info({
        content: "请选择"
      });
      return;
    }
    if (props.isOnly) {
      emits("onSelectItem", isCheckedItems.value[0], props.chengRange);
      onClose();
    } else {
      // 这里需要做的工作，isCheckedItems将 部门和人进行整合，去重
      if (
        isCheckedItems.value.length < 1 &&
        !props.radioFlag &&
        props.chengRange === 2
      ) {
        MessagePlugin.info({
          content: "请选择"
        });
        return;
      }
      if (props.isFilter) {
        let staffArr = [];
        isCheckedItems.value.forEach((element) => {
          // eslint-disable-next-line no-prototype-builtins
          if (element.hasOwnProperty("idStaff")) {
            staffArr.push(element);
          } else {
            // 获取staff部分的数据
            staffArr = staffArr.concat(element.staffs);
            staffArr = getStaffs(element.children, staffArr);
          }
        });
        // 去重
        staffArr = arrayNonRepeatfy(staffArr);
        emits("onFilterSelectPersons", staffArr);
      } else {
        emits("onSelectItems", isCheckedItems.value, props.chengRange);
      }
      onClose();
    }
  };

  const onOpen = () => {
    visible.value = true;
    initData();
    getMemberInfoListSelectDatas();
  };
  const onClose = (e?) => {
    console.log('啊啊啊啊啊啊啊啊啊啊啊啊啊');
    searchKey.value = '';
    checkAll.value = false;
    visible.value = false;
    if (e) {
      emits("onClose", true);
    }
  };

  defineExpose({
    onOpen,
    onClose,
    setSelectItem
  });
</script>

<style lang="less" scoped>
  @kyy_color_icon_orange: #eb882b;

  .t-alert--info {
    padding: 8px 16px;
  }

  .t-checkbox-group {
    width: 100%;
  }

  .toBody {
    border: 1px solid #e3e6eb;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    height: 404px;

    &-left {
      flex: 1;

      .input {
        margin: 12px 16px;
        width: auto;
      }

      .bcrumb {
        margin: 0 16px;
        display: flex;
        flex-wrap: wrap;

        &-item {
          display: flex;
          align-items: center;
          max-width: 140px;

          .name {
            font-size: 14px;

            font-weight: 400;
            color: #13161b;
            max-width: 100px;
            cursor: pointer;
            user-select: none;
          }

          .icon {
            margin-left: 2px;
          }

          &:last-child {
            .name {
              color: #a1a2a4;
            }

            .icon {
              display: none;
            }
          }
        }
      }

      .group {
        padding: 0 8px;
        margin-top: 4px;
        display: flex;
        flex-direction: column;
        // justify-content: center;
        width: 100%;

        &-item {
          padding: 4px 8px;
          border-radius: 4px;
          transition: all 0.2s linear;
          display: flex;
          justify-content: space-between;
          user-select: none;
          cursor: pointer;

          &:hover {
            background: #f0f8ff;
          }

          .selectLabel {
            display: flex;
            align-items: center;

            .image {
              width: 24px;
              height: 24px;
              border-radius: 5px;
            }

            .text {
              font-size: 14px;

              font-weight: 400;
              color: #13161b;
              margin-left: 2px;
              width: 200px;
            }
          }

          .tips {
            font-size: 14px;

            font-weight: 400;
            cursor: pointer;
            user-select: none;
            color: #2069e3;
            flex: none;
          }
        }
      }

      .disabled {
        .text {
          color: #a1a2a4 !important;
        }

        .tips {
          color: #a1a2a4 !important;
          cursor: not-allowed;
        }
      }
    }

    &-line {
      background: #e3e6eb;
      border-radius: 8px;
      width: 1px;
      margin: 8px 0;
    }

    &-right {
      flex: 1;

      .behaver {
        font-size: 14px;

        font-weight: 400;
        color: #13161b;
        margin: 16px;
        // background-color: @kyy_color_icon_orange;
      }

      .selectGroup {
        margin: 0 8px;
        margin-top: 4px;

        &-item {
          display: flex;
          align-items: center;
          padding: 4px 8px;
          border-radius: 4px;
          transition: all 0.25s linear;

          justify-content: space-between;

          &:hover {
            background: #f0f8ff;
          }

          .selectLabel {
            display: flex;
            align-items: center;

            .icon {
              background-color: @kyy_color_icon_orange;
              width: 24px;
              height: 24px;
              border-radius: 4px;
              display: flex;
              align-items: center;
              justify-content: center;

              .iconpark {}
            }

            .image {
              width: 24px;
              height: 24px;
              border-radius: 5px;
            }

            .text {
              font-size: 14px;

              font-weight: 400;
              color: #13161b;
              margin-left: 2px;
            }
          }

          .close {
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }

  .radios-group {
    .font-name {
      height: 22px;
      font-size: 14px;

      font-weight: 400;
      color: #13161b;
      line-height: 22px;
      margin-bottom: 12px;
    }
  }

  .mock {
    width: 24px;
    height: 24px;
    background: #4d5eff;
    border-radius: 4px;
    color: #ffffff;
    text-align: center;
    line-height: 24px;
    font-size: 10px;
  }

  .icon {
    background-color: @kyy_color_icon_orange;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color:#fff;

    &-svg {
      color: #fff;
      width: 16px;
      height: 16px;
    }
  }

  // :deep(.t-checkbox__label) {
  // 	display: flex;
  // 	flex: 1;
  // }
</style>
