import {client_orgRequest, iam_srvRequest} from "@/utils/apiRequest";
import LynkerSDK from "@renderer/_jssdk";

interface putAccountModel {
  email?: {
    mail: string,
    code: string,
  }
  mobile?: {
    region: string,
    mobile: string,
    code: string,
  }
}

export function getCfg(typ:string) {
  return iam_srvRequest({
    method: 'get',
    url: `/v1/cfg/${typ}`,
  });
}

export function putCfg(data:{user_id:string, typ:string, attachment: any}) {
  return iam_srvRequest({
    method: 'put',
    url: `/v1/cfg`,
    data
  });
}

export function putAccount(data:putAccountModel) {
  return iam_srvRequest({
    method: 'put',
    url: `/v1/accounts`,
    data
  });
}

export function checkPassword() {
  return iam_srvRequest({
    method: 'get',
    url: `/v1/password/check`,
  });
}

export function deletePassword() {
  return iam_srvRequest({
    method: 'delete',
    url: `/v1/password`,
  });
}

export async function checkVersion(data: {region: string, type: string}) {
  // 手动设置环境时，不检查版本
  const isManualEnv = LynkerSDK.checkIsManualEnv();
  if (isManualEnv) {
    throw new Error('手动设置环境时，不检查版本');
  }
  return client_orgRequest({
    method: 'get',
    url: `/version/recently/${data.region}/${data.type}`,
  });
  // res.data.data.external_version = '4.0.0';
  // res.data.data.internal_version = '4.0.0';
  return res;
}
// export function checkVersion(data: {platform:string,version:string,package?:string}) {
//   return iam_srvRequest({
//     method: 'post',
//     url: `/version`,
//     data
//   });
// }

export function setFeedback(data: {content:string}) {
  return iam_srvRequest({
    method: 'post',
    url: `/v1/feedbacks`,
    data
  });
}

//登录账号查询
export function getMyself() {
  return iam_srvRequest({
    method: 'get',
    url: `/v1/accounts`,
  });
}
