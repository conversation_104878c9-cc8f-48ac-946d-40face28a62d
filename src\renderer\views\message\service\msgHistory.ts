import { GetAdvancedHistoryMsgParams } from "@rk/im-sdk/dist/types/params";
import { getMsgToStore, msgToUseChat, fileReplaceUrl } from './utils';
import { isSignalingMsg } from './msgUtils';
import { getRemoteHistoryMessages, searchLocalMessages, getImWindowActive } from './ipcIMBridge';
import { getMessagesComputed } from './message';
import { logHandler } from "@renderer/log";
import { debounce } from "lodash";
import { getPrivateChatApi } from "@/api/im/api";

import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;

const reLoadMsgWindow = debounce((origin) => {
    // reload messageWindow
    ipcRenderer.invoke('messageWindow-reload');
    logHandler({ name: 'imWindow实列不存在了', info: `error:${JSON.stringify(origin)};}`, desc: `` });
 }, 1000)
/**
 * 加载第一页，合并消息可能不能滚动
 * @param conversation
 * @param params
 * @returns
 */
export const firstLoadOpenIMHistory = async (conversation) => {


  let searchParams = { count: 20, lastMinSeq: 0, startClientMsgID: '', conversationID: conversation.conversationID };
  let needLoad = true;
  let errCode = 0;
  let msgList = [];
  let more = false;
  let wrapperMsgList = [];
  while (needLoad) {
    const { hasMore, data, option, errCode: code } = await loadOpenIMHistory(conversation, searchParams, 'firstLoadOpenIMHistory');
      msgList = msgList.concat(data);
      wrapperMsgList = getMessagesComputed(msgList);
      console.log('====>wrapperMsgList.length', hasMore, wrapperMsgList.length);
      needLoad = (hasMore && wrapperMsgList.length < searchParams.count);
      more = hasMore;
      errCode = code;
      searchParams = option;
  }
  return { hasMore: more, data: msgList, option: searchParams, errCode };
};
/**
 * 拉取openIM历史消息
 */
export const loadOpenIMHistory = async (conversation, params:GetAdvancedHistoryMsgParams, origin:string, isSearch = false) => {
  const result = { hasMore: false, data: [] as MessageToSave[], option: {} as GetAdvancedHistoryMsgParams, errCode: 0 };
  const param = { count: 20, lastMinSeq: 0, startClientMsgID: '', ...params, conversationID: conversation.conversationID, userID:conversation.myOpenImId || '' };
  conversation.conversationType === 3 &&  (param.groupID = conversation.targetId)
  const historyRes = await getRemoteHistoryMessages(param);
  if (historyRes === '请求超时') {
    // 请求超时获取im窗口实列是否还存在
    const imWindow = await  getImWindowActive()
    console.log('====>imWindow', imWindow);
    if (!imWindow?.active) {
      console.error('====>reLoadMsgWindow', );
      reLoadMsgWindow('getRemoteHistoryMessages')
    }

  }
  origin !== 'getSdkLatestMsg' && console.log('=====>loadOpenIMHistory', historyRes, param, origin);
  if (historyRes?.errCode === 0) {
    if (historyRes.messageList?.length > 0) {
      historyRes?.messageList?.forEach((item) => {
        // 需要存储的融云消息类型
        // 'RC:SRSMsg'多端同步已读消息，暂不处理，直接过滤
        // 110自定义消息，101文本消息，2101撤回消息，1501 通知消息 114回复消息

        if ([4].includes(item.sessionType)) {
          return;
        }
        const contentExtra = JSON.parse(item.ex || '{}');
        if (contentExtra?.contentType === 'APP_SQUARE' && contentExtra?.extra) {
          const extra = JSON.parse(contentExtra?.extra || '{}');
          if ([7006, 7014, 7303].includes(extra.scene)) {
            return;
          }
        }
        if (contentExtra?.contentType === 'APP_ACTIVITY' && contentExtra?.extra) {
          const extra = JSON.parse(contentExtra?.extra || '{}');
          if ([20051].includes(extra.scene)) {
            return;
          }
        }
        const searchShowType = [110, 111, 101, 114]
        const nomalShowType = [110, 111, 101, 114, 2101,1501,'RC:RcCmd']
        const isValidType = isSearch ? searchShowType : nomalShowType
        const isValidMsg = isValidType.includes(item.contentType);
        if (!isValidMsg) {
            return;
        }
        const msgItem = msgToUseChat(item, isSearch);
        if (!msgItem) return;
        // 转换消息格式
        let msg = getMsgToStore(msgItem);
        if(!msg) return;
        if (item.attachedInfoElem?.groupHasReadInfo && !isSearch) {
          msg.receipts = item.attachedInfoElem.groupHasReadInfo;
        }
        // 过滤信令消息
        if ([110, 101].includes(item.contentType)) {
            let isValidCustomMsg = isSignalingMsg(msg) === false;
            if (isSearch) isValidCustomMsg = !['server_message_middle', "meeting", "APP_MEETING_INVITE", "meeting_end", "APP_MEETING_MSG"].includes(msg?.contentExtra?.contentType);
            if (isValidCustomMsg) {
              msg = fileReplaceUrl(msg)
              result.data.push(msg);
            }

        } else {
          result.data.push(msg);
        }
      });

      // 返回有数据没有clientMsgID，clientMsgID不存在下次拉取历史会重新拉取获取第二个消息数据，如果再没有那么没有更多了
      const clientMsgID = historyRes.messageList?.[0]?.clientMsgID || historyRes.messageList?.[1]?.clientMsgID;
      result.option = { ...params, startClientMsgID: clientMsgID, lastMinSeq: historyRes.lastMinSeq };

    }
    result.hasMore = !historyRes?.isEnd;

  } else {
    result.hasMore = false;
    result.errCode = historyRes?.errCode;
  }
  origin !== 'getSdkLatestMsg' && console.log('=====>historyMsgToUse', result);
  return result;
};


export const searchMessages = async (params, from = '') => {
 const res = await searchLocalMessages(params);
 console.log('=====>searchMessages', params, res);

 if (res.code !== 0) return {result:[],total:0};
 const result = [];
 if (res.result?.data?.totalCount > 0) {
   const searchResultItems = res.result.data.searchResultItems;
  for (const items of searchResultItems) {
    let relation = ''
    if(from === 'golabel'){
      if(items.conversationType === 1){
       const res = await getPrivateChatApi(items.messageList[0].recvID,items.messageList[0].sendID);
       relation = res.data?.data?.origin
      }
    }
    items.messageList?.forEach((item) => {
      const isValidMsg = [110, 101, 114].includes(item.contentType);
      if (!isValidMsg) {
          return;
      }
      const msgItem = msgToUseChat(item);
      if (!msgItem) return;
      // 转换消息格式
      let msg = getMsgToStore(msgItem, true);
      msg.relation = relation
      // 过滤信令消息
      if ([110, 101].includes(item.contentType)) {
          const isValidCustomMsg = isSignalingMsg(msg) === false;
          if (isValidCustomMsg && msg?.contentExtra?.contentType !== "APP_MEETING_MSG") {
            msg = fileReplaceUrl(msg)
            result.push(msg);
          }

      } else {
        result.push(msg);
      }
    });
  };

 }
 return {
  result,
  pageSize: params.count,
  pageIndex: params.pageIndex,
  latestMsgSendTime: res.result?.data?.searchResultItems?.[0].latestMsgSendTime || null,
  /** SDK有问题，返回分布数据有误，可后期让SDK修复 */
  total: res?.result?.data?.totalCount || 0,
 };
};
