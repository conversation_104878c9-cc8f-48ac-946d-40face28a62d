import { createRouter, createWebHistory } from 'vue-router';
import HomeView from '../views/HomeView.vue';
import NotFound from '../views/NotFound.vue';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/addInvoice',
      name: 'AddInvoice',
      component: () => import('../views/order/addInvoice.vue'),
    },
    {
      path: '/changePay',
      name: 'ChangePay',
      component: () => import('../views/order/changePay.vue'),
    },
    {
      path: '/square/post-detail',
      name: 'PostDetail',
      component: () => import('../views/square/post/PostDetail.vue'),
    },
    {
      path: '/post-detail',
      name: 'PostDetailNew',
      component: () => import('../views/square/post/PostDetail.vue'),
    },
    // {
    //   path: '/square/website',
    //   name: 'Website',
    //   component: () => import('../views/square/website/Frame.vue')
    // },
    {
      path: '/square/website',
      name: 'WebsiteInner',
      component: () => import('../views/square/website/index.vue'),
    },
    {
      path: '/square/square-detail',
      name: 'SquareDetail',
      component: () => import('../views/square/SquareDetail.vue'),
    },
    {
      path: '/demos/upload',
      name: 'UploadDemo',
      component: () => import('../views/demos/Upload.vue'),
    },
    {
      path: '/account/index',
      name: 'accountIndex',
      component: () => import('../views/keeppx/account/index.vue'),
    },
    {
      path: '/account/login',
      name: 'accountLogin',
      component: () => import('../views/keeppx/account/login.vue'),
    },
    {
      path: '/account/register',
      name: 'accountRegister',
      component: () => import('../views/keeppx/account/register.vue'),
    },
    {
      path: '/account/join',
      name: 'accountJoin',
      component: () => import('../views/keeppx/account/join.vue'),
    },
    {
      path: '/account/sms',
      name: 'accountSms',
      component: () => import('../views/keeppx/account/sms.vue'),
    },
    {
      path: '/account/tip',
      name: 'accountTip',
      component: () => import('../views/keeppx/account/tip.vue'),
    },
    {
      path: '/account/expired',
      name: 'accountExpired',
      component: () => import('../views/keeppx/account/expired.vue'),
    },
    {
      path: '/account/jump',
      name: 'accountJump',
      component: () => import('../views/keeppx/account/jump.vue'),
    },
    {
      path: '/download',
      name: 'appDownload',
      component: () => import('../views/keeppx/account/download.vue'),
    },
    {
      path: '/qrcode',
      name: 'qrcode',
      component: () => import('../views/keeppx/qrcode/index.vue'),
    },
    {
      path: '/:pathMatch(.*)',
      name: 'NotFound',
      component: NotFound,
    },
    {
      path: '/common/active',
      name: 'commonActive',
      component: () => import('../views/keeppx/common_active/invite.vue'),
    },

    {
      path: '/member/invite',
      name: 'memberInvite',
      component: () => import('../views/keeppx/member/invite.vue'),
    },
    {
      path: '/member_active/invite',
      name: 'memberActiveInvite',
      component: () => import('../views/keeppx/member_active/invite.vue'),
    },
    {
      path: '/politics/invite',
      name: 'politicsInvite',
      component: () => import('../views/keeppx/politics/invite.vue'),
    },
    {
      path: '/politics_active/invite',
      name: 'politicsActiveInvite',
      component: () => import('../views/keeppx/politics_active/invite.vue'),
    },
    {
      path: '/association/invite',
      name: 'associationInvite',
      component: () => import('../views/keeppx/association/invite.vue'),
    },
    {
      path: '/association_active/invite',
      name: 'associationActiveInvite',
      component: () => import('../views/keeppx/association_active/invite.vue'),
    },
     {
      path: '/uni/invite',
      name: 'uniInvite',
      component: () => import('../views/keeppx/uni/invite.vue'),
    },
    {
      path: '/uni_active/invite',
      name: 'uniActiveInvite',
      component: () => import('../views/keeppx/uni_active/invite.vue'),
    },
    {
      path: '/cbd/invite',
      name: 'cbdInvite',
      component: () => import('../views/keeppx/cbd/invite.vue'),
    },
    {
      path: '/cbd_active/invite',
      name: 'cbdActiveInvite',
      component: () => import('../views/keeppx/cbd_active/invite.vue'),
    },

    {
      path: '/association/invite',
      name: 'associationInvite',
      component: () => import('../views/keeppx/association/invite.vue'),
    },
     {
      path: '/uni/invite',
      name: 'uniInvite',
      component: () => import('../views/keeppx/uni/invite.vue'),
    },

    {
      path: '/visitor/invite',
      name: 'visitorInvite',
      component: () => import('../views/keeppx/visitor/invite.vue'),
    },

    // {
    //   path: '/member_h5/invite',
    //   name: 'memberInviteH5',
    //   component: () => import('../views/keepvw/member/invite.vue')
    // },
    {
      path: '/create/success',
      name: 'createOrgSuccess',
      component: () => import('../views/keeppx/org/success.vue'),
    },
    {
      path: '/create/org',
      name: 'createOrg',
      component: () => import('../views/keeppx/org/createOrg.vue'),
    },
    {
      path: '/org/personal',
      name: 'orgPersonal',
      component: () => import('../views/keeppx/org/orgPersonal.vue'),
    },
    {
      path: '/business-share/detail',
      name: 'businessDetail',
      component: () => import('../views/business/detail.vue'),
    },
    {
      path: '/business-share/productDetail',
      name: 'productDetail',
      component: () => import('../views/business/productDetailH5.vue'),
    },
    {
      path: '/activity/checkIn',
      name: 'activityCheckIn',
      component: () => import('../views/activity/checkNotice.vue'),
    },
    {
      path: '/activity/checkOut',
      name: 'activityCheckOut',
      component: () => import('../views/activity/checkNotice.vue'),
    },
    {
      path: '/service-share/detail',
      name: 'serviceDetail',
      component: () => import('../views/service/detail.vue'),
    },
    {
      path: '/share',
      name: 'share',
      component: () => import('../views/Share.vue'),
    },
    {
      path: '/E_Sign',
      name: 'E_Sign',
      component: () => import('../views/keeppx/account/E_sign.vue'),
    },
    // 笔记分享
    {
      path: '/account/shareNote',
      name: 'AccountShareNote',
      component: () => import('../views/keeppx/account/shareNote.vue'),
    },
    {
      path: '/account/expired404',
      name: 'accountExpired404',
      component: () => import('../views/keeppx/account/expired404.vue'),
    },
    {
      path: '/ebook/browse',
      name: 'ebookBrowse',
      component: () => import('../views/ebook/browse.vue'),
    },
    {
      path: '/ebook/browse-mobile',
      name: 'browseMobile ',
      component: () => import('../views/ebook/browse-mobile.vue'),
    },
    {
      path: '/safeCheck',
      name: 'safeCheck',
      component: () => import('../views/keeppx/account/safeCheck.vue'),
    },
    // 关于我们分享页面
    {
      path: '/aboutUs',
      name: 'AboutUs',
      component: () => import('../views/keeppx/share/AboutUs.vue'),
    },
    {
      path: '/fengcai-share/detail',
      name: 'fengcai',
      component: () => import('../views/fengcai/detail.vue'),
    },
    // 相册
    {
      path: '/album/list',
      name: 'albumList',
      component: () => import('../views/album/list.vue'),
    },
    {
      path: '/partner/order',
      name: 'partnerOrder',
      component: () => import('../views/partner/order.vue'),
    },
    {
      path: '/partner/sale',
      name: 'partnerSale',
      component: () => import('../views/partner/sale.vue'),
    },
    {
      path: '/uni-join',
      name: 'uniJoin',
      component: () => import('../views/uni-join/index.vue'),
    },
    {
      path: '/uni-join/search',
      name: 'uniJoinSearch',
      component: () => import('../views/uni-join/search.vue'),
    },
  ],
});

// 全局钩子
router.beforeEach((to, from, next) => {
  console.log(to, from, 'router beforeEach');
  // 如果返回的是jump页面，并且是app，则跳转回app
  if (to.name === 'accountJump' && from.name && from.name !== 'uniJoin') {
    try {
      window.RingkolApp ? window.RingkolAppChannel?.postMessage(JSON.stringify({ method: 'appRoutePop', to: to.fullPath })) : next();
    } catch (error) {
      console.log(error, 'error');
      next(); 
    }
  } else {
    if(to.query.orgkey === 'uniJoin'&& localStorage.getItem('unifunc')) {
      localStorage.removeItem('unifunc')
      next({ path: '/uni-join'})
    }else{
      next();
    }
  }
});

export default router;
