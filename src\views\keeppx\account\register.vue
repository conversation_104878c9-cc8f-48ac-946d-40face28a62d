<template>
  <div class="container containerPc">
    <div></div>
    <div v-if="showStep === 1">
      <!--        <div class="back pointer" @click="goBack">{{ t('account.back') }}</div>-->
      <!-- <div class="top">
        <div class="back pointer" @click="goBack"></div>
      </div> -->
      <div class="logo">
        <img src="@/assets/img/logo_img_logo.svg" alt="" />
      </div>
      <div class="logo-name" id="logo-name">
        {{ t('account.registerkyy') }}
      </div>
      <!-- <div class="login-tab">
        <div :class="['tab-item', loginType === 1 && 'active']" @click="changeTab(1)">{{ t('account.tel') }}</div>
        <div :class="['tab-item', loginType === 2 && 'active']" style="margin-left: 25px" @click="changeTab(2)">
          {{ t('account.mail') }}
        </div>
      </div> -->
      <div class="info">
        <div v-if="loginType === 1" class="mt12">
          <div class="select-area mb12">
            <t-select v-model="registerParams.region" @change="changeRegisterRegion" class="selectRef selectRefTel">
              <t-option v-for="item in countList" :key="item.code" :value="item.code" :label="item.name"></t-option>
            </t-select>
            <!-- <t-tooltip placement="top" show-arrow>
              <img class="tip-img" src="@/assets/svg/0.icon_tips.svg" alt="">
              <template #content>
                <div>{{ t('zx.account.registerTip') }}</div>
              </template>
            </t-tooltip> -->

            <img @click.stop="openTip" class="tip-img" src="@/assets/svg/0.icon_tips.svg" alt="" />
          </div>
          <t-input v-model="registerParams.title" class="mb12" clearable :placeholder="t('account.inputName')" />
          <!-- <t-input-adornment>
            <template #prepend>
              <div>
                <area-code v-model="registerParams.mobile.region" />
              </div>
            </template>
            <t-input v-model="registerParams.mobile.mobile" :placeholder="t('account.inputTel')" />
          </t-input-adornment> -->
          <div class="adornment tel-code-style">
            <!-- <area-code v-model="registerParams.mobile.region" class="area" :disabled="registerParams.region === 'CN'" />
            <t-input v-model="registerParams.mobile.mobile" clearable class="phone" :placeholder="t('account.inputTel')" /> -->

            <tel-code
              :isMO="registerParams.region === 'MO'"
              :required="true"
              :selectDisabled="registerParams.region === 'CN'"
              :tel-code="registerParams.mobile.region"
              :tel-number="registerParams.mobile.mobile"
              @changeCode="(v) => (registerParams.mobile.region = v)"
              @changeTel="(v) => (registerParams.mobile.mobile = v)"
            />
          </div>
          <t-input
            v-model="registerParams.mobile.code"
            clearable
            class="mt12 codeBox"
            :placeholder="t('account.inputCode')"
          >
            <template #suffix>
              <div style="display: flex; align-items: center">
                <div class="suffix--line"></div>
                <div v-if="countdown <= 0" class="verify pointer" aria-role="button" @click="checkSM">
                  {{ countdown === -2 ? t('account.sendCode') : t('account.resend') }}
                </div>
                <div v-else class="verify">{{ `${countdown}s` }}</div>
              </div>
            </template>
          </t-input>
        </div>
        <div v-else class="mt12">
          <div class="select-area mb12">
            <t-select v-model="registerParams.region" class="selectRef selectRefMail">
              <t-option v-for="item in countList" :key="item.code" :value="item.code" :label="item.name"></t-option>
            </t-select>
            <img @click.stop="openTip" class="tip-img" src="@/assets/svg/0.icon_tips.svg" alt="" />
          </div>
          <t-input v-model="registerParams.title" class="mb12" clearable :placeholder="t('account.inputName')" />
          <t-input v-model="registerParams.email.mail" class="mt12" clearable :placeholder="t('account.inputEamil')" />
          <t-input
            v-model="registerParams.email.code"
            clearable
            class="mt12 codeBox"
            :placeholder="t('account.inputCode')"
          >
            <template #suffix>
              <div style="display: flex; align-items: center">
                <div class="suffix--line"></div>
                <div v-if="countdown <= 0" class="verify pointer" aria-role="button" @click="checkSM">
                  {{ countdown === -2 ? t('account.sendCode') : t('account.resend') }}
                </div>
                <div v-else class="verify">{{ `${countdown}s` }}</div>
              </div>
            </template>
          </t-input>
        </div>
        <t-button
          v-loading="loginLoading"
          class="login-btn"
          :disabled="loginBtnDisabled"
          block
          theme="primary"
          variant="base"
          @touchstart.stop.prevent="registerAction"
          @click="registerAction"
          >{{ loginType === 1 ? t('account.registerLogin') : t('contacts.nextStep') }}</t-button
        >
        <div class="mt12">
          <t-checkbox v-model="agreeProto">
            <span>{{ t('account.read') }}</span
            ><a target="_blank" :href="`${getBaseUrl('website')}/privacy?uuid=PlatformServices`">{{
              t('account.agreement')
            }}</a
            ><a target="_blank" :href="`${getBaseUrl('website')}/privacy?uuid=PrivacyPolicy`">{{
              t('account.privacy')
            }}</a>
          </t-checkbox>
        </div>

        <div class="mt24px goToLogin">
          <div class="bs" @click="goLogin">
            已有另可账号
            <iconpark-icon name="iconarrowright" class="iconarrowright"></iconpark-icon>
          </div>
        </div>
      </div>
    </div>

    <set-phone v-if="showStep === 2" ref="setPhoneDom" @back="showStep = 1" @confirm="getPhone" />
    <set-pw
      v-if="showStep === 3"
      ref="setPwDom"
      :btn-confirm="t('account.registerLogin')"
      @back="showStep = 1"
      @confirm="getPw"
    />
  </div>

  <t-dialog v-model:visible="notifivisible" theme="info" width="100%" :header="t('account.tip')" :close-btn="false">
    <div>
      <span style="color: #717376">{{ t('account.pleaseAgree') }}</span
      ><a target="_blank" :href="`${getBaseUrl('website')}/privacy?uuid=PlatformServices`">{{
        t('account.agreement')
      }}</a
      ><a target="_blank" :href="`${getBaseUrl('website')}/privacy?uuid=PrivacyPolicy`">{{ t('account.privacy') }}</a>
    </div>
    <template #footer>
      <div style="margin-right: 8px">
        <t-button class="btn cancel" variant="outline" theme="default" @click="notifivisible = false">{{
          t('account.cancel')
        }}</t-button>
        <t-button class="btn confirm" theme="primary" variant="base" @click="confirmAgree">{{
          t('account.agree')
        }}</t-button>
      </div>
    </template>
  </t-dialog>

  <tip
    v-model:visible="tipVisible"
    :tip="t('zx.account.userAlreadyExist')"
    :btn-cancel="t('account.cancel')"
    :btn-confirm="t('zx.account.backLogin')"
    @onconfirm="goBack"
  />
  <tip
    v-model:visible="checkPhoneVisible"
    :tip="checkPhoneTip"
    :btn-confirm="t('zx.other.confirmChange')"
    :btn-cancel="t('account.cancel')"
    @onconfirm="changeRegion"
  />
</template>

<script setup lang="ts">
import { getBaseUrl } from '@/api/requestApi';
import tip from './tipCommon.vue';
import { useRoute, useRouter } from 'vue-router';
import { ref, watch, nextTick, onMounted } from 'vue';
import setPhone from './components/setPhone.vue';
import setPw from './components/setPassword.vue';
import { err_reason, checkPhoneAndMatch, handleTrim } from './util';
import { MessagePlugin } from 'tdesign-vue-next';
import { registerAccount, getIdentifyCode, getProfile, accountExist, verifyCode } from '@/api/account/login';
import { countList } from './constant';
import { useI18n } from 'vue-i18n';
import areaCode from '@/components/keeppx/account/AreaCode.vue';
import telCode from '@/components/keeppx/account/TelCode.vue';
import { setAccesstoken, setOpenid, setSMDeviceId } from '@/utils/auth';
const router = useRouter();
import { useAccountStore } from '@/stores/account';
import { encrypt } from '@/utils/myUtils';
import { DialogPlugin } from 'tdesign-vue-next';
import { initSM, dealSmDeviceId, getSMCaptcha, getSMCaptchaResult } from '@/utils/shumei';
const accountStore = useAccountStore();
const route = useRoute();
const redirect = route.query.redirect as string;

const { t } = useI18n();
const agreeProto = ref(false);
const loginBtnDisabled = ref(true);
const registerParams = ref({
  mobile: {
    region: '86',
    mobile: '',
    code: '',
  },
  email: {
    mail: '',
    code: '',
    password: '',
    region: '',
    mobile: '',
    mobile_code: '',
  },
  title: '',
  region: 'CN',
});
const countdown = ref(-2);
const loginLoading = ref(false);
const notifivisible = ref(false);
const loginType = ref(1);
const timer: any = ref(null);
const checkPhoneVisible = ref(false);
const checkPhoneTip = ref('');
const tipVisible = ref(false);
const showStep = ref(1);
let checkRegion: any = 0;
const noMatchCountry = [{ code: '86', area: 'CN' }]; // 不反向匹配的国家，暂时只有中国大陆，预留其他地区

const telCodeToRegion = [
  { code: '86', area: 'CN' },
  { code: '853', area: 'MO' },
];
const changeRegisterRegion = (val) => {
  registerParams.value.mobile.region = telCodeToRegion?.find((item) => item.area === val)?.code || '86';
};

const checkSM = () => {
  if (!SMCaptcha.value) {
    return;
  }
  getSMCaptchaResult(SMCaptcha.value, getCode);
};

const SMCaptcha = ref(null);
onMounted(async () => {
  initSM();
  try {
    SMCaptcha.value = await getSMCaptcha({ width: 300 });
    console.error(SMCaptcha.value);
  } catch (error) {
    console.error(error);
  }
  console.log(route.query, 'route.query');
  if(route.query.redirect?.includes('uniJoin')) {
    localStorage.setItem('unifunc', '1')
  }
});

const goBack = () => {
  // router.push({ name: 'accountLogin' });
  router.go(-1);
};
const goLogin = () => {
  router.push({ name: 'accountLogin', query: { redirect } });
}
const confirmAgree = () => {
  agreeProto.value = true;
  notifivisible.value = false;
  // registerAcc();
  registerAction();
};

const changeTab = (type: number) => {
  loginType.value = type;
  countdown.value = -2;
  checkLoginDisabled();
  timer.value && (clearInterval(timer.value), (timer.value = null));
  onSelectTypeChange();
};
const changeRegion = () => {
  checkPhoneVisible.value = false;
  registerParams.value.mobile.region = checkRegion.toString();
  registerParams.value.mobile.code ? registerAcc() : getCode();
};
const registerAction = () => {
  if (!loginBtnDisabled.value) {
    loginType.value === 1 ? registerAcc() : next();
  }
};
const next = async () => {
  // 校验邮箱合法性
  const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
  const isEmail = reg.test(registerParams.value.email.mail);
  if (!isEmail) {
    const offset = document.documentElement.scrollTop || document.body.scrollTop;
    MessagePlugin.error({
      content: t('zx.account.inputCorrectEmail'),
      duration: 3000,
      offset: ['0', offset > 50 ? offset : 50],
    });
    return;
  }
  if (!agreeProto.value) {
    notifivisible.value = true;
    return;
  }
  // 检查邮箱是否注册过
  const res = await accountExist({ acc: registerParams.value.email.mail });
  if (res.done) {
    tipVisible.value = true;
    return;
  }
  await verifyCode({ account: registerParams.value.email.mail, code: registerParams.value.email.code })
    .then((re) => {
      registerParams.value.region === 'CN' ? (showStep.value = 2) : (showStep.value = 3);
    })
    .catch((err) => {
      const offset = document.documentElement.scrollTop || document.body.scrollTop;
      MessagePlugin.error({
        content: err?.response?.data?.message || '验证码校验失败',
        duration: 3000,
        offset: ['0', offset > 50 ? offset : 50],
      });
    });
};
const getPhone = (v: any) => {
  registerParams.value.email.region = v.region;
  registerParams.value.email.mobile = v.mobile;
  registerParams.value.email.mobile_code = v.code;
  registerAcc();
};

const getPw = (v: any) => {
  registerParams.value.email.password = encrypt(v.password);
  registerAcc();
};

const registerAcc = () => {
  registerParams.value

  dealSmDeviceId(async (deviceId) => {
    console.log('回调执行成功，设备标识为：' + deviceId);
    setSMDeviceId(deviceId);
    if (!agreeProto.value) {
      notifivisible.value = true;
      return;
    }
    if (!/^(?!\s*$).+/.test(registerParams.value.title)) {
      const offset = document.documentElement.scrollTop || document.body.scrollTop;
      MessagePlugin.error({
        content: t('zx.account.userNameIllegal'),
        duration: 3000,
        offset: ['0', offset > 50 ? offset : 50],
      });
      return;
    }
    registerParams.value.mobile = handleTrim(registerParams.value.mobile);
    registerParams.value.email = handleTrim(registerParams.value.email);
    // 校验邮箱合法性
    if (loginType.value === 2) {
      const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
      const isEmail = reg.test(registerParams.value.email.mail);
      if (!isEmail) {
        const offset = document.documentElement.scrollTop || document.body.scrollTop;
        MessagePlugin.error({
          content: t('zx.account.inputCorrectEmail'),
          duration: 3000,
          offset: ['0', offset > 50 ? offset : 50],
        });
        return;
      }
    } else {
      if (!checkPhone()) return;
    }
    loginLoading.value = true;
    loginBtnDisabled.value = true;
    const params =
      loginType.value === 1
        ? {
            title: registerParams.value.title,
            mobile: registerParams.value.mobile,
            region: registerParams.value.region,
          }
        : {
            title: registerParams.value.title,
            email: registerParams.value.email,
            region: registerParams.value.region,
          };
    const extraParams = {
      app: 'RINGKOL',
      from: 'H5',
      platform: 'H5',
    };
    // const data={...params, ...extraParams}
    // console.log(data,encrypt(data?.email?.password),'data?.email?.password');

    registerAccount({ ...params, ...extraParams })
      .then((res: any) => {
        console.log(res, 'registerAccount');
        if (res.openid) {
          setAccesstoken(res.jwt);
          setOpenid(res.openid);
          getUserInfo();
        }
      })
      .catch((err) => {
        const reason = err.response.data.reason;
        const offset = document.documentElement.scrollTop || document.body.scrollTop;
        MessagePlugin.error({
          content: err_reason[reason] || err.response.data?.message || '注册失败',
          duration: 3000,
          offset: ['0', offset > 50 ? offset : 50],
        });
        loginLoading.value = false;
        loginBtnDisabled.value = false;
      });
  }).catch((e) => {
    console.error(e);
    MessagePlugin.error('网络繁忙，请稍后刷新重试');
  });
};

const getUserInfo = () => {
  getProfile()
    .then((res: any) => {
      accountStore.setUserInfo(res);
      localStorage.setItem('profile', JSON.stringify(res));
      localStorage.setItem('userInfo', JSON.stringify(res));
      localStorage.removeItem('unifunc');
      if (redirect) {
        const redirectUrl = redirect ? decodeURIComponent(redirect) : '';
        router.push(redirectUrl);
      } else {
        router.push({ name: 'accountJoin' });
      }
    })
    .finally(() => {
      loginLoading.value = false;
      loginBtnDisabled.value = false;
    });
};

const checkPhone = () => {
  const noMatch = !~noMatchCountry.findIndex((v) => v.area === registerParams.value.region);
  checkRegion = checkPhoneAndMatch(
    Number(registerParams.value.mobile.region),
    registerParams.value.mobile.mobile,
    noMatch,
  );
  if (!checkRegion) {
    const offset = document.documentElement.scrollTop || document.body.scrollTop;
    MessagePlugin.error({
      content: t('zx.account.phoneIllegal'),
      duration: 3000,
      offset: ['0', offset > 50 ? offset : 50],
    });
    return false;
  }
  if (noMatch && checkRegion !== Number(registerParams.value.mobile.region)) {
    (checkPhoneTip.value = `${t('zx.account.checkPhoneRegion1')}“+${checkRegion}”，${t(
      'zx.account.checkPhoneRegion2',
    )}`),
      (checkPhoneVisible.value = true);
    return false;
  }
  return true;
};

const getCode = (data?) => {
  const mobileParams = handleTrim(registerParams.value.mobile);
  // 校验邮箱合法性
  if (loginType.value === 2) {
    const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
    const isEmail = reg.test(registerParams.value.email.mail);
    if (!isEmail) {
      const offset = document.documentElement.scrollTop || document.body.scrollTop;
      MessagePlugin.error({
        content: t('zx.account.inputCorrectEmail'),
        duration: 3000,
        offset: ['0', offset > 50 ? offset : 50],
      });
      return;
    }
  } else {
    if (!checkPhone()) return;
  }
  // 区分手机和邮箱获取验证码
  const params =
    loginType.value === 1
      ? {
          typ: 'REGISTER',
          mobile: {
            mobile: mobileParams.mobile,
            region: mobileParams.region,
          },
        }
      : {
          typ: 'REGISTER',
          email: {
            mail: registerParams.value.email.mail,
          },
        };
  if (data) {
    params.captcha = {
      code: data?.rid,
      mode: 'slide',
    };
  }
  getIdentifyCode(params)
    .then((res: any) => {
      countdown.value = 60;
      timer.value = setInterval(() => {
        countdown.value--;
        if (countdown.value === 0) {
          clearInterval(timer.value);
          timer.value = null;
        }
      }, 1000);
    })
    .catch((err) => {
      const reason = err.response.data.reason;
      const offset = document.documentElement.scrollTop || document.body.scrollTop;
      MessagePlugin.error({
        content: err_reason[reason] || '获取验证码失败',
        duration: 3000,
        offset: ['0', offset > 50 ? offset : 50],
      });
    });
};
const checkLoginDisabled = () => {
  if (
    loginType.value === 1 &&
    registerParams.value.title &&
    registerParams.value.mobile.mobile &&
    registerParams.value.mobile.code
  ) {
    loginBtnDisabled.value = false;
  } else if (
    loginType.value === 2 &&
    registerParams.value.title &&
    registerParams.value.email.mail &&
    registerParams.value.email.code
  ) {
    loginBtnDisabled.value = false;
  } else {
    loginBtnDisabled.value = true;
  }
};
const checkAreaRegion = () => {
  const match = noMatchCountry.find((v) => v.area === registerParams.value.region);
  match && (registerParams.value.mobile.region = match.code);
};

watch(
  registerParams,
  () => {
    checkLoginDisabled();
  },
  {
    deep: true,
  },
);
watch(
  () => registerParams.value.region,
  (newValue) => {
    countdown.value = -2;
    checkAreaRegion();
  },
);

// ios autofill问题 设置input type为button
const onSelectTypeChange = () => {
  nextTick(() => {
    const elTel = document.querySelector('.selectRefTel input');
    const elMail = document.querySelector('.selectRefMail input');
    elTel && (elTel.type = 'button');
    elMail && (elMail.type = 'button');
  });
};

const openTip = () => {
  const confirmDia = DialogPlugin.alert({
    header: t('zx.account.registerTipTitle'),
    theme: '',
    body: t('zx.account.registerTipContent'),
    className: 'alertBox',
    attach: '.container',
    placement: 'center',
    closeBtn: null,
    confirmBtn: t('contacts.iknow'),
    onConfirm: async () => {
      confirmDia.hide();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

onMounted(() => {
  onSelectTypeChange();
});
</script>

<style lang="less" scoped>
@import url('../css/base.less');

.goToLogin {
  display: flex;
  justify-content: center;
  .bs {
    height: 32px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    border-radius: var(--radius-kyy_radius_button_s, 4px);
    border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);

    .iconarrowright {
      color: #828DA5;
      font-size: 20px;
    }
  }
}

:deep(.t-input .t-input__inner) {
  font-size: 18px;
}
:deep(.t-input) {
  box-shadow: none;
}
:deep(.alertBox) {
  .t-dialog {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 280px;
    padding: 24px 16px;
    border-radius: 16px;
    background: var(--kyy_color_modal_bg, #fff);
  }
  .t-dialog__header-content {
    color: #1a2139;
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;

    /* kyy_fontSize_3/bold */
    font-family: PingFang SC;
    font-size: 17px;
    font-style: normal;
    font-weight: 600;
    line-height: 26px; /* 152.941% */
  }
  .t-dialog__body__icon {
    color: var(--kyy_color_modal_content, #516082);
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .t-dialog__footer {
    text-align: center;
    .t-button {
      width: 100%;
      display: flex;
      min-width: 80px;
      padding: 7px 24px;
      flex-direction: column;
      align-items: center;
      gap: 10px;
      flex: 1 0 0;
      border-radius: 4px;
      background: var(--color-button-primary-kyy-color-button-primary-bg-default, #4d5eff);
      color: var(--color-button-primary-kyy-color-button-primary-text, #fff);
      text-align: center;
      font-feature-settings: 'clig' off, 'liga' off;

      /* kyy_fontSize_3/bold */
      font-family: PingFang SC;
      font-size: 17px;
      font-style: normal;
      font-weight: 600;
      line-height: 26px; /* 152.941% */
    }
  }
}

:deep(.selectRef input) {
  text-align: left;
}
:deep(.tel-code-style) {
  & > div {
    width: 100%;
    border-bottom: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
  }
  .kyy-cell {
    width: 108px !important;
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 8px;
    margin-bottom: 0;
    font-size: 18px;
    .van-field__control {
      height: 40px;
    }
  }
  .van-bottom {
    width: calc(100% - 108px) !important;
    padding-top: 0;
    padding-bottom: 0;
    margin-bottom: 0;
    .van-field__control {
      height: 40px;
    }
  }
  .line {
    height: 24px;
  }
}

.container {
  // max-width: 600px;
  // min-height: 100vh;
  // margin: auto;
  // // position: absolute;
  // // top: 0;
  // // bottom: 0;
  // // left: 50%;
  // // transform: translateX(-50%);
  // background-color: #fff;
  overflow: hidden;

  :deep(.codeBox .t-input__clear) {
    position: absolute;
    right: 120px;
  }
}
.mt12 {
  margin-top: 12px;
}
.mb12 {
  margin-bottom: 12px;
}
.mt16 {
  margin-top: 16px;
}
.pointer {
  cursor: pointer;
}
.top {
  width: 320px;
  margin: 41px auto 0;
  padding-left: 15px;
  padding-bottom: 10px;
}

.back {
  font-size: 14px;
  font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
  color: var(--text-kyy-color-text-3, #828da5);
  line-height: 22px;
  position: relative;
  &:before {
    content: '';
    width: 8px;
    height: 8px;
    border-top: 2px solid #a1a2a4;
    border-left: 2px solid #a1a2a4;
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%) rotate(-45deg);
  }
}
.logo {
  margin-top: 25px;
  text-align: center;
  img {
    width: 64px;
    height: 64px;
    vertical-align: bottom;
  }
}
.logo-name {
  text-align: center;
  font-size: 18px;
  font-family: MicrosoftYaHei, MicrosoftYaHei-Bold;
  font-weight: 700;
  color: var(--text-kyy-color-text-1, #1a2139);
  line-height: 26px;
  margin-top: 12px;
  margin-bottom: 44px;
}
.suffix--line {
  width: 1px;
  height: 24px;
  background-color: #f6f6f6;
  margin-right: 16px;
}
.verify {
  font-size: 14px;
  font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
  color: #4d5eff;
  line-height: 22px;
}
.info {
  width: 320px;
  margin: auto;
  .adornment {
    display: flex;
    .area {
      // min-width: 78px;
      width: 80px !important;
      margin-right: -1px;
      display: flex;
      :deep(.t-input) {
        border-bottom-right-radius: 0;
        border-top-right-radius: 0;
      }
    }
    .phone {
      width: calc(100% - 80px) !important;
      :deep(.t-input) {
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
      }
    }
  }
  :deep(.t-input) {
    height: 40px;
  }
  :deep(.t-input-adornment__prepend) {
    background: #fff;
  }
  .login-btn {
    margin-top: 16px;
    height: 40px;
    font-size: 16px;
    font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
    color: #ffffff;
    line-height: 24px;
    border-radius: 4px;
  }
}
.dialog-body {
  margin: 10px 0;
  .input-title {
    font-size: 14px;
    font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
    color: var(--text-kyy-color-text-1, #1a2139);
    line-height: 22px;
    margin-bottom: 6px;
  }
  .btn {
    width: 60px;
    height: 32px;
    font-size: 14px;
    font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
    line-height: 22px;
  }
  .cancel {
    color: var(--text-kyy-color-text-1, #1a2139);
  }
  .confirm {
    color: #ffffff;
  }
}

.login-tab {
  width: 320px;
  margin: 25px auto 0;
  display: flex;
  justify-content: felx-start;
  // margin-left: 80px;
  font-size: 17px;
  font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
  font-weight: normal;
  color: var(--text-kyy-color-text-1, #1a2139);
  line-height: 22px;
  .tab-item {
    cursor: pointer;
    margin-bottom: 9px !important;
  }
  .active {
    color: var(--brand-kyy-color-brand-default, #4d5eff);

    /* kyy_fontSize_3/bold */
    font-family: PingFang SC;
    font-size: 17px;
    font-style: normal;
    font-weight: 600;

    overflow: visible;
    position: relative;
    &:before {
      height: 2px;
      width: 16px;
      content: '';
      background: #4d5eff;
      position: absolute;
      border-radius: 2px;
      left: 50%;
      bottom: -9px;
      transform: translateX(-50%);
    }
  }
}
.select-area {
  position: relative;
  .tip-img {
    width: 20px;
    height: 20px;
    position: absolute;
    right: 38px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1 !important;
  }
}
a {
  color: #4d5eff;
}

:deep(.t-input) {
  border: none;
  border-radius: 0;
  border-bottom: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
  .t-input__inner {
    color: var(--input-kyy-color-input-text-completed, #1a2139);
  }
}
:deep(.t-is-disabled) {
  background-color: transparent;
  border-color: var(--divider-kyy-color-divider-light, #eceff5) !important;
}
:deep(.t-checkbox__input) {
  border-radius: 50%;
  margin: 8px;
}
:deep(.t-checkbox__label) {
  margin-left: 0;
}
</style>
