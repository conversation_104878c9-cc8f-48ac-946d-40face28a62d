// 引入组件库的少量全局样式变量
// import TDesignMobile from 'tdesign-mobile-vue';
// import 'tdesign-mobile-vue/es/style/index.css';
// import fastClick from 'fastclick'

import TDesign from 'tdesign-vue-next';
import 'tdesign-vue-next/es/style/index.css';
import Vue3BaiduMapGL from 'vue3-baidu-map-gl'
// 移动端

import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { createPersistedState } from 'pinia-plugin-persistedstate';
import { preloadStorage } from './utils/preload';

import App from './App.vue';
import router from './router';
import lazyPlugin from 'vue3-lazy';
import 'uno.css';
import { Tab, Tabs, Dialog, Toast,RadioGroup, Radio } from 'vant';
import 'vant/lib/index.css';
import './style/index.less';
import { i18n } from './i18n';
// import VueAMap, { initAMapApiLoader } from '@vuemap/vue-amap';
// 引入vue-amap
// import '@vuemap/vue-amap/dist/style.css';

import filters from './utils/filters';
import { isMobile } from './utils/myUtils';
import loadingImg from '@/assets/loading.png';
import errorImg from '@/assets/img/Rectangle.png';
import LkEditor from '@rk/editor';
import '@rk/editor/index.css';
import iconspng from '@/assets/icon_check_one.png';

// fastClick(window.document.body)
// fastClick.attach(window.document.body)
// 初始化vue-amap
// initAMapApiLoader({
//   // 高德的key
//   key: '667e6d41ce220bb7239b8623058f723c',
//   securityJsCode: 'aa5fc425c15dcce14f033523f55ca0c9', // 新版key需要配合安全密钥使用
//   // Loca:{
//   //  version: '2.0.0'
//   // } // 如果需要使用loca组件库，需要加载Loca
// });

const app = createApp(App);

const store = createPinia();

// 固化信息
store.use(createPersistedState());
console.log(isMobile);
app.use(TDesign);
app.use(Vue3BaiduMapGL, {
  ak: 'CfbnYiaUGtwLLNNDFxNVJTQWOPtMO68u',
})
if (isMobile) {
  // import('tdesign-mobile-vue/es/style/index.css');
  // const TDesignMobile = await import('tdesign-mobile-vue')
  // app.use(TDesignMobile);
  // import('tdesign-vue-next/es/style/index.css');
  // const TDesign = await import('tdesign-vue-next')
  // app.use(TDesign);
  // const TDesign = await import('tdesign-vue-next')
  // import('tdesign-vue-next/es/style/index.css');
  // app.use(TDesign);
} else {
  // const TDesign = await import('tdesign-vue-next')
  // import('tdesign-vue-next/es/style/index.css');
  // app.use(TDesign);
}

app.use(router);

app.use(store);
// app.mixin({
//   mounted() {
//     const eleArr = document.querySelectorAll('.van-cascader__selected-icon');
//     // eleArr.forEach((iconElement) => {
//     // const svgElement = iconElement.querySelector("i");
//     // console.log(svgElement,'svgElementsvgElement');
//     console.log(eleArr, 'eleArreleArreleArr');
//     if (eleArr[0]) {
//       const newImgElement = document.createElement('img');
//       newImgElement.src = iconspng;
//       newImgElement.style.width = '24px';
//       newImgElement.style.height = '24px';
//       // eleArr[0].innerHTML = newImgElement;
//       eleArr[0].parentNode.replaceChild(newImgElement, eleArr[0]);
//       // eleArr[0].appendChild = newImgElement;
//     }
//     // if (svgElement) {
//     //   const str = svgElement.getAttribute("class");
//     //   if (str.includes("van-icon-success")) {

//     //     iconElement.innerHTML = "";
//     //     newImgElement.src = iconspng;

//     //     iconElement.appendChild(newImgElement);
//     //   }
//     // }
//     // });
//   },
// });
app.use(lazyPlugin, {
  loading: loadingImg, // 图片加载时默认图片
  error: errorImg, // 图片加载失败时默认图片
});
app.use(i18n);
// app.use(VueAMap);
app.use(LkEditor);
app.use(Tab);
app.use(Tabs);
app.use(Dialog);
app.use(Toast);
app.use(Radio);
app.use(RadioGroup);
preloadStorage().then(() => {
  console.log('注入成功');
  filters(app);
  app.mount('#app');
}).catch(() => {


  console.log('注入失败');
  filters(app);
  app.mount('#app');

});
// preloadStorage();



// import './utils/preload';
