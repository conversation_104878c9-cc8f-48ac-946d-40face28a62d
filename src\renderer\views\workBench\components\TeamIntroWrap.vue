<script lang="ts" setup>
import { ref } from "vue";
import { useRoute } from "vue-router";
import to from "await-to-js";
import { useI18n } from "vue-i18n";
import { MessagePlugin } from "tdesign-vue-next";
import NoData from "@renderer/views/workBench/components/noData.vue";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import { AxiosResponse, AxiosError } from "axios";
import { shareLinkSplicing } from "@renderer/utils/aboutUs";
import { getIntroduceDetailById, getFrontIntroduceDetailById, getShearId } from "@/api/workBench/introduce";
import OrganizationalInFoDetail from "@/views/workBench/components/organizationalInFoDetail.vue";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useSquareStore } from "@renderer/views/square/store/square";

// 组织介绍详情的包装类，用于动态详情卡片
const props = defineProps<{
  id?: string;
  teamId?: string;
  shareToken?: string;
  channelType?: string;
  showHeader?: Boolean;
}>();
const emit = defineEmits(["share-ready"]);

const route = useRoute();
const { t } = useI18n();

const data = ref();
const shareData = ref({});
const teamInfo = ref<any>({});
const loading = ref(false);
const isDelete = ref(false);

const getDetailData = async () => {
  const id = props.id || route.query.id;
  if (!id) return;
  // getFrontIntroduceDetailById
  loading.value = true;
  console.log(props,'啊实打实大师大asdaaa');
  const [err, res] = await to<AxiosResponse, AxiosError<{ code: number }>>(
    getShearId(
      {
        share_token: props.shareToken,
        channel_type: props.channelType,
      },
      props.teamId || localStorage.getItem("honorteamid"),
    ),
  );
  loading.value = false;

  if (err) {
    console.log(err,'asdasdasd');
    if (err?.response?.data?.code === -1) {
      isDelete.value = true;
      MessagePlugin.warning(t("banch.contentIsDeleted"));
      return;
    }
    if (err) {
      isDelete.value = true;
      MessagePlugin.warning(err?.response?.data?.message);
      return;
    }
    return;

  }
  data.value = res.data.data;
  data.value.isRepublish = true;
  const copyUrl = shareLinkSplicing({
    type: "organize",
    share_token: data.value?.share_token,
    teamid: props.teamId,
    channel_type: String(pageType.value),
  });
  emit("share-ready", copyUrl);
};

// Copy from src/renderer/views/workBench/aboutour/index.vue
const pageType = ref(1);
const getTeamInfo = () => {
  if (route.path.includes("/digitalPlatformIndex")) {
    const digitalPlatformStore = useDigitalPlatformStore();
    pageType.value = 3;
    teamInfo.value = digitalPlatformStore.activeAccount;
  } else if (route.path.includes("/workBenchIndex")) {
    const _teamInfo = localStorage.getItem("honorteam");
    pageType.value = 2;
    teamInfo.value = JSON.parse(_teamInfo);
  } else if (route.path.includes("/square")) {
    const store = useSquareStore();
    teamInfo.value = {
      logo: store.squareSelected.square.avatar,
      name: store.squareSelected.square.name,
      teamId: store.squareSelected.square.squareId,
    };
    pageType.value = 1;
    teamInfo.value = store.squareSelected.square;
  }
};

onMountedOrActivated(() => {
  getTeamInfo();
  getDetailData();
});
</script>

<template>
  <div class="detail-page">
    <div v-if="!loading" class="detail-page-wrap">
      <NoData v-if="isDelete" class="no-data" :text="$t('banch.contentIsDeleted')" />
      <OrganizationalInFoDetail
        :data="data"
        :share-data="{
          share_token:shareToken
        }"
        :is-request-data="!loading"
        :show-share="false"
        :show-header="true"
      />
    </div>
    <t-loading v-else />
  </div>
</template>
<style scoped>
.detail-page {
  height: 100%;
}
.detail-page-wrap {
  height: 100%;
}
</style>
