# 百度地图

主要使用 [百度地图 GL版 Vue3 组件库](https://yue1123.github.io/vue3-baidu-map-gl/zh-CN/guide/introduction) 接入百度地图

> 使用到的百度地图 API：
> [JavaScript API GL](https://lbsyun.baidu.com/index.php?title=jspopularGL/guide/geoloaction)
> [地点输入提示 - Web服务API](https://lbsyun.baidu.com/faq/api?title=webapi/place-suggestion-api)
> [静态图-高清地图 - Web服务API](https://lbsyun.baidu.com/faq/api?title=static/heightStatic)
> [地图调起API - Web服务API](https://lbsyun.baidu.com/faq/api?title=webapi/uri/web)

## 使用百度地图

### 显示地图

使用 `vue3-baidu-map-gl` 的 `BMap` 、`BMarker` 等组件，示例：

```vue
<div class="map-container">
  <BMap
    :api-url="BAIDU_API_URL"
    :center="center"
    :zoom="zoom"
    :height="214"
    enable-scroll-wheel-zoom
    v-bind="mapOptions"
    @initd="mapInit"
    @click="mapClick"
  >
    <BZoom />
    <BMarker
      v-if="markerPoint"
      :position="markerPoint"
      :enable-clicking="false"
      :offset="{ x: -14, y: -48 }"
      :icon="markerIcon"
    />
  </BMap>
</div>
```

> **注意：** `<BMap>` 组件中 `apiUrl` 默认值为 `//` 打头，导致 electron 打包后默认赋值协议为 `app://`，导致 SDK 资源无法访问，所以需手动设置 apiUrl 属性。

### 地图选择器弹窗组件

使用 `BaiduMapSelector.vue` ，示例：

```vue
<BaiduMapSelector
  v-if="mapVisible"
  v-model:visible="mapVisible" 
  :loc="location"
  only-show
  @confirm="locationConfirm"
/>
```

### 地图显示卡片

使用 `BaiduMapCard.vue` ，示例：

```html
<BaiduMapCard
  mini
  can-update
  :location="location"
  @confirm="mapConfirm"
/>
```

### IP 定位到当前城市

```ts
import { useBaiduMap, BMap, mapOptions } from "@renderer/components/common/map/hooks";

// 地图 IP 定位
const { location, markerInfo, onMapInit } = useBaiduMap({ 
  onIPLocation() {
    console.log(location.value);
  },
  onPointGeocoder() {
    console.log(markerInfo.value);
  },
})
```

> **注意:** 需要模板中声明 `BMap` 组件并设置高度为 0 不可见，`useIpLocation`、`usePointGeocoder` 等 hooks 依赖于 `BMapGL`，所以需要在 Map 组件初始化完毕后调用。

```vue
<BMap :api-url="BAIDU_API_URL" :height="0" v-bind="mapOptions" @initd="onMapInit" />
```

如果需要在指定流程中触发获取 IP 定位，可声明一个标识符，在 `onIPLocation` 或 `onPointGeocoder` 回调中判断标识符以执行特定操作。

```ts
// 地图 IP 定位
const { location, markerInfo, onMapInit, getIPLocation, getPointGeocoder } = useBaiduMap({ 
  onIPLocation() {
    if (refreshFlag.value) {
      getPointGeocoder(location.value.point);
    }
  },
  onPointGeocoder() {
    if (refreshFlag.value) {
      refreshPosition();
    }
  },
});

const refreshFlag = ref(false);
const refreshPosition = () => {
  // do something
  // ... ...

  // 别忘了重置标识符
  refreshFlag.value = false;
}
```

## 从高德地图迁移

- 移除 main.ts 中的 @vuemap/vue-amap 引入和注册
- 搜索 `<MapSelector`, 替换为 `<BaiduMapSelector` 相关逻辑
- 搜索 `AMap.plugin`, 替换为百度的 SDK 实现
- 搜索 `<el-amap` , 替换为 `<BMap` 百度地图相关逻辑
- 替换跳转地图指定点查看链接, `https://uri.amap.com/marker` 替换为 `http://api.map.baidu.com/marker`

### 业务功能涉及点

- 广场动态发布弹窗
- 广场的发布地址显示及跳转
- 广场组织介绍详情弹窗
- 广场附近页
- 组织广场信息页
- 广场右侧广场号模块
- 活动编辑
- 拾光相册照片内容编辑
- 大市场
- 数字平台市场
- IM 天气设置 / 极速模式弹窗中查看地图
- 商机创建/编辑页
